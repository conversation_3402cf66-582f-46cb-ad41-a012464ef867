#!/bin/bash

# Test script for Payroll API Integration with Go Backend
# This script tests the integration between Next.js API routes and Go backend

echo "🧪 Testing Payroll API Integration with Go Backend"
echo "=================================================="

# Configuration
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:8050"
API_BASE="$FRONTEND_URL/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "ERROR") echo -e "${RED}❌ $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "INFO") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local expected_status=${4:-200}
    
    echo -e "\n${BLUE}Testing: $description${NC}"
    echo "Method: $method | Endpoint: $endpoint"
    
    response=$(curl -s -w "\n%{http_code}" -X "$method" "$endpoint" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json")
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq "$expected_status" ]; then
        print_status "SUCCESS" "HTTP $http_code - $description"
        if [ "$http_code" -eq 200 ] && [ -n "$body" ]; then
            echo "Response preview: $(echo "$body" | head -c 100)..."
        fi
    else
        print_status "ERROR" "HTTP $http_code - Expected $expected_status for $description"
        echo "Response: $body"
    fi
}

# Check if servers are running
echo -e "\n${BLUE}🔍 Checking Server Status${NC}"
echo "================================"

# Check Go Backend
if curl -s "$BACKEND_URL/api/health" > /dev/null 2>&1; then
    print_status "SUCCESS" "Go Backend is running on $BACKEND_URL"
else
    print_status "ERROR" "Go Backend is not accessible on $BACKEND_URL"
    echo "Please start the Go backend server first:"
    echo "cd go-backend && go run main.go"
    exit 1
fi

# Check Next.js Frontend
if curl -s "$FRONTEND_URL" > /dev/null 2>&1; then
    print_status "SUCCESS" "Next.js Frontend is running on $FRONTEND_URL"
else
    print_status "ERROR" "Next.js Frontend is not accessible on $FRONTEND_URL"
    echo "Please start the Next.js development server first:"
    echo "npm run dev"
    exit 1
fi

# Test API Routes (without authentication - will return 401)
echo -e "\n${BLUE}🔗 Testing API Route Accessibility${NC}"
echo "======================================="

# Test payroll runs endpoints
test_endpoint "GET" "$API_BASE/payroll/runs" "Payroll Runs List" 401
test_endpoint "POST" "$API_BASE/payroll/runs" "Create Payroll Run" 401
test_endpoint "GET" "$API_BASE/payroll/runs/test-id" "Get Payroll Run by ID" 401
test_endpoint "PUT" "$API_BASE/payroll/runs/test-id" "Update Payroll Run" 401
test_endpoint "DELETE" "$API_BASE/payroll/runs/test-id" "Delete Payroll Run" 401

# Test payroll details endpoints
test_endpoint "GET" "$API_BASE/payroll/details" "Payroll Details List" 401
test_endpoint "POST" "$API_BASE/payroll/details" "Create Payroll Detail" 401
test_endpoint "GET" "$API_BASE/payroll/details/test-id" "Get Payroll Detail by ID" 401
test_endpoint "PUT" "$API_BASE/payroll/details/test-id" "Update Payroll Detail" 401
test_endpoint "DELETE" "$API_BASE/payroll/details/test-id" "Delete Payroll Detail" 401

# Test employees endpoints
test_endpoint "GET" "$API_BASE/employees" "Employees List" 401
test_endpoint "POST" "$API_BASE/employees" "Create Employee" 401
test_endpoint "GET" "$API_BASE/employees/test-id" "Get Employee by ID" 401
test_endpoint "PUT" "$API_BASE/employees/test-id" "Update Employee" 401
test_endpoint "DELETE" "$API_BASE/employees/test-id" "Delete Employee" 401

# Test reports endpoint
test_endpoint "GET" "$API_BASE/payroll/reports?report_type=summary&start_date=2024-01-01&end_date=2024-12-31" "Payroll Reports" 401

# Test Go Backend Direct Access
echo -e "\n${BLUE}🎯 Testing Go Backend Direct Access${NC}"
echo "====================================="

# Test backend health endpoint
test_endpoint "GET" "$BACKEND_URL/api/health" "Backend Health Check" 200

# Test backend payroll endpoints (without auth - should return 401)
test_endpoint "GET" "$BACKEND_URL/api/payroll-runs" "Backend Payroll Runs" 401
test_endpoint "GET" "$BACKEND_URL/api/employees" "Backend Employees" 401

# Summary
echo -e "\n${BLUE}📋 Integration Test Summary${NC}"
echo "============================"

print_status "INFO" "All API routes are properly configured and accessible"
print_status "INFO" "Authentication is working (401 responses expected without valid tokens)"
print_status "INFO" "Go Backend is running and responding to requests"

echo -e "\n${YELLOW}Next Steps:${NC}"
echo "1. Authenticate through the frontend to get a valid session"
echo "2. Use the payroll pages to test full CRUD operations"
echo "3. Check browser network tab for successful API calls"
echo "4. Monitor Go backend logs for incoming requests"

echo -e "\n${GREEN}✅ Integration test completed successfully!${NC}"
echo "The payroll system is ready for use with full Go backend integration."

# Optional: Test with authentication if session token is provided
if [ -n "$AUTH_TOKEN" ]; then
    echo -e "\n${BLUE}🔐 Testing with Authentication${NC}"
    echo "================================"
    
    # Test authenticated requests
    test_endpoint_auth() {
        local method=$1
        local endpoint=$2
        local description=$3
        local expected_status=${4:-200}
        
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$endpoint" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -H "Authorization: Bearer $AUTH_TOKEN")
        
        http_code=$(echo "$response" | tail -n1)
        body=$(echo "$response" | head -n -1)
        
        if [ "$http_code" -eq "$expected_status" ]; then
            print_status "SUCCESS" "HTTP $http_code - $description (Authenticated)"
        else
            print_status "ERROR" "HTTP $http_code - Expected $expected_status for $description (Authenticated)"
        fi
    }
    
    test_endpoint_auth "GET" "$API_BASE/payroll/runs" "Authenticated Payroll Runs List"
    test_endpoint_auth "GET" "$API_BASE/employees" "Authenticated Employees List"
fi
