# Reusable Delete Dialog Components

This document explains how to use the reusable delete dialog components in the application.

## Components Overview

### 1. `DeleteDialog` - Base Component
Location: `src/components/ui/delete-dialog.tsx`

A fully reusable delete confirmation dialog that can be used across the application.

### 2. `useDeleteDialog` - Hook
Location: `src/hooks/useDeleteDialog.tsx`

A custom hook that provides an easy way to manage delete dialog state and rendering.

### 3. Entity-Specific Wrappers
Examples:
- `BillDeleteDialog` - `src/app/[locale]/bills/_components/BillDeleteDialog.tsx`
- `VendorDeleteDialog` - `src/app/[locale]/vendors/_components/VendorDeleteDialog.tsx`

## Usage Patterns

### Pattern 1: Direct DeleteDialog Usage

```tsx
import { DeleteDialog } from '@/components/ui/delete-dialog';
import { useState } from 'react';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);

  const handleDelete = async () => {
    // Your delete logic here
    await deleteItem(itemToDelete.id);
  };

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>Delete</Button>
      
      <DeleteDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onConfirm={handleDelete}
        title="Delete Item"
        description="Are you sure you want to delete this item?"
        itemDetails={<p>Item: {itemToDelete?.name}</p>}
        canDelete={true}
        confirmButtonText="Delete"
        cancelButtonText="Cancel"
        loadingText="Deleting..."
        successMessage="Item deleted successfully"
        errorMessage="Failed to delete item"
      />
    </>
  );
}
```

### Pattern 2: Using the useDeleteDialog Hook

```tsx
import { useDeleteDialog } from '@/hooks/useDeleteDialog';

function MyComponent() {
  const deleteDialog = useDeleteDialog({
    title: 'Delete Item',
    description: 'Are you sure you want to delete this item?',
    successMessage: 'Item deleted successfully',
    errorMessage: 'Failed to delete item',
  });

  const handleDeleteClick = (item) => {
    deleteDialog.openDialog({
      item,
      onConfirm: async () => {
        await deleteItem(item.id);
      },
      onDeleteSuccess: () => {
        // Refresh list or navigate
      },
      canDelete: item.status === 'draft',
      warningMessage: item.status !== 'draft' ? 'Only draft items can be deleted' : undefined,
      itemDetails: <p>Item: {item.name}</p>,
    });
  };

  return (
    <>
      <Button onClick={() => handleDeleteClick(myItem)}>Delete</Button>
      <deleteDialog.DeleteDialogComponent />
    </>
  );
}
```

### Pattern 3: Entity-Specific Wrapper

```tsx
import { BillDeleteDialog } from '@/app/[locale]/bills/_components/BillDeleteDialog';

function BillList() {
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [billToDelete, setBillToDelete] = useState(null);

  const handleDelete = (bill) => {
    setBillToDelete(bill);
    setIsDeleteOpen(true);
  };

  return (
    <>
      <Button onClick={() => handleDelete(bill)}>Delete Bill</Button>
      
      <BillDeleteDialog
        isOpen={isDeleteOpen}
        onClose={() => setIsDeleteOpen(false)}
        bill={billToDelete}
        onDeleteSuccess={() => {
          // Refresh or navigate
        }}
      />
    </>
  );
}
```

## DeleteDialog Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `isOpen` | boolean | ✅ | Controls dialog visibility |
| `onClose` | function | ✅ | Called when dialog should close |
| `onConfirm` | function | ✅ | Async function that performs the delete |
| `title` | string | ✅ | Dialog title |
| `description` | string | ✅ | Dialog description |
| `itemDetails` | ReactNode | ❌ | Custom content showing item details |
| `canDelete` | boolean | ❌ | Whether deletion is allowed (default: true) |
| `warningMessage` | string | ❌ | Warning message when canDelete is false |
| `confirmButtonText` | string | ❌ | Delete button text (default: "Delete") |
| `cancelButtonText` | string | ❌ | Cancel button text (default: "Cancel") |
| `loadingText` | string | ❌ | Loading button text (default: "Deleting...") |
| `successMessage` | string | ❌ | Success toast message |
| `errorMessage` | string | ❌ | Error toast message |
| `onDeleteSuccess` | function | ❌ | Called after successful deletion |

## useDeleteDialog Hook

### Parameters
```tsx
interface UseDeleteDialogProps {
  title: string;
  description: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  loadingText?: string;
  successMessage?: string;
  errorMessage?: string;
}
```

### Returns
```tsx
{
  openDialog: (options) => void;
  closeDialog: () => void;
  DeleteDialogComponent: React.Component;
  isOpen: boolean;
  item: any;
}
```

## Best Practices

1. **Use entity-specific wrappers** for complex business logic
2. **Use the hook** for simple, repeated delete operations
3. **Use direct DeleteDialog** for one-off implementations
4. **Always provide meaningful error messages** and success feedback
5. **Include item details** to help users confirm they're deleting the right item
6. **Implement proper permission checks** with `canDelete` prop
7. **Handle success callbacks** for UI updates (refresh lists, navigation, etc.)

## Features

- ✅ Loading states with spinner
- ✅ Error handling with toast notifications
- ✅ Success feedback with toast notifications
- ✅ Permission-based deletion with warnings
- ✅ Customizable content and messaging
- ✅ Proper accessibility with AlertDialog
- ✅ Consistent styling across the application
- ✅ TypeScript support
- ✅ Internationalization ready
