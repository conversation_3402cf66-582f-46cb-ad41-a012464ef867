#!/usr/bin/env tsx

/**
 * Accounts Receivable API Test Runner
 *
 * This script runs comprehensive API tests for the accounts receivable module.
 * It validates all AR endpoints and ensures proper integration with the backend.
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8050';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3001';

// Test files to run (only for endpoints that exist in backend)
const AR_TEST_FILES = [
  'tests/api/accounts-receivable/invoices.api.test.ts',
  // Note: aging-reports and credit-notes endpoints don't exist in backend yet
  // 'tests/api/accounts-receivable/aging-reports.api.test.ts',
  // 'tests/api/accounts-receivable/credit-notes.api.test.ts',
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message: string, color: string = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logHeader(message: string) {
  log(`\n${'='.repeat(60)}`, colors.cyan);
  log(`${message}`, colors.cyan + colors.bright);
  log(`${'='.repeat(60)}`, colors.cyan);
}

function logStep(step: string, message: string) {
  log(`[${step}] ${message}`, colors.blue);
}

function logSuccess(message: string) {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, colors.yellow);
}

async function checkPrerequisites(): Promise<boolean> {
  logHeader('Checking Prerequisites');

  let allGood = true;

  // Check if backend is running
  logStep('1', 'Checking backend server...');
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (response.ok) {
      logSuccess(`Backend server is running at ${API_BASE_URL}`);
    } else {
      logError(`Backend server responded with status ${response.status}`);
      allGood = false;
    }
  } catch (error) {
    logError(`Backend server is not accessible at ${API_BASE_URL}`);
    logError(`Please start the backend server: cd go-backend && make run`);
    allGood = false;
  }

  // Check if frontend is running (optional)
  logStep('2', 'Checking frontend server...');
  try {
    const response = await fetch(`${FRONTEND_URL}`);
    if (response.ok) {
      logSuccess(`Frontend server is running at ${FRONTEND_URL}`);
    } else {
      logWarning(`Frontend server responded with status ${response.status}`);
    }
  } catch (error) {
    logWarning(`Frontend server is not accessible at ${FRONTEND_URL}`);
    logWarning(`Consider starting the frontend: npm run dev`);
  }

  // Check if test user exists
  logStep('3', 'Checking test user...');
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpass123'
      }),
    });

    if (response.ok) {
      logSuccess('Test user authentication successful');
    } else {
      logError('Test user authentication failed');
      logError('Please ensure test user exists: <EMAIL> / testpass123');
      allGood = false;
    }
  } catch (error) {
    logError('Failed to test user authentication');
    allGood = false;
  }

  // Check if test files exist
  logStep('4', 'Checking test files...');
  for (const testFile of AR_TEST_FILES) {
    if (existsSync(testFile)) {
      logSuccess(`Found test file: ${testFile}`);
    } else {
      logError(`Missing test file: ${testFile}`);
      allGood = false;
    }
  }

  return allGood;
}

function runVitest(testFiles: string[]): Promise<boolean> {
  return new Promise((resolve) => {
    logHeader('Running Accounts Receivable API Tests');

    const vitestArgs = [
      'vitest',
      'run',
      '--reporter=verbose',
      '--bail=1', // Stop on first failure for debugging
      ...testFiles
    ];

    logStep('EXEC', `bun ${vitestArgs.join(' ')}`);

    const vitestProcess = spawn('bun', vitestArgs, {
      stdio: 'inherit',
      env: {
        ...process.env,
        NEXT_PUBLIC_API_URL: API_BASE_URL,
        FRONTEND_URL: FRONTEND_URL,
      }
    });

    vitestProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess('All API tests passed!');
        resolve(true);
      } else {
        logError(`Tests failed with exit code ${code}`);
        resolve(false);
      }
    });

    vitestProcess.on('error', (error) => {
      logError(`Failed to run tests: ${error.message}`);
      resolve(false);
    });
  });
}

async function generateTestReport(success: boolean) {
  logHeader('Test Report Generation');

  const timestamp = new Date().toISOString();
  const reportData = {
    timestamp,
    success,
    testSuite: 'Accounts Receivable API Tests',
    apiBaseUrl: API_BASE_URL,
    frontendUrl: FRONTEND_URL,
    testFiles: AR_TEST_FILES,
  };

  const reportPath = path.join('tests', 'test-results', `ar-api-test-report-${Date.now()}.json`);

  try {
    const fs = await import('fs/promises');
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(reportData, null, 2));
    logSuccess(`Test report saved to: ${reportPath}`);
  } catch (error) {
    logWarning(`Failed to save test report: ${error}`);
  }
}

async function main() {
  logHeader('Accounts Receivable API Test Suite');
  log('This script validates all AR API endpoints and backend integration.', colors.cyan);

  // Check prerequisites
  const prerequisitesOk = await checkPrerequisites();
  if (!prerequisitesOk) {
    logError('Prerequisites check failed. Please fix the issues above and try again.');
    process.exit(1);
  }

  // Run the tests
  const testsSuccessful = await runVitest(AR_TEST_FILES);

  // Generate report
  await generateTestReport(testsSuccessful);

  // Final summary
  logHeader('Test Summary');
  if (testsSuccessful) {
    logSuccess('🎉 All Accounts Receivable API tests passed!');
    logSuccess('The AR module is properly integrated with the backend.');
    log('\nNext steps:', colors.cyan);
    log('1. Run integration tests for AR pages', colors.cyan);
    log('2. Run E2E tests for complete AR workflows', colors.cyan);
    log('3. Test performance with larger datasets', colors.cyan);
    process.exit(0);
  } else {
    logError('💥 Some tests failed. Please check the output above.');
    log('\nDebugging tips:', colors.yellow);
    log('1. Check backend logs for API errors', colors.yellow);
    log('2. Verify database connectivity', colors.yellow);
    log('3. Ensure test data is properly seeded', colors.yellow);
    log('4. Check authentication and permissions', colors.yellow);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('\n\nTest execution interrupted by user.', colors.yellow);
  process.exit(130);
});

process.on('SIGTERM', () => {
  log('\n\nTest execution terminated.', colors.yellow);
  process.exit(143);
});

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export { main, checkPrerequisites, runVitest };
