import { describe, it, expect, beforeAll, afterAll } from 'vitest';

/**
 * Accounts Receivable - Credit Notes API Tests
 *
 * This test suite validates the credit notes API endpoints used by the AR module.
 * Tests cover CRUD operations, application to invoices, and data validation.
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8050';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpass123'
};

let authToken: string;
let testCreditNoteId: string;
let testInvoiceId: string;

describe('Accounts Receivable - Credit Notes API', () => {
  beforeAll(async () => {
    // Login to get auth token
    const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    expect(loginResponse.ok).toBe(true);
    const loginData = await loginResponse.json();
    authToken = loginData.accessToken;
    expect(authToken).toBeDefined();

    // Create a test invoice for credit note application
    const invoiceResponse = await fetch(`${API_BASE_URL}/api/invoices`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customer_id: 1,
        invoice_number: `TEST-INV-CN-${Date.now()}`,
        issue_date: new Date().toISOString().split('T')[0],
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        subtotal: 500.00,
        tax_amount: 50.00,
        total_amount: 550.00,
        status: 'pending',
        items: [
          {
            description: 'Test Service for Credit Note',
            quantity: 1,
            unit_price: 500.00,
            total: 500.00
          }
        ]
      }),
    });

    if (invoiceResponse.ok) {
      const invoiceData = await invoiceResponse.json();
      testInvoiceId = invoiceData.invoice.id;
    }
  });

  describe('GET /api/credit-notes', () => {
    it('should fetch credit notes with authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('credit_notes');
      expect(Array.isArray(data.credit_notes)).toBe(true);

      // Validate credit note structure if credit notes exist
      if (data.credit_notes.length > 0) {
        const creditNote = data.credit_notes[0];
        expect(creditNote).toHaveProperty('id');
        expect(creditNote).toHaveProperty('credit_note_number');
        expect(creditNote).toHaveProperty('customer_id');
        expect(creditNote).toHaveProperty('amount');
        expect(creditNote).toHaveProperty('status');
        expect(creditNote).toHaveProperty('issue_date');
        expect(creditNote).toHaveProperty('created_at');
      }
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes`);
      expect(response.status).toBe(401);
    });

    it('should support pagination parameters', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes?page=1&limit=10`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('credit_notes');
      expect(data).toHaveProperty('pagination');
      expect(data.pagination).toHaveProperty('page');
      expect(data.pagination).toHaveProperty('limit');
      expect(data.pagination).toHaveProperty('total');
    });

    it('should support filtering by status', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes?status=pending`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('credit_notes');
      // All returned credit notes should have pending status
      data.credit_notes.forEach((creditNote: any) => {
        expect(creditNote.status).toBe('pending');
      });
    });

    it('should support filtering by customer', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes?customer_id=1`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('credit_notes');
      // All returned credit notes should be for the specified customer
      data.credit_notes.forEach((creditNote: any) => {
        expect(creditNote.customer_id).toBe(1);
      });
    });
  });

  describe('POST /api/credit-notes', () => {
    it('should create a new credit note with valid data', async () => {
      const newCreditNote = {
        customer_id: 1,
        credit_note_number: `CN-TEST-${Date.now()}`,
        issue_date: new Date().toISOString().split('T')[0],
        amount: 100.00,
        reason: 'Product return',
        status: 'pending',
        items: [
          {
            description: 'Returned item',
            quantity: 1,
            unit_price: 100.00,
            total: 100.00
          }
        ]
      };

      const response = await fetch(`${API_BASE_URL}/api/credit-notes`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newCreditNote),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('credit_note');
      expect(data.credit_note).toHaveProperty('id');
      expect(data.credit_note.credit_note_number).toBe(newCreditNote.credit_note_number);
      expect(data.credit_note.amount).toBe(newCreditNote.amount);
      expect(data.credit_note.reason).toBe(newCreditNote.reason);

      testCreditNoteId = data.credit_note.id;
    });

    it('should reject credit note creation with invalid data', async () => {
      const invalidCreditNote = {
        // Missing required fields
        customer_id: null,
        amount: -50, // Invalid negative amount
      };

      const response = await fetch(`${API_BASE_URL}/api/credit-notes`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidCreditNote),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/credit-notes/:id', () => {
    it('should fetch a specific credit note by ID', async () => {
      if (!testCreditNoteId) {
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('credit_note');
      expect(data.credit_note.id).toBe(testCreditNoteId);
      expect(data.credit_note).toHaveProperty('items');
      expect(Array.isArray(data.credit_note.items)).toBe(true);
    });

    it('should return 404 for non-existent credit note', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes/999999`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.status).toBe(404);
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes/1`);
      expect(response.status).toBe(401);
    });
  });

  describe('PUT /api/credit-notes/:id', () => {
    it('should update an existing credit note', async () => {
      if (!testCreditNoteId) {
        return;
      }

      const updateData = {
        status: 'approved',
        notes: 'Updated via API test'
      };

      const response = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('credit_note');
      expect(data.credit_note.status).toBe('approved');
      expect(data.credit_note.notes).toBe('Updated via API test');
    });

    it('should reject invalid update data', async () => {
      if (!testCreditNoteId) {
        return;
      }

      const invalidUpdate = {
        amount: -100 // Invalid negative amount
      };

      const response = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidUpdate),
      });

      expect(response.status).toBe(400);
    });
  });

  describe('POST /api/credit-notes/:id/apply', () => {
    it('should apply credit note to an invoice', async () => {
      if (!testCreditNoteId || !testInvoiceId) {
        return;
      }

      const applyData = {
        invoice_id: testInvoiceId,
        amount: 50.00
      };

      const response = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}/apply`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(applyData),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('application');
      expect(data.application).toHaveProperty('credit_note_id');
      expect(data.application).toHaveProperty('invoice_id');
      expect(data.application).toHaveProperty('amount_applied');
      expect(data.application.credit_note_id).toBe(testCreditNoteId);
      expect(data.application.invoice_id).toBe(testInvoiceId);
      expect(data.application.amount_applied).toBe(50.00);
    });

    it('should reject application with invalid amount', async () => {
      if (!testCreditNoteId || !testInvoiceId) {
        return;
      }

      const invalidApplyData = {
        invoice_id: testInvoiceId,
        amount: 1000.00 // Amount greater than credit note balance
      };

      const response = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}/apply`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidApplyData),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should reject application to non-existent invoice', async () => {
      if (!testCreditNoteId) {
        return;
      }

      const applyData = {
        invoice_id: 999999,
        amount: 25.00
      };

      const response = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}/apply`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(applyData),
      });

      expect(response.status).toBe(404);
    });
  });

  describe('DELETE /api/credit-notes/:id', () => {
    it('should delete a credit note', async () => {
      if (!testCreditNoteId) {
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);

      // Verify credit note is deleted
      const getResponse = await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(getResponse.status).toBe(404);
    });

    it('should return 404 when deleting non-existent credit note', async () => {
      const response = await fetch(`${API_BASE_URL}/api/credit-notes/999999`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.status).toBe(404);
    });
  });

  afterAll(async () => {
    // Cleanup: Delete test credit note and invoice if they still exist
    if (testCreditNoteId) {
      await fetch(`${API_BASE_URL}/api/credit-notes/${testCreditNoteId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });
    }

    if (testInvoiceId) {
      await fetch(`${API_BASE_URL}/api/invoices/${testInvoiceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });
    }
  });
});
