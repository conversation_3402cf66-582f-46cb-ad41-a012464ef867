import { describe, it, expect, beforeAll } from 'vitest';

/**
 * Accounts Receivable - Aging Reports API Tests
 *
 * This test suite validates the aging reports API endpoints used by the AR module.
 * Tests cover data retrieval, filtering, and report generation.
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8050';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpass123'
};

let authToken: string;

describe('Accounts Receivable - Aging Reports API', () => {
  beforeAll(async () => {
    // Login to get auth token
    const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    expect(loginResponse.ok).toBe(true);
    const loginData = await loginResponse.json();
    authToken = loginData.accessToken;
    expect(authToken).toBeDefined();
  });

  describe('GET /api/accounts-receivable-aging', () => {
    it('should fetch aging report data with authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');
      expect(Array.isArray(data.aging_data)).toBe(true);

      // Validate aging report structure
      if (data.aging_data.length > 0) {
        const agingEntry = data.aging_data[0];
        expect(agingEntry).toHaveProperty('customer_id');
        expect(agingEntry).toHaveProperty('customer_name');
        expect(agingEntry).toHaveProperty('current');
        expect(agingEntry).toHaveProperty('days_30');
        expect(agingEntry).toHaveProperty('days_60');
        expect(agingEntry).toHaveProperty('days_90');
        expect(agingEntry).toHaveProperty('days_over_90');
        expect(agingEntry).toHaveProperty('total_outstanding');

        // Validate numeric fields
        expect(typeof agingEntry.current).toBe('number');
        expect(typeof agingEntry.days_30).toBe('number');
        expect(typeof agingEntry.days_60).toBe('number');
        expect(typeof agingEntry.days_90).toBe('number');
        expect(typeof agingEntry.days_over_90).toBe('number');
        expect(typeof agingEntry.total_outstanding).toBe('number');
      }
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging`);
      expect(response.status).toBe(401);
    });

    it('should support date range filtering', async () => {
      const asOfDate = new Date().toISOString().split('T')[0];

      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?as_of_date=${asOfDate}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');
      expect(data).toHaveProperty('as_of_date');
      expect(data.as_of_date).toBe(asOfDate);
    });

    it('should support customer filtering', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?customer_id=1`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');
      // All entries should be for the specified customer
      data.aging_data.forEach((entry: any) => {
        expect(entry.customer_id).toBe(1);
      });
    });

    it('should include summary totals', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?include_summary=true`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');
      expect(data).toHaveProperty('summary');

      if (data.summary) {
        expect(data.summary).toHaveProperty('total_current');
        expect(data.summary).toHaveProperty('total_30_days');
        expect(data.summary).toHaveProperty('total_60_days');
        expect(data.summary).toHaveProperty('total_90_days');
        expect(data.summary).toHaveProperty('total_over_90_days');
        expect(data.summary).toHaveProperty('grand_total');

        // Validate summary totals are numbers
        expect(typeof data.summary.total_current).toBe('number');
        expect(typeof data.summary.total_30_days).toBe('number');
        expect(typeof data.summary.total_60_days).toBe('number');
        expect(typeof data.summary.total_90_days).toBe('number');
        expect(typeof data.summary.total_over_90_days).toBe('number');
        expect(typeof data.summary.grand_total).toBe('number');
      }
    });

    it('should support sorting options', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?sort_by=total_outstanding&sort_order=desc`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');

      // Verify sorting if there are multiple entries
      if (data.aging_data.length > 1) {
        for (let i = 0; i < data.aging_data.length - 1; i++) {
          expect(data.aging_data[i].total_outstanding).toBeGreaterThanOrEqual(
            data.aging_data[i + 1].total_outstanding
          );
        }
      }
    });

    it('should handle empty results gracefully', async () => {
      // Use a future date that should have no aging data
      const futureDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?as_of_date=${futureDate}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');
      expect(Array.isArray(data.aging_data)).toBe(true);
    });

    it('should validate date format', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?as_of_date=invalid-date`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should support export format parameter', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?format=csv`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);

      // Check if response is CSV format
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('text/csv')) {
        const csvData = await response.text();
        expect(csvData).toContain('customer_name');
        expect(csvData).toContain('total_outstanding');
      } else {
        // If not CSV, should be JSON with export data
        const data = await response.json();
        expect(data).toHaveProperty('aging_data');
      }
    });

    it('should include overdue invoice details when requested', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?include_details=true`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');

      // Check if details are included for entries with outstanding amounts
      data.aging_data.forEach((entry: any) => {
        if (entry.total_outstanding > 0) {
          expect(entry).toHaveProperty('invoice_details');
          if (entry.invoice_details && entry.invoice_details.length > 0) {
            const detail = entry.invoice_details[0];
            expect(detail).toHaveProperty('invoice_id');
            expect(detail).toHaveProperty('invoice_number');
            expect(detail).toHaveProperty('due_date');
            expect(detail).toHaveProperty('amount_due');
            expect(detail).toHaveProperty('days_overdue');
          }
        }
      });
    });

    it('should handle pagination for large datasets', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging?page=1&limit=10`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('aging_data');
      expect(data).toHaveProperty('pagination');

      if (data.pagination) {
        expect(data.pagination).toHaveProperty('page');
        expect(data.pagination).toHaveProperty('limit');
        expect(data.pagination).toHaveProperty('total');
        expect(data.pagination).toHaveProperty('total_pages');

        expect(data.pagination.page).toBe(1);
        expect(data.pagination.limit).toBe(10);
        expect(data.aging_data.length).toBeLessThanOrEqual(10);
      }
    });
  });

  describe('GET /api/accounts-receivable-aging/summary', () => {
    it('should fetch aging summary data', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging/summary`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('summary');
      expect(data.summary).toHaveProperty('total_customers');
      expect(data.summary).toHaveProperty('total_outstanding');
      expect(data.summary).toHaveProperty('overdue_amount');
      expect(data.summary).toHaveProperty('current_amount');

      // Validate numeric fields
      expect(typeof data.summary.total_customers).toBe('number');
      expect(typeof data.summary.total_outstanding).toBe('number');
      expect(typeof data.summary.overdue_amount).toBe('number');
      expect(typeof data.summary.current_amount).toBe('number');
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/accounts-receivable-aging/summary`);
      expect(response.status).toBe(401);
    });
  });
});
