import { describe, it, expect, beforeAll, afterAll } from 'vitest';

/**
 * Accounts Receivable - Invoices API Tests
 *
 * This test suite validates the invoices API endpoints used by the AR module.
 * Tests cover CRUD operations, data validation, and error handling.
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8050';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3001';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpass123'
};

let authToken: string;
let testInvoiceId: string;

describe('Accounts Receivable - Invoices API', () => {
  beforeAll(async () => {
    // Login to get auth token
    const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    expect(loginResponse.ok).toBe(true);
    const loginData = await loginResponse.json();
    authToken = loginData.accessToken;
    expect(authToken).toBeDefined();
  });

  describe('GET /api/invoices', () => {
    it('should fetch invoices with authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('data');
      expect(data).toHaveProperty('page');
      expect(data).toHaveProperty('limit');
      expect(data).toHaveProperty('total');
      expect(data).toHaveProperty('totalPages');

      // Validate invoice structure if invoices exist
      if (data.data && data.data.length > 0) {
        const invoice = data.data[0];
        expect(invoice).toHaveProperty('id');
        expect(invoice).toHaveProperty('invoice_number');
        expect(invoice).toHaveProperty('customer_id');
        expect(invoice).toHaveProperty('total_amount');
        expect(invoice).toHaveProperty('status');
        expect(invoice).toHaveProperty('due_date');
        expect(invoice).toHaveProperty('created_at');
      }
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices`);
      expect(response.status).toBe(401);
    });

    it('should support pagination parameters', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices?page=1&limit=10`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('data');
      expect(data).toHaveProperty('page');
      expect(data).toHaveProperty('limit');
      expect(data).toHaveProperty('total');
      expect(data).toHaveProperty('totalPages');
    });

    it('should support filtering by status', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices?status=pending`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      expect(data).toHaveProperty('data');
      // All returned invoices should have pending status
      if (data.data && data.data.length > 0) {
        data.data.forEach((invoice: any) => {
          expect(invoice.status).toBe('pending');
        });
      }
    });

    it('should support search functionality', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices?search=INV`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('data');
    });
  });

  describe('POST /api/invoices', () => {
    it('should create a new invoice with valid data', async () => {
      const newInvoice = {
        customer_id: 1,
        invoice_number: `TEST-INV-${Date.now()}`,
        issue_date: new Date().toISOString().split('T')[0],
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        subtotal: 1000.00,
        tax_amount: 100.00,
        total_amount: 1100.00,
        status: 'pending',
        items: [
          {
            description: 'Test Service',
            quantity: 1,
            unit_price: 1000.00,
            total: 1000.00
          }
        ]
      };

      const response = await fetch(`${API_BASE_URL}/api/invoices`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newInvoice),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      // The response might be the invoice object directly or wrapped
      const invoice = data.invoice || data;
      expect(invoice).toHaveProperty('id');
      expect(invoice.invoice_number).toBe(newInvoice.invoice_number);
      expect(invoice.total_amount).toBe(newInvoice.total_amount);

      testInvoiceId = invoice.id;
    });

    it('should reject invoice creation with invalid data', async () => {
      const invalidInvoice = {
        // Missing required fields
        customer_id: null,
        total_amount: -100, // Invalid negative amount
      };

      const response = await fetch(`${API_BASE_URL}/api/invoices`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidInvoice),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/invoices/:id', () => {
    it('should fetch a specific invoice by ID', async () => {
      if (!testInvoiceId) {
        // Skip if no test invoice was created
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/invoices/${testInvoiceId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      // The response might be the invoice object directly or wrapped
      const invoice = data.invoice || data;
      expect(invoice.id).toBe(testInvoiceId);
      expect(invoice).toHaveProperty('items');
      expect(Array.isArray(invoice.items)).toBe(true);
    });

    it('should return 404 for non-existent invoice', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices/999999`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.status).toBe(404);
    });

    it('should reject requests without authentication', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices/1`);
      expect(response.status).toBe(401);
    });
  });

  describe('PUT /api/invoices/:id', () => {
    it('should update an existing invoice', async () => {
      if (!testInvoiceId) {
        return;
      }

      const updateData = {
        status: 'sent',
        notes: 'Updated via API test'
      };

      const response = await fetch(`${API_BASE_URL}/api/invoices/${testInvoiceId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();

      // The response might be the invoice object directly or wrapped
      const invoice = data.invoice || data;
      expect(invoice.status).toBe('sent');
      expect(invoice.notes).toBe('Updated via API test');
    });

    it('should reject invalid update data', async () => {
      if (!testInvoiceId) {
        return;
      }

      const invalidUpdate = {
        total_amount: -500 // Invalid negative amount
      };

      const response = await fetch(`${API_BASE_URL}/api/invoices/${testInvoiceId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidUpdate),
      });

      expect(response.status).toBe(400);
    });
  });

  describe('DELETE /api/invoices/:id', () => {
    it('should delete an invoice', async () => {
      if (!testInvoiceId) {
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/invoices/${testInvoiceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.ok).toBe(true);

      // Verify invoice is deleted
      const getResponse = await fetch(`${API_BASE_URL}/api/invoices/${testInvoiceId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(getResponse.status).toBe(404);
    });

    it('should return 404 when deleting non-existent invoice', async () => {
      const response = await fetch(`${API_BASE_URL}/api/invoices/999999`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      expect(response.status).toBe(404);
    });
  });

  afterAll(async () => {
    // Cleanup: Delete test invoice if it still exists
    if (testInvoiceId) {
      await fetch(`${API_BASE_URL}/api/invoices/${testInvoiceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });
    }
  });
});
