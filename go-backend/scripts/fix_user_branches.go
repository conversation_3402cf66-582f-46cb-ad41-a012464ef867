package main

import (
	"fmt"
	"log"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database connection
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Get all users who don't have any branch permissions
	var users []models.User
	err = db.Where("id NOT IN (SELECT DISTINCT user_id FROM user_branch_permissions)").Find(&users).Error
	if err != nil {
		log.Fatal("Failed to get users:", err)
	}

	fmt.Printf("Found %d users without branch permissions\n", len(users))

	for _, user := range users {
		fmt.Printf("Processing user: %s (%s)\n", user.Email, user.ID)

		// Check if user has an organization
		var orgPermission models.UserOrganizationPermission
		err = db.Where("user_id = ?", user.ID).First(&orgPermission).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				fmt.Printf("User %s has no organization, creating one...\n", user.Email)
				err = createDefaultOrganizationForUser(db, &user)
				if err != nil {
					log.Printf("Failed to create organization for user %s: %v", user.Email, err)
					continue
				}
			} else {
				log.Printf("Error checking organization for user %s: %v", user.Email, err)
				continue
			}
		} else {
			// User has organization, create default branch
			fmt.Printf("User %s has organization, creating default branch...\n", user.Email)
			err = createDefaultBranchForUser(db, &user, orgPermission.OrganizationID)
			if err != nil {
				log.Printf("Failed to create branch for user %s: %v", user.Email, err)
				continue
			}
		}

		fmt.Printf("Successfully processed user: %s\n", user.Email)
	}

	fmt.Println("Migration completed!")
}

func createDefaultOrganizationForUser(db *gorm.DB, user *models.User) error {
	// Create organization
	orgName := "My Organization"
	if user.Name != nil && *user.Name != "" {
		orgName = *user.Name + "'s Organization"
	}

	organization := models.Organization{
		ID:          uuid.New().String(),
		Name:        orgName,
		Slug:        generateSlug(orgName),
		Description: nil,
		IsActive:    true,
	}

	if err := db.Create(&organization).Error; err != nil {
		return err
	}

	// Create organization permission for the user (as Owner)
	orgPermission := models.UserOrganizationPermission{
		ID:              uuid.New().String(),
		UserID:          user.ID,
		OrganizationID:  organization.ID,
		PermissionLevel: models.PermissionOwner,
	}

	if err := db.Create(&orgPermission).Error; err != nil {
		return err
	}

	// Create default branch
	return createDefaultBranchForUser(db, user, organization.ID)
}

func createDefaultBranchForUser(db *gorm.DB, user *models.User, organizationID string) error {
	// Create a default branch for the organization
	branchName := "Main Branch"
	if user.Name != nil && *user.Name != "" {
		branchName = "Main Branch"
	}

	branch := models.Branch{
		ID:             uuid.New().String(),
		OrganizationID: organizationID,
		Name:           branchName,
		Description:    nil,
		IsActive:       true,
	}

	if err := db.Create(&branch).Error; err != nil {
		return err
	}

	// Create branch permission for the user (as Owner)
	branchPermission := models.UserBranchPermission{
		ID:              uuid.New().String(),
		UserID:          user.ID,
		BranchID:        branch.ID,
		PermissionLevel: models.PermissionOwner,
	}

	if err := db.Create(&branchPermission).Error; err != nil {
		return err
	}

	return nil
}

func generateSlug(name string) string {
	// Simple slug generation - replace spaces with hyphens and convert to lowercase
	slug := ""
	for _, char := range name {
		if char == ' ' {
			slug += "-"
		} else if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || (char >= '0' && char <= '9') {
			if char >= 'A' && char <= 'Z' {
				slug += string(char + 32) // Convert to lowercase
			} else {
				slug += string(char)
			}
		}
	}
	return slug
}
