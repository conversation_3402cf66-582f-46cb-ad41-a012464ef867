package main

import (
	"fmt"
	"log"

	"adc-account-backend/internal/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Connect to database
	db, err := gorm.Open(postgres.Open(cfg.DatabaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("Starting bank_accounts table migration...")

	// Check if merchant_id column exists
	var merchantIdExists bool
	err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'bank_accounts' AND column_name = 'merchant_id')").Scan(&merchantIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check merchant_id column: %v", err)
	}

	// Check if branch_id column exists
	var branchIdExists bool
	err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'bank_accounts' AND column_name = 'branch_id')").Scan(&branchIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check branch_id column: %v", err)
	}

	if merchantIdExists && !branchIdExists {
		fmt.Println("Migrating bank_accounts from merchant_id to branch_id...")

		// Add branch_id column
		if err := db.Exec("ALTER TABLE bank_accounts ADD COLUMN branch_id VARCHAR(36)").Error; err != nil {
			log.Fatalf("Failed to add branch_id column: %v", err)
		}

		// Copy data from merchant_id to branch_id (assuming 1:1 mapping for now)
		if err := db.Exec("UPDATE bank_accounts SET branch_id = merchant_id").Error; err != nil {
			log.Fatalf("Failed to copy merchant_id to branch_id: %v", err)
		}

		// Make branch_id NOT NULL
		if err := db.Exec("ALTER TABLE bank_accounts ALTER COLUMN branch_id SET NOT NULL").Error; err != nil {
			log.Fatalf("Failed to make branch_id NOT NULL: %v", err)
		}

		// Add index for branch_id
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_bank_accounts_branch_id ON bank_accounts(branch_id)").Error; err != nil {
			log.Fatalf("Failed to create branch_id index: %v", err)
		}

		// Drop foreign key constraint for merchant_id
		if err := db.Exec("ALTER TABLE bank_accounts DROP CONSTRAINT IF EXISTS fk_bank_accounts_merchant").Error; err != nil {
			log.Printf("Warning: Failed to drop merchant foreign key constraint: %v", err)
		}

		// Drop merchant_id column
		if err := db.Exec("ALTER TABLE bank_accounts DROP COLUMN merchant_id").Error; err != nil {
			log.Fatalf("Failed to drop merchant_id column: %v", err)
		}

		// Add foreign key constraint for branch_id
		if err := db.Exec("ALTER TABLE bank_accounts ADD CONSTRAINT fk_bank_accounts_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE").Error; err != nil {
			log.Printf("Warning: Failed to add branch foreign key constraint: %v", err)
		}

		fmt.Println("Successfully migrated bank_accounts table to use branch_id")
	} else if branchIdExists {
		fmt.Println("bank_accounts table already uses branch_id")
	} else {
		fmt.Println("bank_accounts table structure is unexpected")
	}

	// Check if account_id column exists (this might be the missing field causing the error)
	var accountIdExists bool
	err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'bank_accounts' AND column_name = 'account_id')").Scan(&accountIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check account_id column: %v", err)
	}

	if accountIdExists {
		fmt.Println("Removing account_id column if it exists...")
		// Drop account_id column if it exists (it's not in our current schema)
		if err := db.Exec("ALTER TABLE bank_accounts DROP COLUMN IF EXISTS account_id").Error; err != nil {
			log.Printf("Warning: Failed to drop account_id column: %v", err)
		}
	}

	fmt.Println("Bank accounts migration completed successfully!")
}
