package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Financial Status Enums
type InvoiceStatus string
type BillStatus string
type ExpenseStatus string

const (
	InvoiceStatusDraft         InvoiceStatus = "Draft"
	InvoiceStatusSent          InvoiceStatus = "Sent"
	InvoiceStatusPartiallyPaid InvoiceStatus = "PartiallyPaid"
	InvoiceStatusPaid          InvoiceStatus = "Paid"
	InvoiceStatusOverdue       InvoiceStatus = "Overdue"
	InvoiceStatusVoid          InvoiceStatus = "Void"

	BillStatusDraft         BillStatus = "Draft"
	BillStatusOpen          BillStatus = "Open"
	BillStatusPartiallyPaid BillStatus = "PartiallyPaid"
	BillStatusPaid          BillStatus = "Paid"
	BillStatusVoid          BillStatus = "Void"

	ExpenseStatusPending  ExpenseStatus = "Pending"
	ExpenseStatusApproved ExpenseStatus = "Approved"
	ExpenseStatusRejected ExpenseStatus = "Rejected"
	ExpenseStatusPaid     ExpenseStatus = "Paid"
)

// Invoice represents the invoices table
type Invoice struct {
	ID              string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID        string          `json:"branchId" gorm:"type:varchar(36);not null;index"`
	CustomerID      string          `json:"customerId" gorm:"type:varchar(36);not null;index"`
	InvoiceNumber   string          `json:"invoiceNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_branch_invoice_number"`
	Status          InvoiceStatus   `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	IssueDate       time.Time       `json:"issueDate" gorm:"not null;index"`
	DueDate         *time.Time      `json:"dueDate" gorm:"index"`
	SubTotal        decimal.Decimal `json:"subTotal" gorm:"type:decimal(15,2);not null;default:0"`
	TaxAmount       decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	DiscountAmount  decimal.Decimal `json:"discountAmount" gorm:"type:decimal(15,2);not null;default:0"`
	TotalAmount     decimal.Decimal `json:"totalAmount" gorm:"type:decimal(15,2);not null;default:0"`
	PaidAmount      decimal.Decimal `json:"paidAmount" gorm:"type:decimal(15,2);not null;default:0"`
	BalanceAmount   decimal.Decimal `json:"balanceAmount" gorm:"type:decimal(15,2);not null;default:0"`
	Notes           *string         `json:"notes" gorm:"type:text"`
	Terms           *string         `json:"terms" gorm:"type:text"`
	CreatedAt       time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency        string          `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate    decimal.Decimal `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`
	Reference       *string         `json:"reference" gorm:"type:varchar(255)"`
	PoNumber        *string         `json:"poNumber" gorm:"type:varchar(100)"`
	ShippingAddress *string         `json:"shippingAddress" gorm:"type:text"`
	BillingAddress  *string         `json:"billingAddress" gorm:"type:text"`

	// Relationships
	Branch         Branch           `json:"branch" gorm:"foreignKey:BranchID"`
	Customer       Customer         `json:"customer" gorm:"foreignKey:CustomerID"`
	Items          []InvoiceItem    `json:"items" gorm:"foreignKey:InvoiceID"`
	Payments       []InvoicePayment `json:"payments" gorm:"foreignKey:InvoiceID"`
	CreditNotes    []CreditNote     `json:"creditNotes" gorm:"foreignKey:InvoiceID"`
	JournalEntries []JournalEntry   `json:"journalEntries" gorm:"foreignKey:SourceID;foreignKey:SourceType"`
}

// InvoiceItem represents the invoice_items table
type InvoiceItem struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	InvoiceID   string          `json:"invoiceId" gorm:"type:varchar(36);not null;index"`
	Description string          `json:"description" gorm:"type:text;not null"`
	Quantity    decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice   decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TotalPrice  decimal.Decimal `json:"totalPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID   *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	TaxAmount   decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	SortOrder   int             `json:"sortOrder" gorm:"default:0"`

	// Relationships
	Invoice Invoice  `json:"invoice" gorm:"foreignKey:InvoiceID"`
	TaxRate *TaxRate `json:"taxRate" gorm:"foreignKey:TaxRateID"`
}

// InvoicePayment represents the invoice_payments table
type InvoicePayment struct {
	ID            string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	InvoiceID     string          `json:"invoiceId" gorm:"type:varchar(36);not null;index"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	PaymentDate   time.Time       `json:"paymentDate" gorm:"not null;index"`
	PaymentMethod *string         `json:"paymentMethod" gorm:"type:varchar(100)"`
	Reference     *string         `json:"reference" gorm:"type:varchar(255)"`
	Notes         *string         `json:"notes" gorm:"type:text"`
	CreatedAt     time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Invoice Invoice `json:"invoice" gorm:"foreignKey:InvoiceID"`
}

// Bill represents the bills table
type Bill struct {
	ID             string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID       string          `json:"branchId" gorm:"type:varchar(36);not null;index"`
	VendorID       string          `json:"vendorId" gorm:"type:varchar(36);not null;index"`
	BillNumber     *string         `json:"billNumber" gorm:"type:varchar(100)"`
	VendorBillNo   *string         `json:"vendorBillNo" gorm:"type:varchar(100)"`
	Status         BillStatus      `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	BillDate       time.Time       `json:"billDate" gorm:"not null;index"`
	DueDate        *time.Time      `json:"dueDate" gorm:"index"`
	SubTotal       decimal.Decimal `json:"subTotal" gorm:"type:decimal(15,2);not null;default:0"`
	TaxAmount      decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	DiscountAmount decimal.Decimal `json:"discountAmount" gorm:"type:decimal(15,2);not null;default:0"`
	TotalAmount    decimal.Decimal `json:"totalAmount" gorm:"type:decimal(15,2);not null;default:0"`
	PaidAmount     decimal.Decimal `json:"paidAmount" gorm:"type:decimal(15,2);not null;default:0"`
	BalanceAmount  decimal.Decimal `json:"balanceAmount" gorm:"type:decimal(15,2);not null;default:0"`
	Notes          *string         `json:"notes" gorm:"type:text"`
	CreatedAt      time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt      time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency       string          `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate   decimal.Decimal `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`
	Reference      *string         `json:"reference" gorm:"type:varchar(255)"`

	// Relationships
	Branch         Branch         `json:"branch" gorm:"foreignKey:BranchID"`
	Vendor         Vendor         `json:"vendor" gorm:"foreignKey:VendorID"`
	Items          []BillItem     `json:"items" gorm:"foreignKey:BillID"`
	Payments       []BillPayment  `json:"payments" gorm:"foreignKey:BillID"`
	JournalEntries []JournalEntry `json:"journalEntries" gorm:"foreignKey:SourceID;foreignKey:SourceType"`
}

// BillItem represents the bill_items table
type BillItem struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BillID      string          `json:"billId" gorm:"type:varchar(36);not null;index"`
	Description string          `json:"description" gorm:"type:text;not null"`
	Quantity    decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice   decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TotalPrice  decimal.Decimal `json:"totalPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID   *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	TaxAmount   decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	SortOrder   int             `json:"sortOrder" gorm:"default:0"`

	// Relationships
	Bill    Bill     `json:"bill" gorm:"foreignKey:BillID"`
	TaxRate *TaxRate `json:"taxRate" gorm:"foreignKey:TaxRateID"`
}

// BillPayment represents the bill_payments table
type BillPayment struct {
	ID            string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BillID        string          `json:"billId" gorm:"type:varchar(36);not null;index"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	PaymentDate   time.Time       `json:"paymentDate" gorm:"not null;index"`
	PaymentMethod *string         `json:"paymentMethod" gorm:"type:varchar(100)"`
	Reference     *string         `json:"reference" gorm:"type:varchar(255)"`
	Notes         *string         `json:"notes" gorm:"type:text"`
	CreatedAt     time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Bill Bill `json:"bill" gorm:"foreignKey:BillID"`
}

// Expense represents the expenses table
type Expense struct {
	ID           string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID     string          `json:"branchId" gorm:"type:varchar(36);not null;index"`
	VendorID     *string         `json:"vendorId" gorm:"type:varchar(36);index"`
	AccountID    string          `json:"accountId" gorm:"type:varchar(36);not null;index"`
	Amount       decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	Description  string          `json:"description" gorm:"type:text;not null"`
	ExpenseDate  time.Time       `json:"expenseDate" gorm:"not null;index"`
	Status       ExpenseStatus   `json:"status" gorm:"type:varchar(50);default:'Pending';index"`
	Reference    *string         `json:"reference" gorm:"type:varchar(255)"`
	ReceiptURL   *string         `json:"receiptUrl" gorm:"type:text"`
	Notes        *string         `json:"notes" gorm:"type:text"`
	CreatedAt    time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency     string          `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate decimal.Decimal `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`
	TaxAmount    decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID    *string         `json:"taxRateId" gorm:"type:varchar(36)"`

	// Enhanced Relationships
	BillID            *string `json:"billId" gorm:"type:varchar(36);index"`            // Link to bill if expense comes from a bill
	AssetID           *string `json:"assetId" gorm:"type:varchar(36);index"`           // Link to asset if expense is for asset purchase/maintenance
	EmployeeID        *string `json:"employeeId" gorm:"type:varchar(36);index"`        // Link to employee for employee expenses
	PurchaseOrderID   *string `json:"purchaseOrderId" gorm:"type:varchar(36);index"`   // Link to purchase order
	ProjectID         *string `json:"projectId" gorm:"type:varchar(36);index"`         // Link to project for project tracking
	CostCenter        *string `json:"costCenter" gorm:"type:varchar(100);index"`       // Cost center for departmental tracking
	Department        *string `json:"department" gorm:"type:varchar(100);index"`       // Department classification
	ExpenseCategoryID *string `json:"expenseCategoryId" gorm:"type:varchar(36);index"` // Link to expense category

	// Payment and Approval Tracking
	PaymentMethod *string    `json:"paymentMethod" gorm:"type:varchar(100)"`      // How expense was paid
	BankAccountID *string    `json:"bankAccountId" gorm:"type:varchar(36);index"` // Bank account used for payment
	ApprovedBy    *string    `json:"approvedBy" gorm:"type:varchar(36);index"`    // User who approved
	ApprovedAt    *time.Time `json:"approvedAt"`                                  // When approved
	PaidAt        *time.Time `json:"paidAt"`                                      // When payment was made

	// Additional Financial Fields
	SubTotal       decimal.Decimal `json:"subTotal" gorm:"type:decimal(15,2);not null;default:0"`       // Amount before tax
	DiscountAmount decimal.Decimal `json:"discountAmount" gorm:"type:decimal(15,2);not null;default:0"` // Any discount applied

	// Audit Fields
	CreatedBy         *string `json:"createdBy" gorm:"type:varchar(36);index"`         // User who created expense
	UpdatedBy         *string `json:"updatedBy" gorm:"type:varchar(36);index"`         // User who last updated
	IsRecurring       bool    `json:"isRecurring" gorm:"default:false"`                // Is this a recurring expense
	RecurringParentID *string `json:"recurringParentId" gorm:"type:varchar(36);index"` // Link to parent recurring expense

	// Relationships
	Branch            Branch           `json:"branch" gorm:"foreignKey:BranchID"`
	Vendor            *Vendor          `json:"vendor" gorm:"foreignKey:VendorID"`
	Account           ChartOfAccount   `json:"account" gorm:"foreignKey:AccountID"`
	TaxRate           *TaxRate         `json:"taxRate" gorm:"foreignKey:TaxRateID"`
	Bill              *Bill            `json:"bill" gorm:"foreignKey:BillID"`
	Asset             *Asset           `json:"asset" gorm:"foreignKey:AssetID"`
	Employee          *Employee        `json:"employee" gorm:"foreignKey:EmployeeID"`
	PurchaseOrder     *PurchaseOrder   `json:"purchaseOrder" gorm:"foreignKey:PurchaseOrderID"`
	ExpenseCategory   *ExpenseCategory `json:"expenseCategory" gorm:"foreignKey:ExpenseCategoryID"`
	BankAccount       *BankAccount     `json:"bankAccount" gorm:"foreignKey:BankAccountID"`
	ApproverUser      *User            `json:"approverUser" gorm:"foreignKey:ApprovedBy"`
	CreatorUser       *User            `json:"creatorUser" gorm:"foreignKey:CreatedBy"`
	UpdaterUser       *User            `json:"updaterUser" gorm:"foreignKey:UpdatedBy"`
	RecurringParent   *Expense         `json:"recurringParent" gorm:"foreignKey:RecurringParentID"`
	RecurringChildren []Expense        `json:"recurringChildren" gorm:"foreignKey:RecurringParentID"`
	JournalEntries    []JournalEntry   `json:"journalEntries" gorm:"foreignKey:SourceID;foreignKey:SourceType"`
	ExpensePayments   []ExpensePayment `json:"expensePayments" gorm:"foreignKey:ExpenseID"`
}

// ExpenseCategory represents the expense_categories table
type ExpenseCategory struct {
	ID          string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID    string    `json:"branchId" gorm:"type:varchar(36);not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description *string   `json:"description" gorm:"type:text"`
	ParentID    *string   `json:"parentId" gorm:"type:varchar(36);index"`
	AccountID   *string   `json:"accountId" gorm:"type:varchar(36);index"` // Default account for this category
	IsActive    bool      `json:"isActive" gorm:"default:true;index"`
	SortOrder   int       `json:"sortOrder" gorm:"default:0"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Branch   Branch            `json:"branch" gorm:"foreignKey:BranchID"`
	Parent   *ExpenseCategory  `json:"parent" gorm:"foreignKey:ParentID"`
	Children []ExpenseCategory `json:"children" gorm:"foreignKey:ParentID"`
	Account  *ChartOfAccount   `json:"account" gorm:"foreignKey:AccountID"`
	Expenses []Expense         `json:"expenses" gorm:"foreignKey:ExpenseCategoryID"`
}

// ExpensePayment represents the expense_payments table
type ExpensePayment struct {
	ID            string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	ExpenseID     string          `json:"expenseId" gorm:"type:varchar(36);not null;index"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	PaymentDate   time.Time       `json:"paymentDate" gorm:"not null;index"`
	PaymentMethod *string         `json:"paymentMethod" gorm:"type:varchar(100)"`
	BankAccountID *string         `json:"bankAccountId" gorm:"type:varchar(36);index"`
	Reference     *string         `json:"reference" gorm:"type:varchar(255)"`
	Notes         *string         `json:"notes" gorm:"type:text"`
	CreatedAt     time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	CreatedBy     string          `json:"createdBy" gorm:"type:varchar(36);not null;index"`

	// Relationships
	Expense     Expense      `json:"expense" gorm:"foreignKey:ExpenseID"`
	BankAccount *BankAccount `json:"bankAccount" gorm:"foreignKey:BankAccountID"`
	CreatorUser User         `json:"creatorUser" gorm:"foreignKey:CreatedBy"`
}

// BeforeCreate hooks
func (i *Invoice) BeforeCreate(tx *gorm.DB) error {
	if i.ID == "" {
		i.ID = uuid.New().String()
	}
	return nil
}

func (ii *InvoiceItem) BeforeCreate(tx *gorm.DB) error {
	if ii.ID == "" {
		ii.ID = uuid.New().String()
	}
	return nil
}

func (ip *InvoicePayment) BeforeCreate(tx *gorm.DB) error {
	if ip.ID == "" {
		ip.ID = uuid.New().String()
	}
	return nil
}

func (b *Bill) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (bi *BillItem) BeforeCreate(tx *gorm.DB) error {
	if bi.ID == "" {
		bi.ID = uuid.New().String()
	}
	return nil
}

func (bp *BillPayment) BeforeCreate(tx *gorm.DB) error {
	if bp.ID == "" {
		bp.ID = uuid.New().String()
	}
	return nil
}

func (e *Expense) BeforeCreate(tx *gorm.DB) error {
	if e.ID == "" {
		e.ID = uuid.New().String()
	}
	return nil
}

func (ec *ExpenseCategory) BeforeCreate(tx *gorm.DB) error {
	if ec.ID == "" {
		ec.ID = uuid.New().String()
	}
	return nil
}

func (ep *ExpensePayment) BeforeCreate(tx *gorm.DB) error {
	if ep.ID == "" {
		ep.ID = uuid.New().String()
	}
	return nil
}
