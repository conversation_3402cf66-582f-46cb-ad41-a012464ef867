package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Additional Enums
type AssetStatus string
type DepreciationMethod string
type EmploymentStatus string
type PayrollRunStatus string
type RecurringFrequency string
type RecurringStatus string
type EmailStatus string
type CreditNoteStatus string
type CreditNoteType string
type PaymentReminderStatus string
type BankReconciliationStatus string
type BankTransactionStatus string
type InventoryItemStatus string
type InventoryTransactionType string
type CashFlowItemType string
type CashFlowItemStatus string
type TaxReportType string
type CollectionCaseStatus string
type CollectionActivityType string
type SubscriptionPlan string
type SubscriptionStatus string
type PurchaseOrderStatus string

const (
	AssetStatusActive   AssetStatus = "Active"
	AssetStatusDisposed AssetStatus = "Disposed"
	AssetStatusSold     AssetStatus = "Sold"

	DepreciationMethodStraightLine DepreciationMethod = "StraightLine"
	DepreciationMethodNone         DepreciationMethod = "None"

	EmploymentStatusActive   EmploymentStatus = "Active"
	EmploymentStatusInactive EmploymentStatus = "Inactive"

	PayrollRunStatusDraft     PayrollRunStatus = "Draft"
	PayrollRunStatusProcessed PayrollRunStatus = "Processed"
	PayrollRunStatusArchived  PayrollRunStatus = "Archived"

	RecurringFrequencyWeekly     RecurringFrequency = "Weekly"
	RecurringFrequencyMonthly    RecurringFrequency = "Monthly"
	RecurringFrequencyQuarterly  RecurringFrequency = "Quarterly"
	RecurringFrequencyBiannually RecurringFrequency = "Biannually"
	RecurringFrequencyAnnually   RecurringFrequency = "Annually"
	RecurringFrequencyCustom     RecurringFrequency = "Custom"

	RecurringStatusActive    RecurringStatus = "Active"
	RecurringStatusPaused    RecurringStatus = "Paused"
	RecurringStatusCompleted RecurringStatus = "Completed"

	EmailStatusSent      EmailStatus = "Sent"
	EmailStatusFailed    EmailStatus = "Failed"
	EmailStatusScheduled EmailStatus = "Scheduled"

	CreditNoteStatusDraft  CreditNoteStatus = "Draft"
	CreditNoteStatusOpen   CreditNoteStatus = "Open"
	CreditNoteStatusClosed CreditNoteStatus = "Closed"
	CreditNoteStatusVoid   CreditNoteStatus = "Void"

	CreditNoteTypeCustomerRefund CreditNoteType = "CustomerRefund"
	CreditNoteTypeSupplierRefund CreditNoteType = "SupplierRefund"
	CreditNoteTypeWriteOff       CreditNoteType = "WriteOff"
	CreditNoteTypeReturnCredit   CreditNoteType = "ReturnCredit"
	CreditNoteTypeAdjustment     CreditNoteType = "Adjustment"

	PaymentReminderStatusScheduled PaymentReminderStatus = "Scheduled"
	PaymentReminderStatusSent      PaymentReminderStatus = "Sent"
	PaymentReminderStatusCancelled PaymentReminderStatus = "Cancelled"

	BankReconciliationStatusInProgress BankReconciliationStatus = "InProgress"
	BankReconciliationStatusCompleted  BankReconciliationStatus = "Completed"

	BankTransactionStatusUnreconciled BankTransactionStatus = "Unreconciled"
	BankTransactionStatusReconciled   BankTransactionStatus = "Reconciled"
	BankTransactionStatusAdjusted     BankTransactionStatus = "Adjusted"

	InventoryItemStatusActive   InventoryItemStatus = "Active"
	InventoryItemStatusInactive InventoryItemStatus = "Inactive"

	InventoryTransactionTypePurchase   InventoryTransactionType = "Purchase"
	InventoryTransactionTypeSale       InventoryTransactionType = "Sale"
	InventoryTransactionTypeAdjustment InventoryTransactionType = "Adjustment"
	InventoryTransactionTypeTransfer   InventoryTransactionType = "Transfer"
	InventoryTransactionTypeWaste      InventoryTransactionType = "Waste"

	CashFlowItemTypeInflow  CashFlowItemType = "Inflow"
	CashFlowItemTypeOutflow CashFlowItemType = "Outflow"

	CashFlowItemStatusProjected CashFlowItemStatus = "Projected"
	CashFlowItemStatusConfirmed CashFlowItemStatus = "Confirmed"
	CashFlowItemStatusCompleted CashFlowItemStatus = "Completed"

	TaxReportTypeSales   TaxReportType = "Sales"
	TaxReportTypeIncome  TaxReportType = "Income"
	TaxReportTypePayroll TaxReportType = "Payroll"

	CollectionCaseStatusNew        CollectionCaseStatus = "New"
	CollectionCaseStatusInProgress CollectionCaseStatus = "InProgress"
	CollectionCaseStatusOnHold     CollectionCaseStatus = "OnHold"
	CollectionCaseStatusResolved   CollectionCaseStatus = "Resolved"
	CollectionCaseStatusClosed     CollectionCaseStatus = "Closed"

	CollectionActivityTypeCall    CollectionActivityType = "Call"
	CollectionActivityTypeEmail   CollectionActivityType = "Email"
	CollectionActivityTypeLetter  CollectionActivityType = "Letter"
	CollectionActivityTypeMeeting CollectionActivityType = "Meeting"
	CollectionActivityTypePayment CollectionActivityType = "Payment"
	CollectionActivityTypeNote    CollectionActivityType = "Note"

	SubscriptionPlanFree         SubscriptionPlan = "Free"
	SubscriptionPlanStandard     SubscriptionPlan = "Standard"
	SubscriptionPlanProfessional SubscriptionPlan = "Professional"
	SubscriptionPlanEnterprise   SubscriptionPlan = "Enterprise"

	SubscriptionStatusActive     SubscriptionStatus = "Active"
	SubscriptionStatusPastDue    SubscriptionStatus = "PastDue"
	SubscriptionStatusCanceled   SubscriptionStatus = "Canceled"
	SubscriptionStatusTrialing   SubscriptionStatus = "Trialing"
	SubscriptionStatusIncomplete SubscriptionStatus = "Incomplete"

	PurchaseOrderStatusDraft     PurchaseOrderStatus = "Draft"
	PurchaseOrderStatusSent      PurchaseOrderStatus = "Sent"
	PurchaseOrderStatusConfirmed PurchaseOrderStatus = "Confirmed"
	PurchaseOrderStatusPartial   PurchaseOrderStatus = "Partial"
	PurchaseOrderStatusReceived  PurchaseOrderStatus = "Received"
	PurchaseOrderStatusCancelled PurchaseOrderStatus = "Cancelled"
	PurchaseOrderStatusClosed    PurchaseOrderStatus = "Closed"
)

// Asset represents the assets table
type Asset struct {
	ID                      string             `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID                string             `json:"branchId" gorm:"type:varchar(36);not null;index"`
	Name                    string             `json:"name" gorm:"type:varchar(255);not null"`
	Description             *string            `json:"description" gorm:"type:text"`
	PurchaseDate            time.Time          `json:"purchaseDate" gorm:"not null;index"`
	PurchasePrice           decimal.Decimal    `json:"purchasePrice" gorm:"type:decimal(15,2);not null"`
	CurrentValue            decimal.Decimal    `json:"currentValue" gorm:"type:decimal(15,2);not null"`
	DepreciationMethod      DepreciationMethod `json:"depreciationMethod" gorm:"type:varchar(50);default:'StraightLine'"`
	UsefulLife              *int               `json:"usefulLife" gorm:"comment:in years"`
	SalvageValue            decimal.Decimal    `json:"salvageValue" gorm:"type:decimal(15,2);default:0"`
	AccumulatedDepreciation decimal.Decimal    `json:"accumulatedDepreciation" gorm:"type:decimal(15,2);default:0"`
	Status                  AssetStatus        `json:"status" gorm:"type:varchar(50);default:'Active';index"`
	Location                *string            `json:"location" gorm:"type:varchar(255)"`
	SerialNumber            *string            `json:"serialNumber" gorm:"type:varchar(255)"`
	CustodianID             *string            `json:"custodianId" gorm:"type:varchar(36);index"`
	Notes                   *string            `json:"notes" gorm:"type:text"`
	CreatedAt               time.Time          `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt               time.Time          `json:"updatedAt" gorm:"autoUpdateTime"`
	DisposalDate            *time.Time         `json:"disposalDate"`
	DisposalValue           *decimal.Decimal   `json:"disposalValue" gorm:"type:decimal(15,2)"`
	DisposalMethod          *string            `json:"disposalMethod" gorm:"type:varchar(100)"`

	// Relationships
	Branch    Branch    `json:"branch" gorm:"foreignKey:BranchID"`
	Custodian *User     `json:"custodian" gorm:"foreignKey:CustodianID"`
	Expenses  []Expense `json:"expenses" gorm:"foreignKey:AssetID"`
}

// BankAccount represents the bank_accounts table (matching actual database schema)
type BankAccount struct {
	ID                  string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID          *string    `json:"merchantId" gorm:"type:varchar(36);index"`
	BranchID            string     `json:"branchId" gorm:"type:varchar(36);not null;index"`
	BankName            string     `json:"bankName" gorm:"type:varchar(255);not null"`
	AccountName         string     `json:"accountName" gorm:"type:varchar(255);not null"`
	AccountNumber       string     `json:"accountNumber" gorm:"type:varchar(100);not null"`
	RoutingNumber       *string    `json:"routingNumber" gorm:"type:varchar(50)"`
	AccountType         *string    `json:"accountType" gorm:"type:varchar(50);default:'Checking'"`
	Currency            *string    `json:"currency" gorm:"type:varchar(3);default:'USD'"`
	IsActive            *bool      `json:"isActive" gorm:"default:true"`
	CreatedAt           *time.Time `json:"createdAt" gorm:"type:timestamptz"`
	UpdatedAt           *time.Time `json:"updatedAt" gorm:"type:timestamptz"`
	Balance             *string    `json:"balance" gorm:"type:decimal(15,2);default:'0'"`
	Description         *string    `json:"description" gorm:"type:text"`
	AccountNumberMasked *string    `json:"accountNumberMasked" gorm:"type:varchar(50)"`
	ChartOfAccountID    string     `json:"chartOfAccountId" gorm:"type:varchar(36);not null;unique"`
	Metadata            *string    `json:"metadata" gorm:"type:text"`

	// Relationships
	Branch           Branch               `json:"branch" gorm:"foreignKey:BranchID"`
	ChartOfAccount   ChartOfAccount       `json:"chartOfAccount" gorm:"foreignKey:ChartOfAccountID"`
	BankTransactions []BankTransaction    `json:"bankTransactions" gorm:"foreignKey:BankAccountID"`
	Reconciliations  []BankReconciliation `json:"reconciliations" gorm:"foreignKey:BankAccountID"`
	Expenses         []Expense            `json:"expenses" gorm:"foreignKey:BankAccountID"`
	ExpensePayments  []ExpensePayment     `json:"expensePayments" gorm:"foreignKey:BankAccountID"`
}

// BankTransaction represents the bank_transactions table
type BankTransaction struct {
	ID            string                `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BankAccountID string                `json:"bankAccountId" gorm:"type:varchar(36);not null;index"`
	Date          time.Time             `json:"date" gorm:"not null;index"`
	Description   string                `json:"description" gorm:"type:text;not null"`
	Amount        decimal.Decimal       `json:"amount" gorm:"type:decimal(15,2);not null"`
	Balance       decimal.Decimal       `json:"balance" gorm:"type:decimal(15,2);not null"`
	Reference     *string               `json:"reference" gorm:"type:varchar(255)"`
	Status        BankTransactionStatus `json:"status" gorm:"type:varchar(50);default:'Unreconciled';index"`
	CreatedAt     time.Time             `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt     time.Time             `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	BankAccount BankAccount            `json:"bankAccount" gorm:"foreignKey:BankAccountID"`
	Matches     []BankTransactionMatch `json:"matches" gorm:"foreignKey:BankTransactionID"`
}

// BankReconciliation represents the bank_reconciliations table
type BankReconciliation struct {
	ID               string                   `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BranchID         string                   `json:"branchId" gorm:"type:varchar(36);not null;index"`
	BankAccountID    string                   `json:"bankAccountId" gorm:"type:varchar(36);not null;index"`
	StartDate        time.Time                `json:"startDate" gorm:"not null"`
	EndDate          time.Time                `json:"endDate" gorm:"not null"`
	StatementBalance decimal.Decimal          `json:"statementBalance" gorm:"type:decimal(15,2);not null"`
	BookBalance      decimal.Decimal          `json:"bookBalance" gorm:"type:decimal(15,2);not null"`
	Status           BankReconciliationStatus `json:"status" gorm:"type:varchar(50);default:'InProgress';index"`
	CreatedAt        time.Time                `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt        time.Time                `json:"updatedAt" gorm:"autoUpdateTime"`
	Notes            *string                  `json:"notes" gorm:"type:text"`

	// Relationships
	Branch      Branch      `json:"branch" gorm:"foreignKey:BranchID"`
	BankAccount BankAccount `json:"bankAccount" gorm:"foreignKey:BankAccountID"`
}

// BankTransactionMatch represents the bank_transaction_matches table
type BankTransactionMatch struct {
	ID                string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BankTransactionID string          `json:"bankTransactionId" gorm:"type:varchar(36);not null;index"`
	JournalEntryID    string          `json:"journalEntryId" gorm:"type:varchar(36);not null;index"`
	Amount            decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	CreatedAt         time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	BankTransaction BankTransaction `json:"bankTransaction" gorm:"foreignKey:BankTransactionID"`
	JournalEntry    JournalEntry    `json:"journalEntry" gorm:"foreignKey:JournalEntryID"`
}

// BeforeCreate hooks
func (a *Asset) BeforeCreate(tx *gorm.DB) error {
	if a.ID == "" {
		a.ID = uuid.New().String()
	}
	return nil
}

func (b *BankAccount) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (b *BankTransaction) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (b *BankReconciliation) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (b *BankTransactionMatch) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}
