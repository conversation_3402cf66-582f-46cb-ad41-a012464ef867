package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// CustomerPortalHandler handles customer portal-related HTTP requests
type CustomerPortalHandler struct {
	invoiceService           *services.InvoiceService
	customerService          *services.CustomerService
	customerStatementService *services.CustomerStatementService
	pdfService               *services.PDFService
	emailService             *services.EmailService
	paymentService           *services.PaymentService
}

// NewCustomerPortalHandler creates a new CustomerPortalHandler
func NewCustomerPortalHandler(
	invoiceService *services.InvoiceService,
	customerService *services.CustomerService,
	customerStatementService *services.CustomerStatementService,
	pdfService *services.PDFService,
	emailService *services.EmailService,
	paymentService *services.PaymentService,
) *CustomerPortalHandler {
	return &CustomerPortalHandler{
		invoiceService:           invoiceService,
		customerService:          customerService,
		customerStatementService: customerStatementService,
		pdfService:               pdfService,
		emailService:             emailService,
		paymentService:           paymentService,
	}
}

// CustomerPortalLoginRequest represents the login request for customer portal
type CustomerPortalLoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// CustomerPortalLoginResponse represents the login response for customer portal
type CustomerPortalLoginResponse struct {
	Success  bool                    `json:"success"`
	Message  string                  `json:"message"`
	Customer *CustomerPortalCustomer `json:"customer,omitempty"`
	Token    string                  `json:"token,omitempty"`
}

// CustomerPortalCustomer represents customer data for portal
type CustomerPortalCustomer struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// PaymentRequest represents a payment request from customer portal
type PaymentRequest struct {
	InvoiceID     string          `json:"invoice_id" binding:"required"`
	CustomerID    string          `json:"customer_id" binding:"required"`
	Amount        decimal.Decimal `json:"amount" binding:"required"`
	PaymentMethod string          `json:"payment_method" binding:"required"`
	PaymentDate   time.Time       `json:"payment_date"`
	Notes         string          `json:"notes"`
}

// PaymentResponse represents a payment response
type PaymentResponse struct {
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Payment *models.InvoicePayment `json:"payment,omitempty"`
}

// CustomerPortalLogin handles customer portal login
func (h *CustomerPortalHandler) CustomerPortalLogin(c *gin.Context) {
	var req CustomerPortalLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, CustomerPortalLoginResponse{
			Success: false,
			Message: "Invalid request data",
		})
		return
	}

	// Find customer by email
	customer, err := h.customerService.FindByEmail(req.Email)
	if err != nil {
		c.JSON(http.StatusUnauthorized, CustomerPortalLoginResponse{
			Success: false,
			Message: "Invalid email or password",
		})
		return
	}

	// For demo purposes, we'll accept any non-empty password
	// In production, you would validate against a hashed password
	if req.Password == "" {
		c.JSON(http.StatusUnauthorized, CustomerPortalLoginResponse{
			Success: false,
			Message: "Invalid email or password",
		})
		return
	}

	// Generate a simple token (in production, use JWT)
	token := "customer_token_" + customer.ID

	c.JSON(http.StatusOK, CustomerPortalLoginResponse{
		Success: true,
		Message: "Login successful",
		Customer: &CustomerPortalCustomer{
			ID:    customer.ID,
			Name:  customer.Name,
			Email: *customer.Email,
		},
		Token: token,
	})
}

// GetCustomerInvoices retrieves invoices for a specific customer
func (h *CustomerPortalHandler) GetCustomerInvoices(c *gin.Context) {
	customerID := c.Param("customerId")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	// Verify customer exists
	customer, err := h.customerService.GetCustomerByID(customerID, "")
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "Customer not found"})
		return
	}

	// Get invoices for this customer
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 100
	}

	invoices, _, err := h.invoiceService.GetInvoicesByCustomer(customer.BranchID, customerID, page, limit) // TODO: Update response struct to use BranchID
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, invoices)
}

// GetCustomerInvoiceByID retrieves a specific invoice for a customer
func (h *CustomerPortalHandler) GetCustomerInvoiceByID(c *gin.Context) {
	customerID := c.Param("customerId")
	invoiceID := c.Param("invoiceId")

	if customerID == "" || invoiceID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID and Invoice ID are required"})
		return
	}

	// Get invoice and verify it belongs to the customer
	invoice, err := h.invoiceService.GetInvoiceByID(invoiceID, "")
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "Invoice not found"})
		return
	}

	// Verify the invoice belongs to the customer
	if invoice.CustomerID != customerID {
		c.JSON(http.StatusForbidden, ErrorResponse{Error: "Access denied"})
		return
	}

	c.JSON(http.StatusOK, invoice)
}

// GetCustomerStatements retrieves statements for a specific customer
func (h *CustomerPortalHandler) GetCustomerStatements(c *gin.Context) {
	customerID := c.Param("customerId")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	// Verify customer exists
	_, err := h.customerService.GetCustomerByID(customerID, "")
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "Customer not found"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 100
	}

	statements, _, err := h.customerStatementService.GetCustomerStatementsByCustomer(customerID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, statements)
}

// ProcessPayment handles payment processing for customer portal
func (h *CustomerPortalHandler) ProcessPayment(c *gin.Context) {
	var req PaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, PaymentResponse{
			Success: false,
			Message: "Invalid request data",
		})
		return
	}

	// Verify invoice exists and belongs to customer
	invoice, err := h.invoiceService.GetInvoiceByID(req.InvoiceID, "")
	if err != nil {
		c.JSON(http.StatusNotFound, PaymentResponse{
			Success: false,
			Message: "Invoice not found",
		})
		return
	}

	if invoice.CustomerID != req.CustomerID {
		c.JSON(http.StatusForbidden, PaymentResponse{
			Success: false,
			Message: "Access denied",
		})
		return
	}

	// Validate payment amount
	if req.Amount.LessThanOrEqual(decimal.Zero) || req.Amount.GreaterThan(invoice.TotalAmount) {
		c.JSON(http.StatusBadRequest, PaymentResponse{
			Success: false,
			Message: "Invalid payment amount",
		})
		return
	}

	// Set payment date if not provided
	if req.PaymentDate.IsZero() {
		req.PaymentDate = time.Now()
	}

	// Create payment record
	payment := &models.InvoicePayment{
		InvoiceID:     req.InvoiceID,
		Amount:        req.Amount,
		PaymentDate:   req.PaymentDate,
		PaymentMethod: &req.PaymentMethod,
		Notes:         &req.Notes,
	}

	// Add payment to invoice
	err = h.invoiceService.AddPaymentToInvoice(req.InvoiceID, payment, "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, PaymentResponse{
			Success: false,
			Message: "Failed to process payment",
		})
		return
	}

	c.JSON(http.StatusOK, PaymentResponse{
		Success: true,
		Message: "Payment processed successfully",
		Payment: payment,
	})
}

// GetCustomerBalance retrieves the current balance for a customer
func (h *CustomerPortalHandler) GetCustomerBalance(c *gin.Context) {
	customerID := c.Param("customerId")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	balance, err := h.customerStatementService.GetCustomerBalance(customerID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"customerId": customerID,
		"balance":    balance.String(),
	})
}
