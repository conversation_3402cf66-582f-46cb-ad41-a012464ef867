package handlers

import (
	"io"
	"net/http"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// PaymentHandler handles payment processing requests
type PaymentHandler struct {
	paymentService *services.PaymentService
}

// NewPaymentHandler creates a new PaymentHandler
func NewPaymentHandler(paymentService *services.PaymentService) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
	}
}

// CreatePaymentIntent creates a Stripe payment intent
func (h *PaymentHandler) CreatePaymentIntent(c *gin.Context) {
	var req services.PaymentIntentRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, services.PaymentIntentResponse{
			Success: false,
			Message: "Invalid request data",
		})
		return
	}

	response, err := h.paymentService.CreatePaymentIntent(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, services.PaymentIntentResponse{
			Success: false,
			Message: "Internal server error",
		})
		return
	}

	if !response.Success {
		c.JSON(http.StatusBadRequest, response)
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreateSetupIntent creates a Stripe setup intent
func (h *PaymentHandler) CreateSetupIntent(c *gin.Context) {
	var req services.SetupIntentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, services.SetupIntentResponse{
			Success: false,
			Message: "Invalid request data",
		})
		return
	}

	response, err := h.paymentService.CreateSetupIntent(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, services.SetupIntentResponse{
			Success: false,
			Message: "Internal server error",
		})
		return
	}

	if !response.Success {
		c.JSON(http.StatusBadRequest, response)
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPaymentMethods retrieves saved payment methods for a customer
func (h *PaymentHandler) GetPaymentMethods(c *gin.Context) {
	customerID := c.Query("customer_id")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, services.PaymentMethodsResponse{
			Success: false,
			Message: "Customer ID is required",
		})
		return
	}

	response, err := h.paymentService.GetPaymentMethods(customerID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, services.PaymentMethodsResponse{
			Success: false,
			Message: "Internal server error",
		})
		return
	}

	if !response.Success {
		c.JSON(http.StatusBadRequest, response)
		return
	}

	c.JSON(http.StatusOK, response)
}

// DetachPaymentMethod removes a saved payment method
func (h *PaymentHandler) DetachPaymentMethod(c *gin.Context) {
	paymentMethodID := c.Query("payment_method_id")
	if paymentMethodID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Payment method ID is required",
		})
		return
	}

	if err := h.paymentService.DetachPaymentMethod(paymentMethodID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to remove payment method",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Payment method removed successfully",
	})
}

// HandleWebhook handles Stripe webhooks
func (h *PaymentHandler) HandleWebhook(c *gin.Context) {
	// Read the request body
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to read request body",
		})
		return
	}

	// Get the Stripe signature header
	signature := c.GetHeader("Stripe-Signature")
	if signature == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing Stripe signature",
		})
		return
	}

	// Validate webhook signature
	if !h.paymentService.ValidateWebhookSignature(payload, signature) {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid webhook signature",
		})
		return
	}

	// Process webhook
	if err := h.paymentService.HandleWebhook(payload, signature); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to process webhook",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"received": true,
	})
}

// ProcessPaymentSuccess handles successful payment processing (for testing)
func (h *PaymentHandler) ProcessPaymentSuccess(c *gin.Context) {
	paymentIntentID := c.Param("paymentIntentId")
	if paymentIntentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Payment intent ID is required",
		})
		return
	}

	if err := h.paymentService.ProcessPaymentSuccess(paymentIntentID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to process payment success",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Payment processed successfully",
	})
}
