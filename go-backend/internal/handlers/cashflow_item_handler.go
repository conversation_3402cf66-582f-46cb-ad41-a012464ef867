package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type CashFlowItemHandler struct {
	cashFlowItemService *services.CashFlowItemService
}

func NewCashFlowItemHandler(cashFlowItemService *services.CashFlowItemService) *CashFlowItemHandler {
	return &CashFlowItemHandler{
		cashFlowItemService: cashFlowItemService,
	}
}

// GetCashFlowItems godoc
// @Summary Get all cash flow items
// @Description Get all cash flow items with pagination and filtering
// @Tags cashflow
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(50)
// @Param type query string false "Cash flow type (Inflow/Outflow)"
// @Param status query string false "Cash flow status"
// @Param category query string false "Category filter"
// @Param start_date query string false "Start date filter (YYYY-MM-DD)"
// @Param end_date query string false "End date filter (YYYY-MM-DD)"
// @Success 200 {object} PaginatedResponse{data=[]models.CashFlowItem}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/items [get]
func (h *CashFlowItemHandler) GetCashFlowItems(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	// Get filter parameters
	filters := services.CashFlowItemFilters{
		Type:      c.Query("type"),
		Status:    c.Query("status"),
		Category:  c.Query("category"),
		StartDate: c.Query("start_date"),
		EndDate:   c.Query("end_date"),
	}

	items, total, err := h.cashFlowItemService.GetAllCashFlowItems(page, limit, userID, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       items,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetCashFlowItem godoc
// @Summary Get cash flow item by ID
// @Description Get a specific cash flow item by ID
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Cash Flow Item ID"
// @Success 200 {object} models.CashFlowItem
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/items/{id} [get]
func (h *CashFlowItemHandler) GetCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Cash flow item ID is required"})
		return
	}

	item, err := h.cashFlowItemService.GetCashFlowItemByID(id, userID)
	if err != nil {
		if err.Error() == "cash flow item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// CreateCashFlowItem godoc
// @Summary Create a new cash flow item
// @Description Create a new cash flow item
// @Tags cashflow
// @Accept json
// @Produce json
// @Param item body services.CreateCashFlowItemRequest true "Cash flow item data"
// @Success 201 {object} models.CashFlowItem
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/items [post]
func (h *CashFlowItemHandler) CreateCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateCashFlowItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.cashFlowItemService.CreateCashFlowItem(req, userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, item)
}

// UpdateCashFlowItem godoc
// @Summary Update a cash flow item
// @Description Update an existing cash flow item
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Cash Flow Item ID"
// @Param item body services.UpdateCashFlowItemRequest true "Cash flow item data"
// @Success 200 {object} models.CashFlowItem
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/items/{id} [put]
func (h *CashFlowItemHandler) UpdateCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Cash flow item ID is required"})
		return
	}

	var req services.UpdateCashFlowItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.cashFlowItemService.UpdateCashFlowItem(id, req, userID)
	if err != nil {
		if err.Error() == "cash flow item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// DeleteCashFlowItem godoc
// @Summary Delete a cash flow item
// @Description Delete a cash flow item
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Cash Flow Item ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/items/{id} [delete]
func (h *CashFlowItemHandler) DeleteCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Cash flow item ID is required"})
		return
	}

	err := h.cashFlowItemService.DeleteCashFlowItem(id, userID)
	if err != nil {
		if err.Error() == "cash flow item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// Alias methods for route compatibility
func (h *CashFlowItemHandler) GetAllCashFlowItems(c *gin.Context) {
	h.GetCashFlowItems(c)
}

func (h *CashFlowItemHandler) GetCashFlowItemByID(c *gin.Context) {
	h.GetCashFlowItem(c)
}
