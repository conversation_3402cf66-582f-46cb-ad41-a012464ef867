package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type CashFlowForecastHandler struct {
	cashFlowForecastService *services.CashFlowForecastService
}

func NewCashFlowForecastHandler(cashFlowForecastService *services.CashFlowForecastService) *CashFlowForecastHandler {
	return &CashFlowForecastHandler{
		cashFlowForecastService: cashFlowForecastService,
	}
}

// GetCashFlowForecast godoc
// @Summary Get cash flow forecast
// @Description Get cash flow forecast for specified periods
// @Tags cashflow
// @Accept json
// @Produce json
// @Param period_type query string false "Period type (week/month)" default(week)
// @Param periods query int false "Number of periods" default(8)
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Success 200 {object} services.CashFlowForecastResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/forecast [get]
func (h *CashFlowForecastHandler) GetCashFlowForecast(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	// Get query parameters
	periodType := c.DefaultQuery("period_type", "week")
	periods, _ := strconv.Atoi(c.DefaultQuery("periods", "8"))
	startDate := c.Query("start_date")

	// Validate parameters
	if periodType != "week" && periodType != "month" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "period_type must be 'week' or 'month'"})
		return
	}

	if periods < 1 || periods > 52 {
		periods = 8
	}

	req := services.CashFlowForecastRequest{
		PeriodType: periodType,
		Periods:    periods,
		StartDate:  startDate,
	}

	forecast, err := h.cashFlowForecastService.GenerateForecast(req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, forecast)
}

// GetCashFlowHistory godoc
// @Summary Get historical cash flow data
// @Description Get historical cash flow data for analysis
// @Tags cashflow
// @Accept json
// @Produce json
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Param category query string false "Category filter"
// @Param type query string false "Type filter (Inflow/Outflow)"
// @Success 200 {object} services.CashFlowHistoryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/history [get]
func (h *CashFlowForecastHandler) GetCashFlowHistory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	req := services.CashFlowHistoryRequest{
		StartDate: c.Query("start_date"),
		EndDate:   c.Query("end_date"),
		Category:  c.Query("category"),
		Type:      c.Query("type"),
	}

	history, err := h.cashFlowForecastService.GetHistory(req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, history)
}

// GetBudgetComparison godoc
// @Summary Get budget comparison
// @Description Get cash flow budget comparison data
// @Tags cashflow
// @Accept json
// @Produce json
// @Param year query int true "Year for comparison"
// @Param month query int false "Month for comparison (1-12)"
// @Success 200 {object} services.BudgetComparisonResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/budget-comparison [get]
func (h *CashFlowForecastHandler) GetBudgetComparison(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	year, err := strconv.Atoi(c.Query("year"))
	if err != nil || year < 2000 || year > 2100 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Valid year is required"})
		return
	}

	month := 0
	if monthStr := c.Query("month"); monthStr != "" {
		month, err = strconv.Atoi(monthStr)
		if err != nil || month < 1 || month > 12 {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Month must be between 1 and 12"})
			return
		}
	}

	req := services.BudgetComparisonRequest{
		Year:  year,
		Month: month,
	}

	comparison, err := h.cashFlowForecastService.GetBudgetComparison(req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, comparison)
}
