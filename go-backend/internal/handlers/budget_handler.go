package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// BudgetHandler handles budget-related HTTP requests
type BudgetHandler struct {
	budgetService *services.BudgetService
}

// NewBudgetHandler creates a new BudgetHandler
func NewBudgetHandler(budgetService *services.BudgetService) *BudgetHandler {
	return &BudgetHandler{
		budgetService: budgetService,
	}
}

// CreateBudgetItemRequest represents the request payload for creating a budget item
type CreateBudgetItemRequest struct {
	BranchID  string          `json:"branchId,omitempty"`
	AccountID string          `json:"accountId" binding:"required"`
	Year      int             `json:"year" binding:"required"`
	Month     int             `json:"month" binding:"required"`
	Amount    decimal.Decimal `json:"amount" binding:"required"`
	Notes     *string         `json:"notes,omitempty"`
}

// UpdateBudgetItemRequest represents the request payload for updating a budget item
type UpdateBudgetItemRequest struct {
	AccountID *string          `json:"accountId,omitempty"`
	Year      *int             `json:"year,omitempty"`
	Month     *int             `json:"month,omitempty"`
	Amount    *decimal.Decimal `json:"amount,omitempty"`
	Notes     *string          `json:"notes,omitempty"`
}

// GetBudgetItems retrieves budget items with branch-based filtering (for frontend)
func (h *BudgetHandler) GetBudgetItems(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	accountID := c.Query("accountId")
	branchID := c.Query("branchId")
	organizationID := c.Query("organizationId")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse year and month filters
	var year, month *int
	if yearStr := c.Query("year"); yearStr != "" {
		if parsed, err := strconv.Atoi(yearStr); err == nil {
			year = &parsed
		}
	}
	if monthStr := c.Query("month"); monthStr != "" {
		if parsed, err := strconv.Atoi(monthStr); err == nil && parsed >= 1 && parsed <= 12 {
			month = &parsed
		}
	}

	// Use branch-based filtering if branchID is provided, otherwise use organization-based
	var budgetItems []models.BudgetItem
	var total int64
	var err error

	if branchID != "" {
		budgetItems, total, err = h.budgetService.GetBudgetItemsByBranch(branchID, page, limit, search, accountID, year, month)
	} else if organizationID != "" {
		budgetItems, total, err = h.budgetService.GetBudgetItemsByOrganization(organizationID, page, limit, search, accountID, year, month)
	} else {
		budgetItems, total, err = h.budgetService.GetAllBudgetItems(page, limit, search)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"data": budgetItems,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetAllBudgetItems retrieves all budget items with pagination
func (h *BudgetHandler) GetAllBudgetItems(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	budgetItems, total, err := h.budgetService.GetAllBudgetItems(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"budgetItems": budgetItems,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetBudgetItemByID retrieves a budget item by ID
func (h *BudgetHandler) GetBudgetItemByID(c *gin.Context) {
	id := c.Param("id")

	budgetItem, err := h.budgetService.GetBudgetItemByID(id)
	if err != nil {
		if err.Error() == "budget item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, budgetItem)
}

// GetBudgetItemsByMerchant retrieves budget items for a specific merchant
func (h *BudgetHandler) GetBudgetItemsByMerchant(c *gin.Context) {
	merchantID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	accountID := c.Query("accountId")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse year and month filters
	var year, month *int
	if yearStr := c.Query("year"); yearStr != "" {
		if parsed, err := strconv.Atoi(yearStr); err == nil {
			year = &parsed
		}
	}
	if monthStr := c.Query("month"); monthStr != "" {
		if parsed, err := strconv.Atoi(monthStr); err == nil && parsed >= 1 && parsed <= 12 {
			month = &parsed
		}
	}

	budgetItems, total, err := h.budgetService.GetBudgetItemsByMerchant(merchantID, page, limit, search, accountID, year, month)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"budgetItems": budgetItems,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetBudgetItemsByAccount retrieves budget items for a specific account
func (h *BudgetHandler) GetBudgetItemsByAccount(c *gin.Context) {
	accountID := c.Param("accountId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse year and month filters
	var year, month *int
	if yearStr := c.Query("year"); yearStr != "" {
		if parsed, err := strconv.Atoi(yearStr); err == nil {
			year = &parsed
		}
	}
	if monthStr := c.Query("month"); monthStr != "" {
		if parsed, err := strconv.Atoi(monthStr); err == nil && parsed >= 1 && parsed <= 12 {
			month = &parsed
		}
	}

	budgetItems, total, err := h.budgetService.GetBudgetItemsByAccount(accountID, page, limit, year, month)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"budgetItems": budgetItems,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateBudgetItem creates a new budget item
func (h *BudgetHandler) CreateBudgetItem(c *gin.Context) {
	var req CreateBudgetItemRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get branchID from request body or query parameters
	branchID := req.BranchID
	if branchID == "" {
		branchID = c.Query("branchId")
	}
	if branchID == "" {
		// Fallback to legacy merchantId parameter
		branchID = c.Param("merchantId")
	}

	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "branchId is required"})
		return
	}

	// Create budget item
	budgetItem := &models.BudgetItem{
		BranchID:  branchID,
		AccountID: req.AccountID,
		Year:      req.Year,
		Month:     req.Month,
		Amount:    req.Amount,
		Notes:     req.Notes,
	}

	// Validate budget item
	if err := h.budgetService.ValidateBudgetItem(budgetItem); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create the budget item
	if err := h.budgetService.CreateBudgetItem(budgetItem); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created budget item with relationships
	createdItem, err := h.budgetService.GetBudgetItemByID(budgetItem.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created budget item: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdItem)
}

// UpdateBudgetItem updates an existing budget item
func (h *BudgetHandler) UpdateBudgetItem(c *gin.Context) {
	id := c.Param("id")
	var req UpdateBudgetItemRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.BudgetItem{}
	if req.AccountID != nil {
		updates.AccountID = *req.AccountID
	}
	if req.Year != nil {
		updates.Year = *req.Year
	}
	if req.Month != nil {
		updates.Month = *req.Month
	}
	if req.Amount != nil {
		updates.Amount = *req.Amount
	}
	if req.Notes != nil {
		updates.Notes = req.Notes
	}

	updatedItem, err := h.budgetService.UpdateBudgetItem(id, updates)
	if err != nil {
		if err.Error() == "budget item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedItem)
}

// DeleteBudgetItem deletes a budget item
func (h *BudgetHandler) DeleteBudgetItem(c *gin.Context) {
	id := c.Param("id")

	if err := h.budgetService.DeleteBudgetItem(id); err != nil {
		if err.Error() == "budget item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Budget item deleted successfully"})
}

// GetBudgetReport generates a budget report for a specific period
func (h *BudgetHandler) GetBudgetReport(c *gin.Context) {
	// Get parameters from query string (for new API format)
	yearStr := c.Query("year")
	monthStr := c.Query("month")
	branchID := c.Query("branchId")
	organizationID := c.Query("organizationId")

	// Fallback to path parameters (for legacy API format)
	if yearStr == "" {
		yearStr = c.Param("year")
	}
	if monthStr == "" {
		monthStr = c.Param("month")
	}
	if branchID == "" {
		branchID = c.Param("merchantId") // Legacy support
	}

	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 1900 || year > 2100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Year must be between 1900 and 2100"})
		return
	}

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Month must be between 1 and 12"})
		return
	}

	// Use branch-based filtering if branchID is provided, otherwise use organization-based
	var report interface{}
	if branchID != "" {
		report, err = h.budgetService.GetBudgetReport(branchID, year, month)
	} else if organizationID != "" {
		report, err = h.budgetService.GetBudgetReportByOrganization(organizationID, year, month)
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Either branchId or organizationId is required"})
		return
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, report)
}

// GetBudgetSummary gets summary statistics for budget items
func (h *BudgetHandler) GetBudgetSummary(c *gin.Context) {
	merchantID := c.Param("merchantId")

	summary, err := h.budgetService.GetBudgetSummary(merchantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// UpdateActualAmounts updates actual amounts for budget items based on transactions
func (h *BudgetHandler) UpdateActualAmounts(c *gin.Context) {
	merchantID := c.Param("merchantId")
	year, _ := strconv.Atoi(c.Param("year"))
	month, _ := strconv.Atoi(c.Param("month"))

	if year < 1900 || year > 2100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Year must be between 1900 and 2100"})
		return
	}
	if month < 1 || month > 12 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Month must be between 1 and 12"})
		return
	}

	if err := h.budgetService.UpdateActualAmounts(merchantID, year, month); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Actual amounts updated successfully"})
}
