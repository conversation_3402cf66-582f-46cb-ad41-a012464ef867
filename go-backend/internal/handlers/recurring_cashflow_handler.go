package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type RecurringCashFlowHandler struct {
	recurringCashFlowService *services.RecurringCashFlowItemService
}

func NewRecurringCashFlowHandler(recurringCashFlowService *services.RecurringCashFlowItemService) *RecurringCashFlowHandler {
	return &RecurringCashFlowHandler{
		recurringCashFlowService: recurringCashFlowService,
	}
}

// GetRecurringCashFlowItems godoc
// @Summary Get all recurring cash flow items
// @Description Get all recurring cash flow items with pagination and filtering
// @Tags cashflow
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(50)
// @Param type query string false "Cash flow type (Inflow/Outflow)"
// @Param status query string false "Status filter"
// @Param category query string false "Category filter"
// @Param is_active query bool false "Active status filter"
// @Success 200 {object} PaginatedResponse{data=[]models.RecurringCashFlowItem}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/recurring [get]
func (h *RecurringCashFlowHandler) GetRecurringCashFlowItems(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	// Get filter parameters
	filters := services.RecurringCashFlowFilters{
		Type:     c.Query("type"),
		Status:   c.Query("status"),
		Category: c.Query("category"),
	}

	// Handle is_active filter
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filters.IsActive = &isActive
		}
	}

	items, total, err := h.recurringCashFlowService.GetAllRecurringCashFlowItems(page, limit, userID, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       items,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetRecurringCashFlowItem godoc
// @Summary Get recurring cash flow item by ID
// @Description Get a specific recurring cash flow item by ID
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Recurring Cash Flow Item ID"
// @Success 200 {object} models.RecurringCashFlowItem
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/recurring/{id} [get]
func (h *RecurringCashFlowHandler) GetRecurringCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Recurring cash flow item ID is required"})
		return
	}

	item, err := h.recurringCashFlowService.GetRecurringCashFlowItemByID(id, userID)
	if err != nil {
		if err.Error() == "recurring cash flow item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// CreateRecurringCashFlowItem godoc
// @Summary Create a new recurring cash flow item
// @Description Create a new recurring cash flow item
// @Tags cashflow
// @Accept json
// @Produce json
// @Param item body services.CreateRecurringCashFlowItemRequest true "Recurring cash flow item data"
// @Success 201 {object} models.RecurringCashFlowItem
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/recurring [post]
func (h *RecurringCashFlowHandler) CreateRecurringCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateRecurringCashFlowItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.recurringCashFlowService.CreateRecurringCashFlowItem(req, userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, item)
}

// UpdateRecurringCashFlowItem godoc
// @Summary Update a recurring cash flow item
// @Description Update an existing recurring cash flow item
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Recurring Cash Flow Item ID"
// @Param item body services.UpdateRecurringCashFlowItemRequest true "Recurring cash flow item data"
// @Success 200 {object} models.RecurringCashFlowItem
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/recurring/{id} [put]
func (h *RecurringCashFlowHandler) UpdateRecurringCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Recurring cash flow item ID is required"})
		return
	}

	var req services.UpdateRecurringCashFlowItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.recurringCashFlowService.UpdateRecurringCashFlowItem(id, req, userID)
	if err != nil {
		if err.Error() == "recurring cash flow item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// DeleteRecurringCashFlowItem godoc
// @Summary Delete a recurring cash flow item
// @Description Delete a recurring cash flow item
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Recurring Cash Flow Item ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/recurring/{id} [delete]
func (h *RecurringCashFlowHandler) DeleteRecurringCashFlowItem(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Recurring cash flow item ID is required"})
		return
	}

	err := h.recurringCashFlowService.DeleteRecurringCashFlowItem(id, userID)
	if err != nil {
		if err.Error() == "recurring cash flow item not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// Alias methods for route compatibility
func (h *RecurringCashFlowHandler) GetAllRecurringCashFlowItems(c *gin.Context) {
	h.GetRecurringCashFlowItems(c)
}

func (h *RecurringCashFlowHandler) GetRecurringCashFlowItemByID(c *gin.Context) {
	h.GetRecurringCashFlowItem(c)
}
