package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type CashFlowCategoryHandler struct {
	cashFlowCategoryService *services.CashFlowCategoryService
}

func NewCashFlowCategoryHandler(cashFlowCategoryService *services.CashFlowCategoryService) *CashFlowCategoryHandler {
	return &CashFlowCategoryHandler{
		cashFlowCategoryService: cashFlowCategoryService,
	}
}

// GetCashFlowCategories godoc
// @Summary Get all cash flow categories
// @Description Get all cash flow categories with pagination and filtering
// @Tags cashflow
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(50)
// @Param type query string false "Category type (Inflow/Outflow/Both)"
// @Param active query bool false "Active status filter"
// @Param search query string false "Search term"
// @Success 200 {object} PaginatedResponse{data=[]models.CashFlowCategory}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/categories [get]
func (h *CashFlowCategoryHandler) GetCashFlowCategories(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	// Get filter parameters
	filters := services.CashFlowCategoryFilters{
		Type:   c.Query("type"),
		Search: c.Query("search"),
	}

	// Handle active filter
	if activeStr := c.Query("active"); activeStr != "" {
		if active, err := strconv.ParseBool(activeStr); err == nil {
			filters.Active = &active
		}
	}

	categories, total, err := h.cashFlowCategoryService.GetAllCashFlowCategories(page, limit, userID, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       categories,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetCashFlowCategory godoc
// @Summary Get cash flow category by ID
// @Description Get a specific cash flow category by ID
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Cash Flow Category ID"
// @Success 200 {object} models.CashFlowCategory
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/categories/{id} [get]
func (h *CashFlowCategoryHandler) GetCashFlowCategory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Cash flow category ID is required"})
		return
	}

	category, err := h.cashFlowCategoryService.GetCashFlowCategoryByID(id, userID)
	if err != nil {
		if err.Error() == "cash flow category not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// CreateCashFlowCategory godoc
// @Summary Create a new cash flow category
// @Description Create a new cash flow category
// @Tags cashflow
// @Accept json
// @Produce json
// @Param category body services.CreateCashFlowCategoryRequest true "Cash flow category data"
// @Success 201 {object} models.CashFlowCategory
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/categories [post]
func (h *CashFlowCategoryHandler) CreateCashFlowCategory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateCashFlowCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	category, err := h.cashFlowCategoryService.CreateCashFlowCategory(req, userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// UpdateCashFlowCategory godoc
// @Summary Update a cash flow category
// @Description Update an existing cash flow category
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Cash Flow Category ID"
// @Param category body services.UpdateCashFlowCategoryRequest true "Cash flow category data"
// @Success 200 {object} models.CashFlowCategory
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/categories/{id} [put]
func (h *CashFlowCategoryHandler) UpdateCashFlowCategory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Cash flow category ID is required"})
		return
	}

	var req services.UpdateCashFlowCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	category, err := h.cashFlowCategoryService.UpdateCashFlowCategory(id, req, userID)
	if err != nil {
		if err.Error() == "cash flow category not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteCashFlowCategory godoc
// @Summary Delete a cash flow category
// @Description Delete a cash flow category
// @Tags cashflow
// @Accept json
// @Produce json
// @Param id path string true "Cash Flow Category ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /cashflow/categories/{id} [delete]
func (h *CashFlowCategoryHandler) DeleteCashFlowCategory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Cash flow category ID is required"})
		return
	}

	err := h.cashFlowCategoryService.DeleteCashFlowCategory(id, userID)
	if err != nil {
		if err.Error() == "cash flow category not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// Alias methods for route compatibility
func (h *CashFlowCategoryHandler) GetAllCashFlowCategories(c *gin.Context) {
	h.GetCashFlowCategories(c)
}

func (h *CashFlowCategoryHandler) GetCashFlowCategoryByID(c *gin.Context) {
	h.GetCashFlowCategory(c)
}
