package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type ExpenseCategoryHandler struct {
	service *services.ExpenseCategoryService
}

func NewExpenseCategoryHandler(service *services.ExpenseCategoryService) *ExpenseCategoryHandler {
	return &ExpenseCategoryHandler{service: service}
}

// GetExpenseCategories handles GET /expense-categories
func (h *ExpenseCategoryHandler) GetExpenseCategories(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>y("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "50"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	categories, total, err := h.service.GetAllExpenseCategories(page, limit, userID)
	if err != nil {
		c.J<PERSON>N(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := (int(total) + limit - 1) / limit
	hasNextPage := page < totalPages
	hasPreviousPage := page > 1

	response := gin.H{
		"data": categories,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetExpenseCategoriesByBranch handles GET /branches/:branchId/expense-categories
func (h *ExpenseCategoryHandler) GetExpenseCategoriesByBranch(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("id")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Branch ID is required"})
		return
	}

	categories, err := h.service.GetExpenseCategoriesByBranch(branchID, userID)
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this branch"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": categories})
}

// GetExpenseCategoryByID handles GET /expense-categories/:id
func (h *ExpenseCategoryHandler) GetExpenseCategoryByID(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	categoryID := c.Param("id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	category, err := h.service.GetExpenseCategoryByID(categoryID, userID)
	if err != nil {
		if err.Error() == "expense category not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense category not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// CreateExpenseCategory handles POST /expense-categories
func (h *ExpenseCategoryHandler) CreateExpenseCategory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.CreateExpenseCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category, err := h.service.CreateExpenseCategory(req, userID)
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this branch"})
			return
		}
		if err.Error() == "parent category not found or does not belong to this branch" ||
			err.Error() == "account not found or does not belong to this branch" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// CreateExpenseCategoryForBranch handles POST /branches/:branchId/expense-categories
func (h *ExpenseCategoryHandler) CreateExpenseCategoryForBranch(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("id")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Branch ID is required"})
		return
	}

	var req services.CreateExpenseCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set branch ID from URL parameter
	req.BranchID = branchID

	category, err := h.service.CreateExpenseCategory(req, userID)
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this branch"})
			return
		}
		if err.Error() == "parent category not found or does not belong to this branch" ||
			err.Error() == "account not found or does not belong to this branch" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// UpdateExpenseCategory handles PUT /expense-categories/:id
func (h *ExpenseCategoryHandler) UpdateExpenseCategory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	categoryID := c.Param("id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	var req services.UpdateExpenseCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category, err := h.service.UpdateExpenseCategory(categoryID, req, userID)
	if err != nil {
		if err.Error() == "expense category not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense category not found"})
			return
		}
		if err.Error() == "parent category not found or does not belong to this branch" ||
			err.Error() == "account not found or does not belong to this branch" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteExpenseCategory handles DELETE /expense-categories/:id
func (h *ExpenseCategoryHandler) DeleteExpenseCategory(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	categoryID := c.Param("id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	err := h.service.DeleteExpenseCategory(categoryID, userID)
	if err != nil {
		if err.Error() == "expense category not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense category not found"})
			return
		}
		if err.Error() == "cannot delete category that has associated expenses" ||
			err.Error() == "cannot delete category that has child categories" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Expense category deleted successfully"})
}
