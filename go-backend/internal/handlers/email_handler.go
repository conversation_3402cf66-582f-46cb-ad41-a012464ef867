package handlers

import (
	"net/http"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// EmailHandler handles email sending requests
type EmailHandler struct {
	emailService *services.EmailService
}

// NewEmailHandler creates a new EmailHandler
func NewEmailHandler(emailService *services.EmailService) *EmailHandler {
	return &EmailHandler{
		emailService: emailService,
	}
}

// SendPaymentConfirmationRequest represents payment confirmation email request
type SendPaymentConfirmationRequest struct {
	CustomerName  string  `json:"customer_name" binding:"required"`
	CustomerEmail string  `json:"customer_email" binding:"required,email"`
	InvoiceNumber string  `json:"invoice_number" binding:"required"`
	PaymentAmount float64 `json:"payment_amount" binding:"required"`
	PaymentDate   string  `json:"payment_date" binding:"required"`
	PaymentMethod string  `json:"payment_method" binding:"required"`
	TransactionID string  `json:"transaction_id" binding:"required"`
}

// SendInvoiceNotificationRequest represents invoice notification email request
type SendInvoiceNotificationRequest struct {
	CustomerName  string  `json:"customer_name" binding:"required"`
	CustomerEmail string  `json:"customer_email" binding:"required,email"`
	InvoiceNumber string  `json:"invoice_number" binding:"required"`
	InvoiceAmount float64 `json:"invoice_amount" binding:"required"`
	DueDate       string  `json:"due_date" binding:"required"`
	InvoiceURL    string  `json:"invoice_url" binding:"required"`
}

// EmailResponse represents email sending response
type EmailResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// SendPaymentConfirmation sends a payment confirmation email
func (h *EmailHandler) SendPaymentConfirmation(c *gin.Context) {
	var req SendPaymentConfirmationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, EmailResponse{
			Success: false,
			Message: "Invalid request data",
		})
		return
	}

	// Validate payment amount
	if req.PaymentAmount <= 0 {
		c.JSON(http.StatusBadRequest, EmailResponse{
			Success: false,
			Message: "Payment amount must be greater than zero",
		})
		return
	}

	// Prepare email data
	emailData := services.PaymentConfirmationData{
		CustomerName:  req.CustomerName,
		CustomerEmail: req.CustomerEmail,
		InvoiceNumber: req.InvoiceNumber,
		PaymentAmount: req.PaymentAmount,
		PaymentDate:   req.PaymentDate,
		PaymentMethod: req.PaymentMethod,
		TransactionID: req.TransactionID,
		CompanyName:   "Your Company Name", // This could be configurable
	}

	// Send email
	if err := h.emailService.SendPaymentConfirmation(emailData); err != nil {
		c.JSON(http.StatusInternalServerError, EmailResponse{
			Success: false,
			Message: "Failed to send payment confirmation email",
		})
		return
	}

	c.JSON(http.StatusOK, EmailResponse{
		Success: true,
		Message: "Payment confirmation email sent successfully",
	})
}

// SendInvoiceNotification sends an invoice notification email
func (h *EmailHandler) SendInvoiceNotification(c *gin.Context) {
	var req SendInvoiceNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, EmailResponse{
			Success: false,
			Message: "Invalid request data",
		})
		return
	}

	// Validate invoice amount
	if req.InvoiceAmount <= 0 {
		c.JSON(http.StatusBadRequest, EmailResponse{
			Success: false,
			Message: "Invoice amount must be greater than zero",
		})
		return
	}

	// Prepare email data
	emailData := services.InvoiceNotificationData{
		CustomerName:  req.CustomerName,
		CustomerEmail: req.CustomerEmail,
		InvoiceNumber: req.InvoiceNumber,
		InvoiceAmount: req.InvoiceAmount,
		DueDate:       req.DueDate,
		InvoiceURL:    req.InvoiceURL,
		CompanyName:   "Your Company Name", // This could be configurable
	}

	// Send email
	if err := h.emailService.SendInvoiceNotification(emailData); err != nil {
		c.JSON(http.StatusInternalServerError, EmailResponse{
			Success: false,
			Message: "Failed to send invoice notification email",
		})
		return
	}

	c.JSON(http.StatusOK, EmailResponse{
		Success: true,
		Message: "Invoice notification email sent successfully",
	})
}

// TestEmailConnection tests the email service connection
func (h *EmailHandler) TestEmailConnection(c *gin.Context) {
	if err := h.emailService.TestConnection(); err != nil {
		c.JSON(http.StatusInternalServerError, EmailResponse{
			Success: false,
			Message: "Email service connection failed: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, EmailResponse{
		Success: true,
		Message: "Email service connection successful",
	})
}
