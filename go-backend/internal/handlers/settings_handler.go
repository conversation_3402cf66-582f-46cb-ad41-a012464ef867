package handlers

import (
	"adc-account-backend/internal/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// SettingsHandler handles settings-related requests
type SettingsHandler struct {
	userService         *services.UserService
	organizationService *services.OrganizationService
}

// NewSettingsHandler creates a new settings handler
func NewSettingsHandler(userService *services.UserService, organizationService *services.OrganizationService) *SettingsHandler {
	return &SettingsHandler{
		userService:         userService,
		organizationService: organizationService,
	}
}

// GetUserPreferences gets user preferences by user ID
func (h *SettingsHandler) GetUserPreferences(c *gin.Context) {
	userID := c.Param("userId")

	preferences, err := h.userService.GetUserPreferences(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Use consistent format like organizations
	c.JSON(http.StatusOK, gin.H{
		"data": preferences,
	})
}

// UpdateUserPreferences updates user preferences
func (h *SettingsHandler) UpdateUserPreferences(c *gin.Context) {
	userID := c.Param("userId")

	var preferences map[string]interface{}
	if err := c.ShouldBindJSON(&preferences); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.userService.UpdateUserPreferences(userID, preferences)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Preferences updated successfully"})
}

// GetCompanySettings gets company settings (organization settings)
func (h *SettingsHandler) GetCompanySettings(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user's first organization as company settings
	organizations, _, err := h.organizationService.GetAllOrganizations(1, 1, "", "created_at", "desc", userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if len(organizations) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "No organization found"})
		return
	}

	// Return the first organization as company settings
	c.JSON(http.StatusOK, organizations[0])
}

// UpdateCompanySettings updates company settings (organization settings)
func (h *SettingsHandler) UpdateCompanySettings(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var updates services.UpdateOrganizationRequest
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user's first organization
	organizations, _, err := h.organizationService.GetAllOrganizations(1, 1, "", "created_at", "desc", userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if len(organizations) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "No organization found"})
		return
	}

	// Update the organization
	updatedOrg, err := h.organizationService.UpdateOrganization(organizations[0].ID, updates, userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedOrg)
}

// GetAccountingSettings gets accounting settings (placeholder for now)
func (h *SettingsHandler) GetAccountingSettings(c *gin.Context) {
	// For now, return default accounting settings
	// This can be expanded later with a proper accounting settings model
	defaultSettings := map[string]interface{}{
		"id": "default",
		"default_accounts": map[string]interface{}{
			"accounts_receivable": nil,
			"accounts_payable":    nil,
			"sales_revenue":       nil,
			"cost_of_goods_sold":  nil,
			"inventory_asset":     nil,
			"retained_earnings":   nil,
			"bank_charges":        nil,
			"undeposited_funds":   nil,
		},
		"default_terms": map[string]interface{}{
			"payment_terms":    30,
			"invoice_due_days": 30,
			"bill_due_days":    30,
		},
		"tax_settings": map[string]interface{}{
			"tax_calculation_method": "exclusive",
			"default_tax_rate":       0.0,
			"tax_id_label":           "Tax ID",
		},
	}

	c.JSON(http.StatusOK, defaultSettings)
}

// UpdateAccountingSettings updates accounting settings (placeholder for now)
func (h *SettingsHandler) UpdateAccountingSettings(c *gin.Context) {
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// For now, just return the updates as if they were saved
	// This can be expanded later with a proper accounting settings model
	c.JSON(http.StatusOK, updates)
}
