package handlers

import (
	"net/http"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// PDFHandler handles PDF generation requests
type PDFHandler struct {
	pdfService *services.PDFService
}

// NewPDFHandler creates a new PDFHandler
func NewPDFHandler(pdfService *services.PDFService) *PDFHandler {
	return &PDFHandler{
		pdfService: pdfService,
	}
}

// GenerateInvoicePDF generates a PDF for an invoice
func (h *PDFHandler) GenerateInvoicePDF(c *gin.Context) {
	invoiceID := c.Param("invoiceId")
	if invoiceID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invoice ID is required"})
		return
	}

	// Generate PDF
	pdfBytes, err := h.pdfService.GenerateInvoicePDF(invoiceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// Set headers for PDF download
	c.Header("Content-Type", "application/pdf")
	c.<PERSON><PERSON>("Content-Disposition", "attachment; filename=invoice-"+invoiceID+".pdf")
	c.Header("Content-Length", string(rune(len(pdfBytes))))

	// Return PDF bytes
	c.Data(http.StatusOK, "application/pdf", pdfBytes)
}

// GenerateStatementPDF generates a PDF for a customer statement
func (h *PDFHandler) GenerateStatementPDF(c *gin.Context) {
	statementID := c.Param("statementId")
	if statementID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Statement ID is required"})
		return
	}

	// Generate PDF
	pdfBytes, err := h.pdfService.GenerateStatementPDF(statementID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// Set headers for PDF download
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=statement-"+statementID+".pdf")
	c.Header("Content-Length", string(rune(len(pdfBytes))))

	// Return PDF bytes
	c.Data(http.StatusOK, "application/pdf", pdfBytes)
}
