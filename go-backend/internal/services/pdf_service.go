package services

import (
	"bytes"
	"fmt"

	"adc-account-backend/internal/models"

	"github.com/go-pdf/fpdf"
	"gorm.io/gorm"
)

// PDFService handles PDF generation for invoices, statements, and reports
type PDFService struct {
	db *gorm.DB
}

// NewPDFService creates a new PDFService
func NewPDFService(db *gorm.DB) *PDFService {
	return &PDFService{db: db}
}

// InvoicePDFData represents data for invoice PDF generation
type InvoicePDFData struct {
	Invoice     *models.Invoice         `json:"invoice"`
	Customer    *models.Customer        `json:"customer"`
	Items       []models.InvoiceItem    `json:"items"`
	Payments    []models.InvoicePayment `json:"payments"`
	CompanyInfo CompanyInfo             `json:"company_info"`
}

// StatementPDFData represents data for statement PDF generation
type StatementPDFData struct {
	Statement   *models.CustomerStatement `json:"statement"`
	Customer    *models.Customer          `json:"customer"`
	Invoices    []models.Invoice          `json:"invoices"`
	Payments    []models.InvoicePayment   `json:"payments"`
	CompanyInfo CompanyInfo               `json:"company_info"`
}

// CompanyInfo represents company information for PDFs
type CompanyInfo struct {
	Name    string `json:"name"`
	Address string `json:"address"`
	Phone   string `json:"phone"`
	Email   string `json:"email"`
	Website string `json:"website"`
}

// GenerateInvoicePDF generates a PDF for an invoice
func (s *PDFService) GenerateInvoicePDF(invoiceID string) ([]byte, error) {
	// Fetch invoice with related data
	var invoice models.Invoice
	if err := s.db.Preload("Customer").Preload("Items").Preload("Payments").
		First(&invoice, "id = ?", invoiceID).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch invoice: %w", err)
	}

	// Create PDF
	pdf := fpdf.New("P", "mm", "A4", "")
	pdf.AddPage()

	// Set font
	pdf.SetFont("Arial", "", 12)

	// Company header
	s.addCompanyHeader(pdf)

	// Invoice title
	pdf.SetFont("Arial", "B", 20)
	pdf.Cell(0, 15, "INVOICE")
	pdf.Ln(20)

	// Invoice details
	s.addInvoiceDetails(pdf, &invoice)

	// Customer information
	s.addCustomerInfo(pdf, &invoice.Customer)

	// Invoice items table
	s.addInvoiceItemsTable(pdf, invoice.Items)

	// Total section
	s.addInvoiceTotal(pdf, &invoice)

	// Notes and terms
	if invoice.Notes != nil && *invoice.Notes != "" {
		s.addNotesSection(pdf, "Notes", *invoice.Notes)
	}
	if invoice.Terms != nil && *invoice.Terms != "" {
		s.addNotesSection(pdf, "Terms & Conditions", *invoice.Terms)
	}

	// Generate PDF bytes
	var buf bytes.Buffer
	if err := pdf.Output(&buf); err != nil {
		return nil, fmt.Errorf("failed to generate PDF: %w", err)
	}

	return buf.Bytes(), nil
}

// GenerateStatementPDF generates a PDF for a customer statement
func (s *PDFService) GenerateStatementPDF(statementID string) ([]byte, error) {
	// Fetch statement with related data
	var statement models.CustomerStatement
	if err := s.db.Preload("Customer").First(&statement, "id = ?", statementID).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch statement: %w", err)
	}

	// Fetch invoices for the statement period
	var invoices []models.Invoice
	if err := s.db.Where("customer_id = ? AND issue_date BETWEEN ? AND ?",
		statement.CustomerID, statement.StartDate, statement.EndDate).
		Preload("Items").Preload("Payments").Find(&invoices).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch invoices: %w", err)
	}

	// Create PDF
	pdf := fpdf.New("P", "mm", "A4", "")
	pdf.AddPage()

	// Set font
	pdf.SetFont("Arial", "", 12)

	// Company header
	s.addCompanyHeader(pdf)

	// Statement title
	pdf.SetFont("Arial", "B", 20)
	pdf.Cell(0, 15, "CUSTOMER STATEMENT")
	pdf.Ln(20)

	// Statement details
	s.addStatementDetails(pdf, &statement)

	// Customer information
	s.addCustomerInfo(pdf, &statement.Customer)

	// Statement summary
	s.addStatementSummary(pdf, &statement)

	// Invoice list
	s.addStatementInvoices(pdf, invoices)

	// Generate PDF bytes
	var buf bytes.Buffer
	if err := pdf.Output(&buf); err != nil {
		return nil, fmt.Errorf("failed to generate PDF: %w", err)
	}

	return buf.Bytes(), nil
}

// Helper methods for PDF generation

func (s *PDFService) addCompanyHeader(pdf *fpdf.Fpdf) {
	pdf.SetFont("Arial", "B", 16)
	pdf.Cell(0, 10, "Your Company Name")
	pdf.Ln(8)

	pdf.SetFont("Arial", "", 10)
	pdf.Cell(0, 5, "123 Business Street, City, State 12345")
	pdf.Ln(5)
	pdf.Cell(0, 5, "Phone: (************* | Email: <EMAIL>")
	pdf.Ln(15)
}

func (s *PDFService) addInvoiceDetails(pdf *fpdf.Fpdf, invoice *models.Invoice) {
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Invoice Number:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, invoice.InvoiceNumber)
	pdf.Ln(8)

	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Issue Date:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, invoice.IssueDate.Format("January 2, 2006"))
	pdf.Ln(8)

	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Due Date:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, invoice.DueDate.Format("January 2, 2006"))
	pdf.Ln(8)

	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Status:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, string(invoice.Status))
	pdf.Ln(15)
}

func (s *PDFService) addCustomerInfo(pdf *fpdf.Fpdf, customer *models.Customer) {
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(0, 10, "Bill To:")
	pdf.Ln(10)

	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, customer.Name)
	pdf.Ln(8)

	if customer.Email != nil {
		pdf.Cell(0, 8, *customer.Email)
		pdf.Ln(8)
	}

	if customer.Phone != nil {
		pdf.Cell(0, 8, *customer.Phone)
		pdf.Ln(8)
	}

	if customer.Address != nil {
		pdf.Cell(0, 8, *customer.Address)
		pdf.Ln(8)
	}

	pdf.Ln(10)
}

func (s *PDFService) addInvoiceItemsTable(pdf *fpdf.Fpdf, items []models.InvoiceItem) {
	// Table header
	pdf.SetFont("Arial", "B", 10)
	pdf.SetFillColor(240, 240, 240)
	pdf.CellFormat(80, 8, "Description", "1", 0, "L", true, 0, "")
	pdf.CellFormat(20, 8, "Qty", "1", 0, "C", true, 0, "")
	pdf.CellFormat(30, 8, "Unit Price", "1", 0, "R", true, 0, "")
	pdf.CellFormat(30, 8, "Total", "1", 1, "R", true, 0, "")

	// Table rows
	pdf.SetFont("Arial", "", 10)
	pdf.SetFillColor(255, 255, 255)

	for _, item := range items {
		total := item.Quantity.Mul(item.UnitPrice)
		pdf.CellFormat(80, 8, item.Description, "1", 0, "L", false, 0, "")
		pdf.CellFormat(20, 8, fmt.Sprintf("%.0f", item.Quantity.InexactFloat64()), "1", 0, "C", false, 0, "")
		pdf.CellFormat(30, 8, fmt.Sprintf("$%.2f", item.UnitPrice.InexactFloat64()), "1", 0, "R", false, 0, "")
		pdf.CellFormat(30, 8, fmt.Sprintf("$%.2f", total.InexactFloat64()), "1", 1, "R", false, 0, "")
	}

	pdf.Ln(10)
}

func (s *PDFService) addInvoiceTotal(pdf *fpdf.Fpdf, invoice *models.Invoice) {
	// Total section
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(130, 8, "")
	pdf.Cell(30, 8, "Total Amount:")
	pdf.Cell(30, 8, fmt.Sprintf("$%.2f", invoice.TotalAmount.InexactFloat64()))
	pdf.Ln(15)
}

func (s *PDFService) addStatementDetails(pdf *fpdf.Fpdf, statement *models.CustomerStatement) {
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Statement ID:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, statement.ID)
	pdf.Ln(8)

	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(50, 8, "Period:")
	pdf.SetFont("Arial", "", 12)
	pdf.Cell(0, 8, fmt.Sprintf("%s - %s",
		statement.StartDate.Format("Jan 2, 2006"),
		statement.EndDate.Format("Jan 2, 2006")))
	pdf.Ln(15)
}

func (s *PDFService) addStatementSummary(pdf *fpdf.Fpdf, statement *models.CustomerStatement) {
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(0, 10, "Statement Summary")
	pdf.Ln(10)

	pdf.SetFont("Arial", "", 10)
	pdf.Cell(50, 8, "Opening Balance:")
	pdf.Cell(0, 8, fmt.Sprintf("$%.2f", statement.OpeningBalance.InexactFloat64()))
	pdf.Ln(8)

	pdf.Cell(50, 8, "Total Invoiced:")
	pdf.Cell(0, 8, fmt.Sprintf("$%.2f", statement.TotalInvoiced.InexactFloat64()))
	pdf.Ln(8)

	pdf.Cell(50, 8, "Total Paid:")
	pdf.Cell(0, 8, fmt.Sprintf("$%.2f", statement.TotalPaid.InexactFloat64()))
	pdf.Ln(8)

	pdf.Cell(50, 8, "Closing Balance:")
	pdf.Cell(0, 8, fmt.Sprintf("$%.2f", statement.ClosingBalance.InexactFloat64()))
	pdf.Ln(15)
}

func (s *PDFService) addStatementInvoices(pdf *fpdf.Fpdf, invoices []models.Invoice) {
	if len(invoices) == 0 {
		return
	}

	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(0, 10, "Invoices in Period")
	pdf.Ln(10)

	// Table header
	pdf.SetFont("Arial", "B", 10)
	pdf.SetFillColor(240, 240, 240)
	pdf.CellFormat(40, 8, "Invoice #", "1", 0, "L", true, 0, "")
	pdf.CellFormat(30, 8, "Date", "1", 0, "C", true, 0, "")
	pdf.CellFormat(30, 8, "Due Date", "1", 0, "C", true, 0, "")
	pdf.CellFormat(30, 8, "Amount", "1", 0, "R", true, 0, "")
	pdf.CellFormat(30, 8, "Status", "1", 1, "C", true, 0, "")

	// Table rows
	pdf.SetFont("Arial", "", 10)
	for _, invoice := range invoices {
		pdf.CellFormat(40, 8, invoice.InvoiceNumber, "1", 0, "L", false, 0, "")
		pdf.CellFormat(30, 8, invoice.IssueDate.Format("Jan 2"), "1", 0, "C", false, 0, "")
		pdf.CellFormat(30, 8, invoice.DueDate.Format("Jan 2"), "1", 0, "C", false, 0, "")
		pdf.CellFormat(30, 8, fmt.Sprintf("$%.2f", invoice.TotalAmount.InexactFloat64()), "1", 0, "R", false, 0, "")
		pdf.CellFormat(30, 8, string(invoice.Status), "1", 1, "C", false, 0, "")
	}
}

func (s *PDFService) addNotesSection(pdf *fpdf.Fpdf, title, content string) {
	pdf.Ln(10)
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(0, 8, title+":")
	pdf.Ln(8)

	pdf.SetFont("Arial", "", 10)
	pdf.MultiCell(0, 5, content, "", "", false)
	pdf.Ln(5)
}
