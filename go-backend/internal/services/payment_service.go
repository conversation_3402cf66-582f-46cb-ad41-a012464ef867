package services

import (
	"fmt"
	"os"
	"strings"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/customer"
	"github.com/stripe/stripe-go/v76/paymentintent"
	"github.com/stripe/stripe-go/v76/paymentmethod"
	"github.com/stripe/stripe-go/v76/setupintent"
	"github.com/stripe/stripe-go/v76/webhook"
	"gorm.io/gorm"
)

// PaymentService handles payment processing with Stripe
type PaymentService struct {
	db           *gorm.DB
	emailService *EmailService
}

// NewPaymentService creates a new PaymentService
func NewPaymentService(db *gorm.DB, emailService *EmailService) *PaymentService {
	// Initialize Stripe
	stripe.Key = os.Getenv("STRIPE_SECRET_KEY")

	return &PaymentService{
		db:           db,
		emailService: emailService,
	}
}

// PaymentIntentRequest represents a payment intent creation request
type PaymentIntentRequest struct {
	Amount        float64 `json:"amount" binding:"required"`
	Currency      string  `json:"currency"`
	InvoiceID     string  `json:"invoice_id" binding:"required"`
	CustomerID    string  `json:"customer_id" binding:"required"`
	CustomerEmail string  `json:"customer_email" binding:"required"`
	Description   string  `json:"description"`
}

// PaymentIntentResponse represents a payment intent creation response
type PaymentIntentResponse struct {
	Success          bool   `json:"success"`
	PaymentIntentID  string `json:"payment_intent_id,omitempty"`
	ClientSecret     string `json:"client_secret,omitempty"`
	StripeCustomerID string `json:"stripe_customer_id,omitempty"`
	Message          string `json:"message,omitempty"`
}

// SetupIntentRequest represents a setup intent creation request
type SetupIntentRequest struct {
	CustomerID string `json:"customer_id" binding:"required"`
}

// SetupIntentResponse represents a setup intent creation response
type SetupIntentResponse struct {
	Success       bool   `json:"success"`
	SetupIntentID string `json:"setup_intent_id,omitempty"`
	ClientSecret  string `json:"client_secret,omitempty"`
	Message       string `json:"message,omitempty"`
}

// PaymentMethodsResponse represents payment methods response
type PaymentMethodsResponse struct {
	Success        bool                    `json:"success"`
	PaymentMethods []*stripe.PaymentMethod `json:"payment_methods,omitempty"`
	Message        string                  `json:"message,omitempty"`
}

// CreatePaymentIntent creates a Stripe payment intent
func (s *PaymentService) CreatePaymentIntent(req PaymentIntentRequest) (*PaymentIntentResponse, error) {
	// Validate amount
	if req.Amount <= 0 {
		return &PaymentIntentResponse{
			Success: false,
			Message: "Amount must be greater than zero",
		}, nil
	}

	// Convert amount to cents
	amountCents := int64(req.Amount * 100)

	// Set default currency
	if req.Currency == "" {
		req.Currency = "usd"
	}

	// Find or create Stripe customer
	stripeCustomerID, err := s.findOrCreateStripeCustomer(req.CustomerEmail, req.CustomerID)
	if err != nil {
		return &PaymentIntentResponse{
			Success: false,
			Message: "Failed to process customer information",
		}, fmt.Errorf("failed to handle stripe customer: %w", err)
	}

	// Set description
	description := req.Description
	if description == "" {
		description = fmt.Sprintf("Payment for Invoice %s", req.InvoiceID)
	}

	// Create payment intent
	params := &stripe.PaymentIntentParams{
		Amount:      stripe.Int64(amountCents),
		Currency:    stripe.String(req.Currency),
		Customer:    stripe.String(stripeCustomerID),
		Description: stripe.String(description),
		Metadata: map[string]string{
			"invoice_id":  req.InvoiceID,
			"customer_id": req.CustomerID,
		},
		AutomaticPaymentMethods: &stripe.PaymentIntentAutomaticPaymentMethodsParams{
			Enabled: stripe.Bool(true),
		},
	}

	pi, err := paymentintent.New(params)
	if err != nil {
		return &PaymentIntentResponse{
			Success: false,
			Message: "Failed to create payment intent",
		}, fmt.Errorf("failed to create payment intent: %w", err)
	}

	return &PaymentIntentResponse{
		Success:          true,
		PaymentIntentID:  pi.ID,
		ClientSecret:     pi.ClientSecret,
		StripeCustomerID: stripeCustomerID,
	}, nil
}

// CreateSetupIntent creates a Stripe setup intent for saving payment methods
func (s *PaymentService) CreateSetupIntent(req SetupIntentRequest) (*SetupIntentResponse, error) {
	params := &stripe.SetupIntentParams{
		Customer:           stripe.String(req.CustomerID),
		PaymentMethodTypes: stripe.StringSlice([]string{"card"}),
		Usage:              stripe.String("off_session"),
	}

	si, err := setupintent.New(params)
	if err != nil {
		return &SetupIntentResponse{
			Success: false,
			Message: "Failed to create setup intent",
		}, fmt.Errorf("failed to create setup intent: %w", err)
	}

	return &SetupIntentResponse{
		Success:       true,
		SetupIntentID: si.ID,
		ClientSecret:  si.ClientSecret,
	}, nil
}

// GetPaymentMethods retrieves saved payment methods for a customer
func (s *PaymentService) GetPaymentMethods(customerID string) (*PaymentMethodsResponse, error) {
	params := &stripe.PaymentMethodListParams{
		Customer: stripe.String(customerID),
		Type:     stripe.String("card"),
	}

	iter := paymentmethod.List(params)
	var paymentMethods []*stripe.PaymentMethod

	for iter.Next() {
		paymentMethods = append(paymentMethods, iter.PaymentMethod())
	}

	if err := iter.Err(); err != nil {
		return &PaymentMethodsResponse{
			Success: false,
			Message: "Failed to retrieve payment methods",
		}, fmt.Errorf("failed to list payment methods: %w", err)
	}

	return &PaymentMethodsResponse{
		Success:        true,
		PaymentMethods: paymentMethods,
	}, nil
}

// DetachPaymentMethod removes a saved payment method
func (s *PaymentService) DetachPaymentMethod(paymentMethodID string) error {
	_, err := paymentmethod.Detach(paymentMethodID, nil)
	if err != nil {
		return fmt.Errorf("failed to detach payment method: %w", err)
	}

	return nil
}

// ProcessPaymentSuccess handles successful payment processing
func (s *PaymentService) ProcessPaymentSuccess(paymentIntentID string) error {
	// Retrieve payment intent from Stripe
	pi, err := paymentintent.Get(paymentIntentID, nil)
	if err != nil {
		return fmt.Errorf("failed to retrieve payment intent: %w", err)
	}

	// Extract metadata
	invoiceID := pi.Metadata["invoice_id"]
	customerID := pi.Metadata["customer_id"]

	if invoiceID == "" || customerID == "" {
		return fmt.Errorf("missing invoice_id or customer_id in payment intent metadata")
	}

	// Get invoice details
	var invoice models.Invoice
	if err := s.db.Preload("Customer").First(&invoice, "id = ?", invoiceID).Error; err != nil {
		return fmt.Errorf("failed to fetch invoice: %w", err)
	}

	// Create payment record
	paymentMethod := "credit_card"
	notes := fmt.Sprintf("Stripe Payment Intent: %s", paymentIntentID)
	payment := &models.InvoicePayment{
		InvoiceID:     invoiceID,
		Amount:        decimal.NewFromFloat(float64(pi.Amount) / 100), // Convert from cents
		PaymentDate:   time.Now(),
		PaymentMethod: &paymentMethod,
		Notes:         &notes,
	}

	// Add payment to invoice
	invoiceService := NewInvoiceService(s.db)
	if err := invoiceService.AddPaymentToInvoice(invoiceID, payment, ""); err != nil {
		return fmt.Errorf("failed to add payment to invoice: %w", err)
	}

	// Send payment confirmation email
	if invoice.Customer.Email != nil {
		emailData := PaymentConfirmationData{
			CustomerName:  invoice.Customer.Name,
			CustomerEmail: *invoice.Customer.Email,
			InvoiceNumber: invoice.InvoiceNumber,
			PaymentAmount: float64(pi.Amount) / 100,
			PaymentDate:   time.Now().Format("January 2, 2006"),
			PaymentMethod: "Credit Card",
			TransactionID: paymentIntentID,
			CompanyName:   "Your Company Name",
		}

		if err := s.emailService.SendPaymentConfirmation(emailData); err != nil {
			// Log error but don't fail the payment
			fmt.Printf("Failed to send payment confirmation email: %v\n", err)
		}
	}

	return nil
}

// findOrCreateStripeCustomer finds existing or creates new Stripe customer
func (s *PaymentService) findOrCreateStripeCustomer(email, customerID string) (string, error) {
	// Try to find existing customer by email
	params := &stripe.CustomerListParams{
		Email: stripe.String(email),
	}
	params.Filters.AddFilter("limit", "", "1")

	iter := customer.List(params)
	if iter.Next() {
		return iter.Customer().ID, nil
	}

	// Create new customer
	customerParams := &stripe.CustomerParams{
		Email: stripe.String(email),
		Metadata: map[string]string{
			"customer_id": customerID,
		},
	}

	cust, err := customer.New(customerParams)
	if err != nil {
		return "", fmt.Errorf("failed to create stripe customer: %w", err)
	}

	return cust.ID, nil
}

// HandleWebhook handles Stripe webhooks
func (s *PaymentService) HandleWebhook(payload []byte, signature string) error {
	endpointSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")
	if endpointSecret == "" {
		return fmt.Errorf("stripe webhook secret not configured")
	}

	event, err := webhook.ConstructEvent(payload, signature, endpointSecret)
	if err != nil {
		return fmt.Errorf("failed to construct webhook event: %w", err)
	}

	switch event.Type {
	case "payment_intent.succeeded":
		// Extract payment intent ID from event data
		if paymentIntentID, ok := event.Data.Object["id"].(string); ok {
			// Process successful payment
			if err := s.ProcessPaymentSuccess(paymentIntentID); err != nil {
				return fmt.Errorf("failed to process payment success: %w", err)
			}
		} else {
			return fmt.Errorf("failed to extract payment intent ID from event")
		}

	case "payment_intent.payment_failed":
		// Extract payment intent ID from event data
		if paymentIntentID, ok := event.Data.Object["id"].(string); ok {
			// Log payment failure
			fmt.Printf("Payment failed for payment intent: %s\n", paymentIntentID)
		} else {
			fmt.Printf("Payment failed but could not extract payment intent ID\n")
		}

	default:
		fmt.Printf("Unhandled webhook event type: %s\n", event.Type)
	}

	return nil
}

// ValidateWebhookSignature validates Stripe webhook signature
func (s *PaymentService) ValidateWebhookSignature(payload []byte, signature string) bool {
	endpointSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")
	if endpointSecret == "" {
		return false
	}

	_, err := webhook.ConstructEvent(payload, signature, endpointSecret)
	return err == nil
}

// FormatAmount formats amount for display
func (s *PaymentService) FormatAmount(amount float64, currency string) string {
	return fmt.Sprintf("%.2f %s", amount, strings.ToUpper(currency))
}

// AmountToCents converts amount to cents for Stripe
func (s *PaymentService) AmountToCents(amount float64) int64 {
	return int64(amount * 100)
}

// AmountFromCents converts cents to amount from Stripe
func (s *PaymentService) AmountFromCents(cents int64) float64 {
	return float64(cents) / 100
}
