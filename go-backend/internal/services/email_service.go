package services

import (
	"bytes"
	"fmt"
	"html/template"
	"net/smtp"
	"os"
	"strconv"
	"time"

	"adc-account-backend/internal/models"

	"gorm.io/gorm"
)

// EmailService handles email sending functionality
type EmailService struct {
	db       *gorm.DB
	host     string
	port     int
	username string
	password string
	from     string
}

// NewEmailService creates a new EmailService
func NewEmailService(db *gorm.DB) *EmailService {
	port, _ := strconv.Atoi(getEnv("SMTP_PORT", "587"))

	return &EmailService{
		db:       db,
		host:     getEnv("SMTP_HOST", "smtp.gmail.com"),
		port:     port,
		username: getEnv("SMTP_USER", ""),
		password: getEnv("SMTP_PASS", ""),
		from:     getEnv("SMTP_FROM", getEnv("SMTP_USER", "")),
	}
}

// PaymentConfirmationData represents data for payment confirmation emails
type PaymentConfirmationData struct {
	CustomerName  string  `json:"customer_name"`
	CustomerEmail string  `json:"customer_email"`
	InvoiceNumber string  `json:"invoice_number"`
	PaymentAmount float64 `json:"payment_amount"`
	PaymentDate   string  `json:"payment_date"`
	PaymentMethod string  `json:"payment_method"`
	TransactionID string  `json:"transaction_id"`
	CompanyName   string  `json:"company_name"`
}

// InvoiceNotificationData represents data for invoice notification emails
type InvoiceNotificationData struct {
	CustomerName  string  `json:"customer_name"`
	CustomerEmail string  `json:"customer_email"`
	InvoiceNumber string  `json:"invoice_number"`
	InvoiceAmount float64 `json:"invoice_amount"`
	DueDate       string  `json:"due_date"`
	InvoiceURL    string  `json:"invoice_url"`
	CompanyName   string  `json:"company_name"`
}

// SendPaymentConfirmation sends a payment confirmation email
func (s *EmailService) SendPaymentConfirmation(data PaymentConfirmationData) error {
	subject := fmt.Sprintf("Payment Confirmation - Invoice %s", data.InvoiceNumber)

	// Generate HTML content
	htmlContent, err := s.generatePaymentConfirmationHTML(data)
	if err != nil {
		return fmt.Errorf("failed to generate email content: %w", err)
	}

	// Generate plain text content
	textContent := s.generatePaymentConfirmationText(data)

	// Send email
	if err := s.sendEmail(data.CustomerEmail, subject, htmlContent, textContent); err != nil {
		return fmt.Errorf("failed to send payment confirmation email: %w", err)
	}

	// Log email
	s.logEmail(models.EmailLog{
		ToEmail:   data.CustomerEmail,
		FromEmail: s.from,
		Subject:   subject,
		Body:      htmlContent,
		Status:    models.EmailStatusSent,
		SentAt:    &time.Time{},
	})

	return nil
}

// SendInvoiceNotification sends an invoice notification email
func (s *EmailService) SendInvoiceNotification(data InvoiceNotificationData) error {
	subject := fmt.Sprintf("New Invoice %s - $%.2f", data.InvoiceNumber, data.InvoiceAmount)

	// Generate HTML content
	htmlContent, err := s.generateInvoiceNotificationHTML(data)
	if err != nil {
		return fmt.Errorf("failed to generate email content: %w", err)
	}

	// Generate plain text content
	textContent := s.generateInvoiceNotificationText(data)

	// Send email
	if err := s.sendEmail(data.CustomerEmail, subject, htmlContent, textContent); err != nil {
		return fmt.Errorf("failed to send invoice notification email: %w", err)
	}

	// Log email
	s.logEmail(models.EmailLog{
		ToEmail:   data.CustomerEmail,
		FromEmail: s.from,
		Subject:   subject,
		Body:      htmlContent,
		Status:    models.EmailStatusSent,
		SentAt:    &time.Time{},
	})

	return nil
}

// sendEmail sends an email using SMTP
func (s *EmailService) sendEmail(to, subject, htmlBody, textBody string) error {
	// Create message
	message := s.createMIMEMessage(to, subject, htmlBody, textBody)

	// Set up authentication
	auth := smtp.PlainAuth("", s.username, s.password, s.host)

	// Send email
	addr := fmt.Sprintf("%s:%d", s.host, s.port)
	if err := smtp.SendMail(addr, auth, s.from, []string{to}, []byte(message)); err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	return nil
}

// createMIMEMessage creates a MIME message with both HTML and text parts
func (s *EmailService) createMIMEMessage(to, subject, htmlBody, textBody string) string {
	boundary := "boundary123456789"

	message := fmt.Sprintf(`From: %s
To: %s
Subject: %s
MIME-Version: 1.0
Content-Type: multipart/alternative; boundary="%s"

--%s
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit

%s

--%s
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: 7bit

%s

--%s--
`, s.from, to, subject, boundary, boundary, textBody, boundary, htmlBody, boundary)

	return message
}

// generatePaymentConfirmationHTML generates HTML content for payment confirmation
func (s *EmailService) generatePaymentConfirmationHTML(data PaymentConfirmationData) (string, error) {
	tmpl := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2980b9; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .payment-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .success { color: #27ae60; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Payment Confirmation</h1>
        </div>
        <div class="content">
            <p>Dear {{.CustomerName}},</p>
            <p class="success">✓ Your payment has been successfully processed!</p>
            <p>Thank you for your payment. Here are the details:</p>

            <div class="payment-details">
                <h3>Payment Details</h3>
                <p><strong>Invoice Number:</strong> {{.InvoiceNumber}}</p>
                <p><strong>Amount Paid:</strong> ${{printf "%.2f" .PaymentAmount}}</p>
                <p><strong>Payment Date:</strong> {{.PaymentDate}}</p>
                <p><strong>Payment Method:</strong> {{.PaymentMethod}}</p>
                <p><strong>Transaction ID:</strong> {{.TransactionID}}</p>
            </div>

            <p>If you have any questions about this payment, please don't hesitate to contact us.</p>
            <p>Thank you for your business!</p>
        </div>
        <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>&copy; {{.CompanyName}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`

	t, err := template.New("payment_confirmation").Parse(tmpl)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	if err := t.Execute(&buf, data); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// generatePaymentConfirmationText generates plain text content for payment confirmation
func (s *EmailService) generatePaymentConfirmationText(data PaymentConfirmationData) string {
	return fmt.Sprintf(`Payment Confirmation - Invoice %s

Dear %s,

Your payment has been successfully processed!

Payment Details:
- Invoice Number: %s
- Amount Paid: $%.2f
- Payment Date: %s
- Payment Method: %s
- Transaction ID: %s

Thank you for your business!

%s`,
		data.InvoiceNumber,
		data.CustomerName,
		data.InvoiceNumber,
		data.PaymentAmount,
		data.PaymentDate,
		data.PaymentMethod,
		data.TransactionID,
		data.CompanyName)
}

// generateInvoiceNotificationHTML generates HTML content for invoice notification
func (s *EmailService) generateInvoiceNotificationHTML(data InvoiceNotificationData) (string, error) {
	tmpl := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Invoice</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2980b9; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .invoice-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .cta-button {
            display: inline-block;
            background-color: #27ae60;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>New Invoice</h1>
        </div>
        <div class="content">
            <p>Dear {{.CustomerName}},</p>
            <p>You have received a new invoice. Please review the details below:</p>

            <div class="invoice-details">
                <h3>Invoice Details</h3>
                <p><strong>Invoice Number:</strong> {{.InvoiceNumber}}</p>
                <p><strong>Amount Due:</strong> ${{printf "%.2f" .InvoiceAmount}}</p>
                <p><strong>Due Date:</strong> {{.DueDate}}</p>
            </div>

            <p style="text-align: center;">
                <a href="{{.InvoiceURL}}" class="cta-button">View & Pay Invoice</a>
            </p>

            <p>You can view the full invoice details and make a payment by clicking the button above.</p>
            <p>If you have any questions, please don't hesitate to contact us.</p>
        </div>
        <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>&copy; {{.CompanyName}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`

	t, err := template.New("invoice_notification").Parse(tmpl)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	if err := t.Execute(&buf, data); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// generateInvoiceNotificationText generates plain text content for invoice notification
func (s *EmailService) generateInvoiceNotificationText(data InvoiceNotificationData) string {
	return fmt.Sprintf(`New Invoice %s

Dear %s,

You have received a new invoice.

Invoice Details:
- Invoice Number: %s
- Amount Due: $%.2f
- Due Date: %s

View and pay your invoice: %s

Thank you for your business!

%s`,
		data.InvoiceNumber,
		data.CustomerName,
		data.InvoiceNumber,
		data.InvoiceAmount,
		data.DueDate,
		data.InvoiceURL,
		data.CompanyName)
}

// logEmail logs email sending to the database
func (s *EmailService) logEmail(log models.EmailLog) {
	// Set current time if not set
	now := time.Now()
	if log.SentAt == nil {
		log.SentAt = &now
	}

	// Create email log entry
	s.db.Create(&log)
}

// TestConnection tests the email service connection
func (s *EmailService) TestConnection() error {
	// Try to connect to SMTP server
	addr := fmt.Sprintf("%s:%d", s.host, s.port)
	conn, err := smtp.Dial(addr)
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer conn.Close()

	return nil
}

// getEnv gets environment variable with fallback
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
