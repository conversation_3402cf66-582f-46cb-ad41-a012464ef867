package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// BudgetService handles budget-related business logic
type BudgetService struct {
	db *gorm.DB
}

// NewBudgetService creates a new BudgetService
func NewBudgetService(db *gorm.DB) *BudgetService {
	return &BudgetService{db: db}
}

// GetAllBudgetItems retrieves all budget items with pagination
func (s *BudgetService) GetAllBudgetItems(page, limit int, search string) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemByID retrieves a budget item by ID
func (s *BudgetService) GetBudgetItemByID(id string) (*models.BudgetItem, error) {
	var budgetItem models.BudgetItem

	if err := s.db.Preload("Merchant").Preload("Account").
		First(&budgetItem, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("budget item not found")
		}
		return nil, fmt.Errorf("failed to fetch budget item: %w", err)
	}

	return &budgetItem, nil
}

// GetBudgetItemsByMerchant retrieves budget items for a specific merchant
func (s *BudgetService) GetBudgetItemsByMerchant(merchantID string, page, limit int, search, accountID string, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{}).Where("merchant_id = ?", merchantID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply account filter if provided
	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	// Apply year filter if provided
	if year != nil {
		query = query.Where("year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemsByAccount retrieves budget items for a specific account
func (s *BudgetService) GetBudgetItemsByAccount(accountID string, page, limit int, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{}).Where("account_id = ?", accountID)

	// Apply year filter if provided
	if year != nil {
		query = query.Where("year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemsByBranch retrieves budget items for a specific branch
func (s *BudgetService) GetBudgetItemsByBranch(branchID string, page, limit int, search, accountID string, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply account filter if provided
	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	// Apply year filter if provided
	if year != nil {
		query = query.Where("year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemsByOrganization retrieves budget items for all branches in an organization
func (s *BudgetService) GetBudgetItemsByOrganization(organizationID string, page, limit int, search, accountID string, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	// Join with branches to filter by organization
	query := s.db.Model(&models.BudgetItem{}).
		Joins("JOIN branches ON budget_items.branch_id = branches.id").
		Where("branches.organization_id = ?", organizationID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply account filter if provided
	if accountID != "" {
		query = query.Where("budget_items.account_id = ?", accountID)
	}

	// Apply year filter if provided
	if year != nil {
		query = query.Where("budget_items.year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("budget_items.month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("budget_items.year DESC, budget_items.month DESC, budget_items.created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// CreateBudgetItem creates a new budget item
func (s *BudgetService) CreateBudgetItem(budgetItem *models.BudgetItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", budgetItem.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate account exists and belongs to branch
		var account models.ChartOfAccount
		if err := tx.First(&account, "id = ? AND branch_id = ?", budgetItem.AccountID, budgetItem.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("account not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate account: %w", err)
		}

		// Check if budget item already exists for this branch, account, year, and month
		var existingItem models.BudgetItem
		if err := tx.First(&existingItem, "branch_id = ? AND account_id = ? AND year = ? AND month = ?",
			budgetItem.BranchID, budgetItem.AccountID, budgetItem.Year, budgetItem.Month).Error; err == nil {
			return fmt.Errorf("budget item already exists for this account, year, and month")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to check existing budget item: %w", err)
		}

		// Validate budget item
		if err := s.ValidateBudgetItem(budgetItem); err != nil {
			return err
		}

		// Create the budget item
		if err := tx.Create(budgetItem).Error; err != nil {
			return fmt.Errorf("failed to create budget item: %w", err)
		}

		return nil
	})
}

// UpdateBudgetItem updates an existing budget item
func (s *BudgetService) UpdateBudgetItem(id string, updates *models.BudgetItem) (*models.BudgetItem, error) {
	var budgetItem models.BudgetItem

	// Check if budget item exists
	if err := s.db.First(&budgetItem, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("budget item not found")
		}
		return nil, fmt.Errorf("failed to fetch budget item: %w", err)
	}

	var updatedItem *models.BudgetItem
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// If updating account, year, or month, check for duplicates
		if (updates.AccountID != "" && updates.AccountID != budgetItem.AccountID) ||
			(updates.Year != 0 && updates.Year != budgetItem.Year) ||
			(updates.Month != 0 && updates.Month != budgetItem.Month) {

			accountID := budgetItem.AccountID
			year := budgetItem.Year
			month := budgetItem.Month

			if updates.AccountID != "" {
				accountID = updates.AccountID
			}
			if updates.Year != 0 {
				year = updates.Year
			}
			if updates.Month != 0 {
				month = updates.Month
			}

			var existingItem models.BudgetItem
			if err := tx.Where("branch_id = ? AND account_id = ? AND year = ? AND month = ? AND id != ?",
				budgetItem.BranchID, accountID, year, month, id).
				First(&existingItem).Error; err == nil {
				return fmt.Errorf("budget item already exists for this account, year, and month")
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("failed to check existing budget item: %w", err)
			}
		}

		// Update the budget item
		if err := tx.Model(&budgetItem).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update budget item: %w", err)
		}

		// Fetch updated budget item with relationships
		if err := tx.Preload("Merchant").Preload("Account").
			First(&budgetItem, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated budget item: %w", err)
		}

		updatedItem = &budgetItem
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedItem, nil
}

// DeleteBudgetItem deletes a budget item
func (s *BudgetService) DeleteBudgetItem(id string) error {
	var budgetItem models.BudgetItem

	// Check if budget item exists
	if err := s.db.First(&budgetItem, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("budget item not found")
		}
		return fmt.Errorf("failed to fetch budget item: %w", err)
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete the budget item
		if err := tx.Delete(&budgetItem).Error; err != nil {
			return fmt.Errorf("failed to delete budget item: %w", err)
		}

		return nil
	})
}

// GetBudgetReport generates a budget report for a specific period
func (s *BudgetService) GetBudgetReport(branchID string, year, month int) (*BudgetReport, error) {
	var report BudgetReport

	// Get budget items for the period
	var budgetItems []models.BudgetItem
	if err := s.db.Preload("Account").
		Where("branch_id = ? AND year = ? AND month = ?", branchID, year, month).
		Find(&budgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	// Calculate totals
	var totalBudgeted, totalActual decimal.Decimal
	var reportItems []BudgetReportItem

	for _, item := range budgetItems {
		totalBudgeted = totalBudgeted.Add(item.Amount)
		totalActual = totalActual.Add(item.ActualAmount)

		variance := item.ActualAmount.Sub(item.Amount)
		variancePercent := decimal.Zero
		if !item.Amount.IsZero() {
			variancePercent = variance.Div(item.Amount).Mul(decimal.NewFromInt(100))
		}

		reportItems = append(reportItems, BudgetReportItem{
			AccountID:       item.AccountID,
			AccountName:     item.Account.Name,
			AccountCode:     item.Account.Code,
			BudgetedAmount:  item.Amount,
			ActualAmount:    item.ActualAmount,
			Variance:        variance,
			VariancePercent: variancePercent,
		})
	}

	// Calculate overall variance
	totalVariance := totalActual.Sub(totalBudgeted)
	totalVariancePercent := decimal.Zero
	if !totalBudgeted.IsZero() {
		totalVariancePercent = totalVariance.Div(totalBudgeted).Mul(decimal.NewFromInt(100))
	}

	report = BudgetReport{
		BranchID:             branchID,
		Year:                 year,
		Month:                month,
		TotalBudgeted:        totalBudgeted,
		TotalActual:          totalActual,
		TotalVariance:        totalVariance,
		TotalVariancePercent: totalVariancePercent,
		Items:                reportItems,
		GeneratedAt:          time.Now(),
	}

	return &report, nil
}

// GetBudgetReportByOrganization generates a budget report for all branches in an organization
func (s *BudgetService) GetBudgetReportByOrganization(organizationID string, year, month int) (*BudgetReport, error) {
	var report BudgetReport

	// Get budget items for all branches in the organization
	var budgetItems []models.BudgetItem
	if err := s.db.Preload("Account").Preload("Branch").
		Joins("JOIN branches ON budget_items.branch_id = branches.id").
		Where("branches.organization_id = ? AND budget_items.year = ? AND budget_items.month = ?", organizationID, year, month).
		Find(&budgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	// Calculate totals
	var totalBudgeted, totalActual decimal.Decimal
	var reportItems []BudgetReportItem

	for _, item := range budgetItems {
		totalBudgeted = totalBudgeted.Add(item.Amount)
		totalActual = totalActual.Add(item.ActualAmount)

		variance := item.ActualAmount.Sub(item.Amount)
		variancePercent := decimal.Zero
		if !item.Amount.IsZero() {
			variancePercent = variance.Div(item.Amount).Mul(decimal.NewFromInt(100))
		}

		reportItems = append(reportItems, BudgetReportItem{
			AccountID:       item.AccountID,
			AccountName:     item.Account.Name,
			AccountCode:     item.Account.Code,
			BudgetedAmount:  item.Amount,
			ActualAmount:    item.ActualAmount,
			Variance:        variance,
			VariancePercent: variancePercent,
		})
	}

	// Calculate overall variance
	totalVariance := totalActual.Sub(totalBudgeted)
	totalVariancePercent := decimal.Zero
	if !totalBudgeted.IsZero() {
		totalVariancePercent = totalVariance.Div(totalBudgeted).Mul(decimal.NewFromInt(100))
	}

	report = BudgetReport{
		BranchID:             organizationID, // Using organizationID as identifier
		Year:                 year,
		Month:                month,
		TotalBudgeted:        totalBudgeted,
		TotalActual:          totalActual,
		TotalVariance:        totalVariance,
		TotalVariancePercent: totalVariancePercent,
		Items:                reportItems,
		GeneratedAt:          time.Now(),
	}

	return &report, nil
}

// GetBudgetSummary gets summary statistics for budget items
func (s *BudgetService) GetBudgetSummary(branchID string) (*BudgetSummary, error) {
	var summary BudgetSummary

	// Get total count
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ?", branchID).
		Count(&summary.TotalBudgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Get current year budget items
	currentYear := time.Now().Year()
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ? AND year = ?", branchID, currentYear).
		Count(&summary.CurrentYearItems).Error; err != nil {
		return nil, fmt.Errorf("failed to count current year budget items: %w", err)
	}

	// Get budget by year
	yearCounts := []struct {
		Year  int
		Count int64
	}{}

	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ?", branchID).
		Select("year, COUNT(*) as count").
		Group("year").
		Order("year DESC").
		Scan(&yearCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get year counts: %w", err)
	}

	summary.YearCounts = make(map[int]int64)
	for _, yc := range yearCounts {
		summary.YearCounts[yc.Year] = yc.Count
	}

	// Get budget by account
	accountCounts := []struct {
		AccountID string
		Count     int64
	}{}

	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ?", branchID).
		Select("account_id, COUNT(*) as count").
		Group("account_id").
		Scan(&accountCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get account counts: %w", err)
	}

	summary.AccountCounts = make(map[string]int64)
	for _, ac := range accountCounts {
		summary.AccountCounts[ac.AccountID] = ac.Count
	}

	// Get total budgeted amount for current year
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ? AND year = ?", branchID, currentYear).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&summary.TotalBudgetedCurrentYear).Error; err != nil {
		return nil, fmt.Errorf("failed to sum budgeted amounts: %w", err)
	}

	// Get total actual amount for current year
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ? AND year = ?", branchID, currentYear).
		Select("COALESCE(SUM(actual_amount), 0)").
		Scan(&summary.TotalActualCurrentYear).Error; err != nil {
		return nil, fmt.Errorf("failed to sum actual amounts: %w", err)
	}

	return &summary, nil
}

// UpdateActualAmounts updates actual amounts for budget items based on transactions
func (s *BudgetService) UpdateActualAmounts(branchID string, year, month int) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get budget items for the period
		var budgetItems []models.BudgetItem
		if err := tx.Where("branch_id = ? AND year = ? AND month = ?", branchID, year, month).
			Find(&budgetItems).Error; err != nil {
			return fmt.Errorf("failed to fetch budget items: %w", err)
		}

		// Calculate start and end dates for the month
		startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
		endDate := startDate.AddDate(0, 1, -1)

		for _, item := range budgetItems {
			// Calculate actual amount from journal entries
			var actualAmount decimal.Decimal

			// Sum debit amounts for the account in the period
			var debitSum decimal.Decimal
			if err := tx.Model(&models.JournalEntry{}).
				Where("branch_id = ? AND account_id = ? AND entry_date BETWEEN ? AND ?",
					branchID, item.AccountID, startDate, endDate).
				Select("COALESCE(SUM(debit_amount), 0)").
				Scan(&debitSum).Error; err != nil {
				return fmt.Errorf("failed to sum debit amounts: %w", err)
			}

			// Sum credit amounts for the account in the period
			var creditSum decimal.Decimal
			if err := tx.Model(&models.JournalEntry{}).
				Where("branch_id = ? AND account_id = ? AND entry_date BETWEEN ? AND ?",
					branchID, item.AccountID, startDate, endDate).
				Select("COALESCE(SUM(credit_amount), 0)").
				Scan(&creditSum).Error; err != nil {
				return fmt.Errorf("failed to sum credit amounts: %w", err)
			}

			// Calculate net amount based on account type
			// For expense accounts, actual = debit - credit
			// For revenue accounts, actual = credit - debit
			// This is a simplified calculation - in reality, you'd need to check account type
			actualAmount = debitSum.Sub(creditSum)

			// Update the budget item with actual amount
			if err := tx.Model(&item).Update("actual_amount", actualAmount).Error; err != nil {
				return fmt.Errorf("failed to update actual amount: %w", err)
			}
		}

		return nil
	})
}

// ValidateBudgetItem validates budget item data
func (s *BudgetService) ValidateBudgetItem(budgetItem *models.BudgetItem) error {
	if budgetItem.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if budgetItem.AccountID == "" {
		return fmt.Errorf("account ID is required")
	}
	if budgetItem.Year < 1900 || budgetItem.Year > 2100 {
		return fmt.Errorf("year must be between 1900 and 2100")
	}
	if budgetItem.Month < 1 || budgetItem.Month > 12 {
		return fmt.Errorf("month must be between 1 and 12")
	}
	if budgetItem.Amount.LessThan(decimal.Zero) {
		return fmt.Errorf("budget amount cannot be negative")
	}

	return nil
}

// BudgetReport represents a budget report for a specific period
type BudgetReport struct {
	BranchID             string             `json:"branchId"`
	Year                 int                `json:"year"`
	Month                int                `json:"month"`
	TotalBudgeted        decimal.Decimal    `json:"totalBudgeted"`
	TotalActual          decimal.Decimal    `json:"totalActual"`
	TotalVariance        decimal.Decimal    `json:"totalVariance"`
	TotalVariancePercent decimal.Decimal    `json:"totalVariancePercent"`
	Items                []BudgetReportItem `json:"items"`
	GeneratedAt          time.Time          `json:"generatedAt"`
}

// BudgetReportItem represents a single item in a budget report
type BudgetReportItem struct {
	AccountID       string          `json:"accountId"`
	AccountName     string          `json:"accountName"`
	AccountCode     string          `json:"accountCode"`
	BudgetedAmount  decimal.Decimal `json:"budgetedAmount"`
	ActualAmount    decimal.Decimal `json:"actualAmount"`
	Variance        decimal.Decimal `json:"variance"`
	VariancePercent decimal.Decimal `json:"variancePercent"`
}

// BudgetSummary represents summary statistics for budget items
type BudgetSummary struct {
	TotalBudgetItems         int64            `json:"totalBudgetItems"`
	CurrentYearItems         int64            `json:"currentYearItems"`
	YearCounts               map[int]int64    `json:"yearCounts"`
	AccountCounts            map[string]int64 `json:"accountCounts"`
	TotalBudgetedCurrentYear decimal.Decimal  `json:"totalBudgetedCurrentYear"`
	TotalActualCurrentYear   decimal.Decimal  `json:"totalActualCurrentYear"`
}
