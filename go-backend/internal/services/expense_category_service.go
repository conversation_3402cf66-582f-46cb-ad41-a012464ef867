package services

import (
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-account-backend/internal/models"
)

type ExpenseCategoryService struct {
	db *gorm.DB
}

func NewExpenseCategoryService(db *gorm.DB) *ExpenseCategoryService {
	return &ExpenseCategoryService{db: db}
}

// Request/Response types
type CreateExpenseCategoryRequest struct {
	BranchID    string  `json:"branchId" binding:"required"`
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
	ParentID    *string `json:"parentId"`
	AccountID   *string `json:"accountId"`
	SortOrder   int     `json:"sortOrder"`
}

type UpdateExpenseCategoryRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	ParentID    *string `json:"parentId"`
	AccountID   *string `json:"accountId"`
	IsActive    *bool   `json:"isActive"`
	SortOrder   *int    `json:"sortOrder"`
}

type ExpenseCategoryResponse struct {
	ID          string                    `json:"id"`
	BranchID    string                    `json:"branchId"`
	Name        string                    `json:"name"`
	Description *string                   `json:"description"`
	ParentID    *string                   `json:"parentId"`
	ParentName  *string                   `json:"parentName"`
	AccountID   *string                   `json:"accountId"`
	AccountName *string                   `json:"accountName"`
	IsActive    bool                      `json:"isActive"`
	SortOrder   int                       `json:"sortOrder"`
	CreatedAt   string                    `json:"createdAt"`
	UpdatedAt   string                    `json:"updatedAt"`
	Children    []ExpenseCategoryResponse `json:"children,omitempty"`
}

// GetAllExpenseCategories returns all expense categories with pagination
func (s *ExpenseCategoryService) GetAllExpenseCategories(page, limit int, userID string) ([]ExpenseCategoryResponse, int64, error) {
	var categories []models.ExpenseCategory
	var total int64

	// Build query to get categories from branches user has access to
	query := s.db.Model(&models.ExpenseCategory{}).
		Where(`
			expense_categories.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expense_categories.branch_id IN (
				SELECT merchant_id FROM user_merchant_permissions WHERE user_id = ?
			)
		`, userID, userID)

	// Count total categories
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get categories with pagination and preload relationships
	offset := (page - 1) * limit
	if err := query.Preload("Parent").
		Preload("Account").
		Preload("Children").
		Order("sort_order ASC, name ASC").
		Offset(offset).Limit(limit).Find(&categories).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]ExpenseCategoryResponse, 0, len(categories))
	for _, category := range categories {
		responses = append(responses, s.toExpenseCategoryResponse(category))
	}

	return responses, total, nil
}

// GetExpenseCategoriesByBranch returns categories for a specific branch
func (s *ExpenseCategoryService) GetExpenseCategoriesByBranch(branchID string, userID string) ([]ExpenseCategoryResponse, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, branchID) {
		return nil, errors.New("access denied to this branch")
	}

	var categories []models.ExpenseCategory

	// Get categories for this branch
	if err := s.db.Where("branch_id = ? AND is_active = ?", branchID, true).
		Preload("Parent").
		Preload("Account").
		Preload("Children").
		Order("sort_order ASC, name ASC").
		Find(&categories).Error; err != nil {
		return nil, err
	}

	// Convert to response format
	responses := make([]ExpenseCategoryResponse, 0, len(categories))
	for _, category := range categories {
		responses = append(responses, s.toExpenseCategoryResponse(category))
	}

	return responses, nil
}

// GetExpenseCategoryByID returns a category by ID
func (s *ExpenseCategoryService) GetExpenseCategoryByID(id, userID string) (*ExpenseCategoryResponse, error) {
	var category models.ExpenseCategory

	// Get category and check access through branch permissions
	if err := s.db.Where(`
		expense_categories.id = ? AND (
			expense_categories.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expense_categories.branch_id IN (
				SELECT merchant_id FROM user_merchant_permissions WHERE user_id = ?
			)
		)
	`, id, userID, userID).
		Preload("Parent").
		Preload("Account").
		Preload("Children").
		First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("expense category not found or access denied")
		}
		return nil, err
	}

	response := s.toExpenseCategoryResponse(category)
	return &response, nil
}

// CreateExpenseCategory creates a new expense category
func (s *ExpenseCategoryService) CreateExpenseCategory(req CreateExpenseCategoryRequest, userID string) (*ExpenseCategoryResponse, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, req.BranchID) {
		return nil, errors.New("access denied to this branch")
	}

	// Validate parent category if provided
	if req.ParentID != nil && *req.ParentID != "" {
		var parent models.ExpenseCategory
		if err := s.db.Where("id = ? AND branch_id = ?", *req.ParentID, req.BranchID).First(&parent).Error; err != nil {
			return nil, errors.New("parent category not found or does not belong to this branch")
		}
	}

	// Validate account if provided
	if req.AccountID != nil && *req.AccountID != "" {
		var account models.ChartOfAccount
		if err := s.db.Where("id = ? AND branch_id = ?", *req.AccountID, req.BranchID).First(&account).Error; err != nil {
			return nil, errors.New("account not found or does not belong to this branch")
		}
	}

	// Create category
	category := models.ExpenseCategory{
		ID:          uuid.New().String(),
		BranchID:    req.BranchID,
		Name:        req.Name,
		Description: req.Description,
		ParentID:    req.ParentID,
		AccountID:   req.AccountID,
		IsActive:    true,
		SortOrder:   req.SortOrder,
	}

	if err := s.db.Create(&category).Error; err != nil {
		return nil, err
	}

	// Reload category with relationships
	if err := s.db.Preload("Parent").
		Preload("Account").
		Preload("Children").
		Where("id = ?", category.ID).First(&category).Error; err != nil {
		return nil, err
	}

	response := s.toExpenseCategoryResponse(category)
	return &response, nil
}

// UpdateExpenseCategory updates an existing expense category
func (s *ExpenseCategoryService) UpdateExpenseCategory(id string, req UpdateExpenseCategoryRequest, userID string) (*ExpenseCategoryResponse, error) {
	var category models.ExpenseCategory

	// Get category and check access through branch permissions
	if err := s.db.Where(`
		expense_categories.id = ? AND (
			expense_categories.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expense_categories.branch_id IN (
				SELECT merchant_id FROM user_merchant_permissions WHERE user_id = ?
			)
		)
	`, id, userID, userID).
		First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("expense category not found or access denied")
		}
		return nil, err
	}

	// Update fields
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.ParentID != nil {
		// Validate parent category if provided
		if *req.ParentID != "" {
			var parent models.ExpenseCategory
			if err := s.db.Where("id = ? AND branch_id = ?", *req.ParentID, category.BranchID).First(&parent).Error; err != nil {
				return nil, errors.New("parent category not found or does not belong to this branch")
			}
		}
		updates["parent_id"] = req.ParentID
	}
	if req.AccountID != nil {
		// Validate account if provided
		if *req.AccountID != "" {
			var account models.ChartOfAccount
			if err := s.db.Where("id = ? AND branch_id = ?", *req.AccountID, category.BranchID).First(&account).Error; err != nil {
				return nil, errors.New("account not found or does not belong to this branch")
			}
		}
		updates["account_id"] = req.AccountID
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
	}

	if len(updates) > 0 {
		if err := s.db.Model(&category).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload category with relationships
	if err := s.db.Preload("Parent").
		Preload("Account").
		Preload("Children").
		Where("id = ?", id).First(&category).Error; err != nil {
		return nil, err
	}

	response := s.toExpenseCategoryResponse(category)
	return &response, nil
}

// DeleteExpenseCategory soft deletes an expense category
func (s *ExpenseCategoryService) DeleteExpenseCategory(id, userID string) error {
	var category models.ExpenseCategory

	// Get category and check access through branch permissions
	if err := s.db.Where(`
		expense_categories.id = ? AND (
			expense_categories.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expense_categories.branch_id IN (
				SELECT merchant_id FROM user_merchant_permissions WHERE user_id = ?
			)
		)
	`, id, userID, userID).
		First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("expense category not found or access denied")
		}
		return err
	}

	// Check if category has expenses
	var expenseCount int64
	if err := s.db.Model(&models.Expense{}).Where("expense_category_id = ?", id).Count(&expenseCount).Error; err != nil {
		return err
	}

	if expenseCount > 0 {
		return errors.New("cannot delete category that has associated expenses")
	}

	// Check if category has children
	var childrenCount int64
	if err := s.db.Model(&models.ExpenseCategory{}).Where("parent_id = ?", id).Count(&childrenCount).Error; err != nil {
		return err
	}

	if childrenCount > 0 {
		return errors.New("cannot delete category that has child categories")
	}

	// Soft delete by setting is_active to false
	return s.db.Model(&category).Update("is_active", false).Error
}

// Helper methods
func (s *ExpenseCategoryService) hasBranchAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserBranchPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	if count > 0 {
		return true
	}

	// Check merchant permissions for backward compatibility
	s.db.Model(&models.UserMerchantPermission{}).
		Where("user_id = ? AND merchant_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

func (s *ExpenseCategoryService) toExpenseCategoryResponse(category models.ExpenseCategory) ExpenseCategoryResponse {
	var parentName *string
	if category.Parent != nil {
		parentName = &category.Parent.Name
	}

	var accountName *string
	if category.Account != nil {
		accountName = &category.Account.Name
	}

	// Convert children
	children := make([]ExpenseCategoryResponse, 0, len(category.Children))
	for _, child := range category.Children {
		children = append(children, s.toExpenseCategoryResponse(child))
	}

	return ExpenseCategoryResponse{
		ID:          category.ID,
		BranchID:    category.BranchID,
		Name:        category.Name,
		Description: category.Description,
		ParentID:    category.ParentID,
		ParentName:  parentName,
		AccountID:   category.AccountID,
		AccountName: accountName,
		IsActive:    category.IsActive,
		SortOrder:   category.SortOrder,
		CreatedAt:   category.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   category.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Children:    children,
	}
}
