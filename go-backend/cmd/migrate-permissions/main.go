package main

import (
	"fmt"
	"log"
	"time"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Configure GORM to use the correct table names (PascalCase)
	db.NamingStrategy = nil // Use struct names as table names

	log.Println("Starting migration: Merchant permissions to Branch permissions")

	// Step 1: Create organizations for merchants that don't have them
	if err := createOrganizationsForMerchants(db); err != nil {
		log.Fatalf("Failed to create organizations: %v", err)
	}

	// Step 2: Create branches for merchants
	if err := createBranchesForMerchants(db); err != nil {
		log.Fatalf("Failed to create branches: %v", err)
	}

	// Step 3: Migrate merchant permissions to branch permissions
	if err := migrateMerchantPermissionsToBranch(db); err != nil {
		log.Fatalf("Failed to migrate permissions: %v", err)
	}

	log.Println("Migration completed successfully!")
}

func createOrganizationsForMerchants(db *gorm.DB) error {
	log.Println("Creating organizations for merchants...")

	var merchants []models.Merchant
	if err := db.Find(&merchants).Error; err != nil {
		return fmt.Errorf("failed to fetch merchants: %v", err)
	}

	for _, merchant := range merchants {
		// Check if organization already exists for this merchant
		var existingOrg models.Organization
		orgName := merchant.Name + " Organization"
		if err := db.Where("name = ?", orgName).First(&existingOrg).Error; err == nil {
			log.Printf("Organization already exists for merchant %s", merchant.Name)
			continue
		}

		// Create organization
		org := models.Organization{
			ID:          uuid.New().String(),
			Name:        orgName,
			Description: stringPtr("Auto-created organization for merchant: " + merchant.Name),
			Address:     merchant.Address,
			Phone:       merchant.Phone,
			Email:       merchant.PrimaryContactEmail,
			Currency:    merchant.Currency,
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		if err := db.Create(&org).Error; err != nil {
			return fmt.Errorf("failed to create organization for merchant %s: %v", merchant.Name, err)
		}

		log.Printf("Created organization %s for merchant %s", org.Name, merchant.Name)
	}

	return nil
}

func createBranchesForMerchants(db *gorm.DB) error {
	log.Println("Creating branches for merchants...")

	var merchants []models.Merchant
	if err := db.Find(&merchants).Error; err != nil {
		return fmt.Errorf("failed to fetch merchants: %v", err)
	}

	for _, merchant := range merchants {
		// Find the organization for this merchant
		var org models.Organization
		if err := db.Where("name = ?", merchant.Name+" Organization").First(&org).Error; err != nil {
			log.Printf("No organization found for merchant %s, skipping", merchant.Name)
			continue
		}

		// Check if branch already exists
		var existingBranch models.Branch
		if err := db.Where("organization_id = ? AND name = ?", org.ID, "Main Branch").First(&existingBranch).Error; err == nil {
			log.Printf("Branch already exists for merchant %s", merchant.Name)
			continue
		}

		// Create branch
		branch := models.Branch{
			ID:             merchant.ID, // Use merchant ID as branch ID for easy mapping
			OrganizationID: org.ID,
			Name:           "Main Branch",
			Description:    stringPtr("Main branch for " + merchant.Name),
			Address:        merchant.Address,
			Phone:          merchant.Phone,
			Email:          merchant.PrimaryContactEmail,
			IsActive:       true,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		if err := db.Create(&branch).Error; err != nil {
			return fmt.Errorf("failed to create branch for merchant %s: %v", merchant.Name, err)
		}

		log.Printf("Created branch %s for merchant %s", branch.Name, merchant.Name)
	}

	return nil
}

func migrateMerchantPermissionsToBranch(db *gorm.DB) error {
	log.Println("Migrating merchant permissions to branch permissions...")

	var merchantPermissions []models.UserMerchantPermission
	if err := db.Find(&merchantPermissions).Error; err != nil {
		return fmt.Errorf("failed to fetch merchant permissions: %v", err)
	}

	for _, merchantPerm := range merchantPermissions {
		// Check if branch permission already exists
		var existingBranchPerm models.UserBranchPermission
		if err := db.Where("user_id = ? AND branch_id = ?", merchantPerm.UserID, merchantPerm.MerchantID).First(&existingBranchPerm).Error; err == nil {
			log.Printf("Branch permission already exists for user %s and branch %s", merchantPerm.UserID, merchantPerm.MerchantID)
			continue
		}

		// Create branch permission (using merchant ID as branch ID)
		branchPerm := models.UserBranchPermission{
			ID:                uuid.New().String(),
			UserID:            merchantPerm.UserID,
			BranchID:          merchantPerm.MerchantID, // Merchant ID becomes Branch ID
			PermissionLevel:   merchantPerm.PermissionLevel,
			CustomPermissions: merchantPerm.CustomPermissions,
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
		}

		if err := db.Create(&branchPerm).Error; err != nil {
			return fmt.Errorf("failed to create branch permission for user %s: %v", merchantPerm.UserID, err)
		}

		log.Printf("Migrated permission for user %s from merchant %s to branch %s",
			merchantPerm.UserID, merchantPerm.MerchantID, merchantPerm.MerchantID)
	}

	return nil
}

func stringPtr(s string) *string {
	return &s
}
