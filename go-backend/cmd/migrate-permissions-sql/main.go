package main

import (
	"adc-account-backend/internal/config"
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Connect to database
	db, err := sql.Open("postgres", cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	log.Println("Starting migration: Merchant permissions to Branch permissions")

	// Step 1: Create organizations for merchants that don't have them
	if err := createOrganizationsForMerchants(db); err != nil {
		log.Fatalf("Failed to create organizations: %v", err)
	}

	// Step 2: Create branches for merchants
	if err := createBranchesForMerchants(db); err != nil {
		log.Fatalf("Failed to create branches: %v", err)
	}

	// Step 3: Migrate merchant permissions to branch permissions
	if err := migrateMerchantPermissionsToBranch(db); err != nil {
		log.Fatalf("Failed to migrate permissions: %v", err)
	}

	log.Println("Migration completed successfully!")
}

func createOrganizationsForMerchants(db *sql.DB) error {
	log.Println("Creating organizations for merchants...")

	// Get all merchants
	rows, err := db.Query(`SELECT id, name, address, phone, primary_contact_email, currency FROM "Merchant"`)
	if err != nil {
		return fmt.Errorf("failed to fetch merchants: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var merchantID, name, currency string
		var address, phone, email sql.NullString

		if err := rows.Scan(&merchantID, &name, &address, &phone, &email, &currency); err != nil {
			return fmt.Errorf("failed to scan merchant: %v", err)
		}

		orgName := name + " Organization"

		// Check if organization already exists
		var existingOrgID string
		err := db.QueryRow(`SELECT id FROM "Organization" WHERE name = $1`, orgName).Scan(&existingOrgID)
		if err == nil {
			log.Printf("Organization already exists for merchant %s", name)
			continue
		}

		// Create organization
		orgID := uuid.New().String()
		now := time.Now()

		_, err = db.Exec(`
			INSERT INTO "Organization" (id, name, description, address, phone, email, currency, is_active, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		`, orgID, orgName, "Auto-created organization for merchant: "+name, address, phone, email, currency, true, now, now)

		if err != nil {
			return fmt.Errorf("failed to create organization for merchant %s: %v", name, err)
		}

		log.Printf("Created organization %s for merchant %s", orgName, name)
	}

	return nil
}

func createBranchesForMerchants(db *sql.DB) error {
	log.Println("Creating branches for merchants...")

	// Get all merchants
	rows, err := db.Query(`SELECT id, name, address, phone, primary_contact_email FROM "Merchant"`)
	if err != nil {
		return fmt.Errorf("failed to fetch merchants: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var merchantID, name string
		var address, phone, email sql.NullString

		if err := rows.Scan(&merchantID, &name, &address, &phone, &email); err != nil {
			return fmt.Errorf("failed to scan merchant: %v", err)
		}

		// Find the organization for this merchant
		var orgID string
		err := db.QueryRow(`SELECT id FROM "Organization" WHERE name = $1`, name+" Organization").Scan(&orgID)
		if err != nil {
			log.Printf("No organization found for merchant %s, skipping", name)
			continue
		}

		// Check if branch already exists
		var existingBranchID string
		err = db.QueryRow(`SELECT id FROM "Branch" WHERE organization_id = $1 AND name = $2`, orgID, "Main Branch").Scan(&existingBranchID)
		if err == nil {
			log.Printf("Branch already exists for merchant %s", name)
			continue
		}

		// Create branch (using merchant ID as branch ID for easy mapping)
		now := time.Now()

		_, err = db.Exec(`
			INSERT INTO "Branch" (id, organization_id, name, description, address, phone, email, is_active, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		`, merchantID, orgID, "Main Branch", "Main branch for "+name, address, phone, email, true, now, now)

		if err != nil {
			return fmt.Errorf("failed to create branch for merchant %s: %v", name, err)
		}

		log.Printf("Created branch Main Branch for merchant %s", name)
	}

	return nil
}

func migrateMerchantPermissionsToBranch(db *sql.DB) error {
	log.Println("Migrating merchant permissions to branch permissions...")

	// Get all merchant permissions
	rows, err := db.Query(`SELECT id, user_id, merchant_id, permission_level, custom_permissions FROM "UserMerchantPermission"`)
	if err != nil {
		return fmt.Errorf("failed to fetch merchant permissions: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var permID, userID, merchantID, permissionLevel string
		var customPermissions sql.NullString

		if err := rows.Scan(&permID, &userID, &merchantID, &permissionLevel, &customPermissions); err != nil {
			return fmt.Errorf("failed to scan merchant permission: %v", err)
		}

		// Check if branch permission already exists
		var existingBranchPermID string
		err := db.QueryRow(`SELECT id FROM "UserBranchPermission" WHERE user_id = $1 AND branch_id = $2`, userID, merchantID).Scan(&existingBranchPermID)
		if err == nil {
			log.Printf("Branch permission already exists for user %s and branch %s", userID, merchantID)
			continue
		}

		// Create branch permission (using merchant ID as branch ID)
		branchPermID := uuid.New().String()
		now := time.Now()

		_, err = db.Exec(`
			INSERT INTO "UserBranchPermission" (id, user_id, branch_id, permission_level, custom_permissions, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7)
		`, branchPermID, userID, merchantID, permissionLevel, customPermissions, now, now)

		if err != nil {
			return fmt.Errorf("failed to create branch permission for user %s: %v", userID, err)
		}

		log.Printf("Migrated permission for user %s from merchant %s to branch %s", userID, merchantID, merchantID)
	}

	return nil
}
