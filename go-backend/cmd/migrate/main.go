package main

import (
	"log"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
	"adc-account-backend/internal/models"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	log.Println("Running database migrations...")

	// Auto-migrate all models
	err = db.AutoMigrate(
		// Core models
		&models.User{},
		&models.Organization{},
		&models.Branch{},
		&models.UserOrganizationPermission{},
		&models.UserBranchPermission{},
		&models.UserMerchantPermission{},
		&models.UserPreferences{},
		&models.Merchant{},
		&models.ApiKey{},
		&models.AuditLog{},

		// Customer and Vendor models
		&models.Customer{},
		&models.Vendor{},

		// Accounting models
		&models.ChartOfAccount{},
		&models.JournalEntry{},
		&models.JournalEntryLine{},
		&models.TaxRate{},

		// Financial models
		&models.Invoice{},
		&models.InvoiceItem{},
		&models.InvoicePayment{},
		&models.Bill{},
		&models.BillItem{},
		&models.BillPayment{},
		&models.ExpenseCategory{},
		&models.Expense{},
		&models.ExpensePayment{},

		// Auth models
		&models.Account{},
		&models.Session{},
		&models.VerificationToken{},
		&models.RefreshToken{},
		&models.BlacklistedToken{},
		&models.LoginAttempt{},
		&models.PasswordReset{},
	)

	if err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	log.Println("Database migrations completed successfully!")
}
