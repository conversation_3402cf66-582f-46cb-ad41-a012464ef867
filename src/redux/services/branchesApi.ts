import { createApi } from '@reduxjs/toolkit/query/react';
import { authenticatedBaseQuery } from '../baseQuery';

// Define types for branch management
interface Branch {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
  branchCode?: string;
  managerName?: string;
  createdAt: string;
  updatedAt: string;
  organization?: {
    id: string;
    name: string;
    slug: string;
  };
  userPermissions?: any[];
}

// Define query parameters for branches list
export interface BranchesQueryParams {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Define the response type for paginated branches
export interface BranchesResponse {
  data: Branch[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Define request types
export interface CreateBranchRequest {
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  branchCode?: string;
  managerName?: string;
}

export interface UpdateBranchRequest {
  name?: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive?: boolean;
  branchCode?: string;
  managerName?: string;
}

// Create the branches API
export const branchesApi = createApi({
  reducerPath: 'branchesApi',
  baseQuery: authenticatedBaseQuery,
  tagTypes: ['Branch'],
  endpoints: (builder) => ({
    // Get all branches the user has access to with pagination and filtering
    getBranches: builder.query<BranchesResponse, BranchesQueryParams | void>({
      query: (params = {}) => {
        let url = 'branches';
        const queryParams = new URLSearchParams();

        // Add pagination parameters
        if (params.page) {
          queryParams.append('page', params.page.toString());
        }
        if (params.limit) {
          queryParams.append('limit', params.limit.toString());
        }

        // Add search parameter
        if (params.search) {
          queryParams.append('search', params.search);
        }

        // Add sorting parameters
        if (params.sortBy) {
          queryParams.append('sortBy', params.sortBy);
        }
        if (params.sortOrder) {
          queryParams.append('sortOrder', params.sortOrder);
        }

        const queryString = queryParams.toString();
        return queryString ? `${url}?${queryString}` : url;
      },
      providesTags: ['Branch'],
    }),

    // Get a specific branch by ID
    getBranchById: builder.query<Branch, string>({
      query: (id) => `branches/${id}`,
      providesTags: (result, error, id) => [{ type: 'Branch', id }],
    }),

    // Update a specific branch
    updateBranch: builder.mutation<Branch, { id: string; data: UpdateBranchRequest }>({
      query: ({ id, data }) => ({
        url: `branches/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Branch', id },
        'Branch',
      ],
    }),

    // Delete a branch
    deleteBranch: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `branches/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Branch', id },
        'Branch',
      ],
    }),
  }),
});

export const {
  useGetBranchesQuery,
  useGetBranchByIdQuery,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
} = branchesApi;
