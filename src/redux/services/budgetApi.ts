import { createApi } from '@reduxjs/toolkit/query/react';
import { BudgetItem, BudgetReport, BudgetTemplate } from '@/lib/types';
import { branchAwareBaseQuery } from '../baseQuery';

// Response interface for budget items from Rust backend
interface BudgetItemsResponse {
  items: BudgetItem[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    total_pages: number;
    has_next_page: boolean;
    has_previous_page: boolean;
  };
}

export const budgetApi = createApi({
  reducerPath: 'budgetApi',
  baseQuery: branchAwareBaseQuery,
  tagTypes: ['BudgetItem', 'BudgetReport', 'BudgetTemplate'],
  endpoints: (builder) => ({
    // Budget Items
    getBudgetItems: builder.query<BudgetItem[], { year?: number; month?: number; branchId?: string; organizationId?: string }>({
      query: ({ year, month, branchId, organizationId }) => {
        let url = 'budget/items';
        const params = new URLSearchParams();
        if (year) params.append('year', year.toString());
        if (month) params.append('month', month.toString());
        if (branchId) params.append('branchId', branchId);
        if (organizationId) params.append('organizationId', organizationId);
        const queryString = params.toString();
        return queryString ? `${url}?${queryString}` : url;
      },
      transformResponse: (response: BudgetItemsResponse | BudgetItem[]) => {
        // Handle both response formats for backward compatibility
        if (Array.isArray(response)) {
          return response;
        }
        return response.items || [];
      },
      providesTags: ['BudgetItem'],
    }),
    getBudgetItemById: builder.query<BudgetItem, string>({
      query: (id) => `budget/items/${id}`,
      providesTags: (result, error, id) => [{ type: 'BudgetItem', id }],
    }),
    createBudgetItem: builder.mutation<BudgetItem, Partial<BudgetItem>>({
      query: (body) => ({
        url: 'budget/items',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['BudgetItem'],
    }),
    updateBudgetItem: builder.mutation<BudgetItem, { id: string; body: Partial<BudgetItem> }>({
      query: ({ id, body }) => ({
        url: `budget/items/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'BudgetItem', id }],
    }),
    deleteBudgetItem: builder.mutation<void, string>({
      query: (id) => ({
        url: `budget/items/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BudgetItem'],
    }),

    // Budget Reports
    getBudgetReport: builder.query<BudgetReport, { year: number; month?: number; branchId?: string; organizationId?: string }>({
      query: ({ year, month, branchId, organizationId }) => {
        let url = 'budget/reports';
        const params = new URLSearchParams();
        params.append('year', year.toString());
        if (month) params.append('month', month.toString());
        if (branchId) params.append('branchId', branchId);
        if (organizationId) params.append('organizationId', organizationId);
        return `${url}?${params.toString()}`;
      },
      providesTags: ['BudgetReport'],
    }),

    // Budget Export
    exportBudget: builder.query<void, { year: number; month?: number; format?: string }>({
      query: ({ year, month, format = 'csv' }) => {
        let url = 'budget/export';
        const params = new URLSearchParams();
        params.append('year', year.toString());
        if (month) params.append('month', month.toString());
        params.append('format', format);
        return `${url}?${params.toString()}`;
      },
    }),

    // Budget Import
    importBudget: builder.mutation<
      { message: string; results: { created: number; updated: number; skipped: number; errors: any[] } },
      any[]
    >({
      query: (body) => ({
        url: 'budget/import',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['BudgetItem', 'BudgetReport'],
    }),

    // Budget Templates
    getBudgetTemplates: builder.query<BudgetTemplate[], { branchId?: string; organizationId?: string } | void>({
      query: (params = {}) => {
        let url = 'budget/templates';
        const queryParams = new URLSearchParams();
        if (params.branchId) queryParams.append('branchId', params.branchId);
        if (params.organizationId) queryParams.append('organizationId', params.organizationId);
        const queryString = queryParams.toString();
        return queryString ? `${url}?${queryString}` : url;
      },
      providesTags: ['BudgetTemplate'],
    }),

    getBudgetTemplateById: builder.query<BudgetTemplate, string>({
      query: (id) => `budget/templates/${id}`,
      providesTags: (result, error, id) => [{ type: 'BudgetTemplate', id }],
    }),

    createBudgetTemplate: builder.mutation<
      BudgetTemplate,
      {
        name: string;
        description?: string;
        isDefault?: boolean;
        items: Array<{
          accountId: string;
          amount: number;
          notes?: string;
        }>;
      }
    >({
      query: (body) => ({
        url: 'budget/templates',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['BudgetTemplate'],
    }),

    updateBudgetTemplate: builder.mutation<
      BudgetTemplate,
      {
        id: string;
        body: {
          name?: string;
          description?: string;
          isDefault?: boolean;
          items?: Array<{
            id?: string;
            accountId: string;
            amount: number;
            notes?: string;
          }>;
        };
      }
    >({
      query: ({ id, body }) => ({
        url: `budget/templates/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'BudgetTemplate', id },
        'BudgetTemplate',
      ],
    }),

    deleteBudgetTemplate: builder.mutation<void, string>({
      query: (id) => ({
        url: `budget/templates/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BudgetTemplate'],
    }),

    applyBudgetTemplate: builder.mutation<
      { message: string; results: { created: number; updated: number; skipped: number; errors: any[] } },
      { id: string; year: number; month: number }
    >({
      query: ({ id, year, month }) => ({
        url: `budget/templates/${id}/apply`,
        method: 'POST',
        body: { year, month },
      }),
      invalidatesTags: ['BudgetItem', 'BudgetReport'],
    }),
  }),
});

export const {
  useGetBudgetItemsQuery,
  useGetBudgetItemByIdQuery,
  useCreateBudgetItemMutation,
  useUpdateBudgetItemMutation,
  useDeleteBudgetItemMutation,
  useGetBudgetReportQuery,
  useExportBudgetQuery,
  useImportBudgetMutation,
  useGetBudgetTemplatesQuery,
  useGetBudgetTemplateByIdQuery,
  useCreateBudgetTemplateMutation,
  useUpdateBudgetTemplateMutation,
  useDeleteBudgetTemplateMutation,
  useApplyBudgetTemplateMutation,
} = budgetApi;
