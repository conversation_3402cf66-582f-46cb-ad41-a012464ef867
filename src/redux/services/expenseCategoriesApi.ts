import { createApi } from '@reduxjs/toolkit/query/react';
import { authenticatedBaseQuery } from '../baseQuery';

export interface ExpenseCategory {
  id: string;
  branchId: string;
  name: string;
  description?: string;
  parentId?: string;
  parentName?: string;
  accountId?: string;
  accountName?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  children?: ExpenseCategory[];
}

export interface CreateExpenseCategoryRequest {
  branchId: string;
  name: string;
  description?: string;
  parentId?: string;
  accountId?: string;
  sortOrder?: number;
}

export interface UpdateExpenseCategoryRequest {
  name?: string;
  description?: string;
  parentId?: string;
  accountId?: string;
  isActive?: boolean;
  sortOrder?: number;
}

interface ExpenseCategoriesResponse {
  data: ExpenseCategory[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

interface BranchExpenseCategoriesResponse {
  data: ExpenseCategory[];
}

export const expenseCategoriesApi = createApi({
  reducerPath: 'expenseCategoriesApi',
  baseQuery: authenticatedBaseQuery,
  tagTypes: ['ExpenseCategory'],
  endpoints: (builder) => ({
    // Get all expense categories with pagination
    getExpenseCategories: builder.query<
      ExpenseCategoriesResponse,
      {
        page?: number;
        limit?: number;
      } | void
    >({
      query: (params = {}) => {
        const { page = 1, limit = 50 } = params;
        return `expense-categories?page=${page}&limit=${limit}`;
      },
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map(({ id }) => ({ type: 'ExpenseCategory' as const, id })),
              { type: 'ExpenseCategory', id: 'LIST' },
            ]
          : [{ type: 'ExpenseCategory', id: 'LIST' }],
    }),

    // Get expense categories for a specific branch
    getExpenseCategoriesByBranch: builder.query<
      BranchExpenseCategoriesResponse,
      string
    >({
      query: (branchId) => `branches/${branchId}/expense-categories`,
      providesTags: (result, error, branchId) =>
        result?.data
          ? [
              ...result.data.map(({ id }) => ({ type: 'ExpenseCategory' as const, id })),
              { type: 'ExpenseCategory', id: `BRANCH_${branchId}` },
            ]
          : [{ type: 'ExpenseCategory', id: `BRANCH_${branchId}` }],
    }),

    // Get a specific expense category by ID
    getExpenseCategoryById: builder.query<ExpenseCategory, string>({
      query: (id) => `expense-categories/${id}`,
      providesTags: (result, error, id) => [{ type: 'ExpenseCategory', id }],
    }),

    // Create a new expense category
    createExpenseCategory: builder.mutation<ExpenseCategory, CreateExpenseCategoryRequest>({
      query: (body) => ({
        url: 'expense-categories',
        method: 'POST',
        body,
      }),
      invalidatesTags: (result, error, body) => [
        { type: 'ExpenseCategory', id: 'LIST' },
        { type: 'ExpenseCategory', id: `BRANCH_${body.branchId}` },
      ],
    }),

    // Create expense category for a specific branch
    createExpenseCategoryForBranch: builder.mutation<
      ExpenseCategory,
      { branchId: string; body: Omit<CreateExpenseCategoryRequest, 'branchId'> }
    >({
      query: ({ branchId, body }) => ({
        url: `branches/${branchId}/expense-categories`,
        method: 'POST',
        body,
      }),
      invalidatesTags: (result, error, { branchId }) => [
        { type: 'ExpenseCategory', id: 'LIST' },
        { type: 'ExpenseCategory', id: `BRANCH_${branchId}` },
      ],
    }),

    // Update an expense category
    updateExpenseCategory: builder.mutation<
      ExpenseCategory,
      { id: string; body: UpdateExpenseCategoryRequest }
    >({
      query: ({ id, body }) => ({
        url: `expense-categories/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'ExpenseCategory', id },
        { type: 'ExpenseCategory', id: 'LIST' },
        // Also invalidate branch-specific cache if we know the branch
        ...(result ? [{ type: 'ExpenseCategory' as const, id: `BRANCH_${result.branchId}` }] : []),
      ],
    }),

    // Delete an expense category
    deleteExpenseCategory: builder.mutation<void, string>({
      query: (id) => ({
        url: `expense-categories/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [
        { type: 'ExpenseCategory', id: 'LIST' },
        // Note: We can't easily invalidate branch-specific cache without knowing the branch
        // The LIST invalidation will handle most cases
      ],
    }),
  }),
});

export const {
  useGetExpenseCategoriesQuery,
  useGetExpenseCategoriesByBranchQuery,
  useGetExpenseCategoryByIdQuery,
  useCreateExpenseCategoryMutation,
  useCreateExpenseCategoryForBranchMutation,
  useUpdateExpenseCategoryMutation,
  useDeleteExpenseCategoryMutation,
} = expenseCategoriesApi;
