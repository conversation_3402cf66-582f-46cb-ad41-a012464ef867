import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { inventoryApi } from './services/inventoryApi';
import { budgetApi } from './services/budgetApi';
import { cashflowApi } from './services/cashflowApi';
import { taxApi } from './services/taxApi';
import { payrollApi } from './services/payrollApi';
import { payrollReportsApi } from './services/payrollReportsApi';
import { ordersApi } from './services/ordersApi';
import { journalEntriesApi } from './services/journalEntriesApi';
import { billsApi } from './services/billsApi';
import { assetsApi } from './services/assetsApi';
import { taxRatesApi } from './services/taxRatesApi';
import { vendorsApi } from './services/vendorsApi';
import { chartOfAccountsApi } from './services/chartOfAccountsApi';

import { employeesApi } from './services/employeesApi';
import { bankingApi } from './services/bankingApi';
import { reportsApi } from './services/reportsApi';
import { settingsApi } from './services/settingsApi';
import { usersApi } from './services/usersApi';
import { customersApi } from './services/customersApi';
import { expensesApi } from './services/expensesApi';
import { expenseCategoriesApi } from './services/expenseCategoriesApi';
import { dashboardApi } from './services/dashboardApi';
import { profileApi } from './services/profileApi';
import { integrationsApi } from './services/integrationsApi';
import { invoicesApi } from './services/invoicesApi';
import { emailApi } from './services/emailApi';
import { collectionsApi } from './services/collectionsApi';
import { customReportsApi } from './services/customReportsApi';
import { merchantsApi } from './services/merchantsApi';
import { organizationsApi } from './services/organizationsApi';
import { branchesApi } from './services/branchesApi';
import { subscriptionsApi } from './services/subscriptionsApi';
import { auditLogsApi } from './services/auditLogsApi';

export const store = configureStore({
  reducer: {
    // Original API slices
    [inventoryApi.reducerPath]: inventoryApi.reducer,
    [budgetApi.reducerPath]: budgetApi.reducer,
    [cashflowApi.reducerPath]: cashflowApi.reducer,
    [taxApi.reducerPath]: taxApi.reducer,
    [payrollApi.reducerPath]: payrollApi.reducer,
    [payrollReportsApi.reducerPath]: payrollReportsApi.reducer,

    // New API slices
    [ordersApi.reducerPath]: ordersApi.reducer,
    [journalEntriesApi.reducerPath]: journalEntriesApi.reducer,
    [billsApi.reducerPath]: billsApi.reducer,
    [assetsApi.reducerPath]: assetsApi.reducer,
    [taxRatesApi.reducerPath]: taxRatesApi.reducer,
    [vendorsApi.reducerPath]: vendorsApi.reducer,
    [chartOfAccountsApi.reducerPath]: chartOfAccountsApi.reducer,
    [employeesApi.reducerPath]: employeesApi.reducer,
    [bankingApi.reducerPath]: bankingApi.reducer,
    [reportsApi.reducerPath]: reportsApi.reducer,
    [settingsApi.reducerPath]: settingsApi.reducer,
    [usersApi.reducerPath]: usersApi.reducer,
    [customersApi.reducerPath]: customersApi.reducer,
    [expensesApi.reducerPath]: expensesApi.reducer,
    [expenseCategoriesApi.reducerPath]: expenseCategoriesApi.reducer,
    [dashboardApi.reducerPath]: dashboardApi.reducer,

    [profileApi.reducerPath]: profileApi.reducer,
    [integrationsApi.reducerPath]: integrationsApi.reducer,
    [invoicesApi.reducerPath]: invoicesApi.reducer,
    [emailApi.reducerPath]: emailApi.reducer,
    [collectionsApi.reducerPath]: collectionsApi.reducer,
    [customReportsApi.reducerPath]: customReportsApi.reducer,
    [merchantsApi.reducerPath]: merchantsApi.reducer,
    [organizationsApi.reducerPath]: organizationsApi.reducer,
    [branchesApi.reducerPath]: branchesApi.reducer,
    [subscriptionsApi.reducerPath]: subscriptionsApi.reducer,
    [auditLogsApi.reducerPath]: auditLogsApi.reducer,
  },
  // Adding the api middleware enables caching, invalidation, polling,
  // and other useful features of RTK Query.
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      // Original API middlewares
      inventoryApi.middleware,
      budgetApi.middleware,
      cashflowApi.middleware,
      taxApi.middleware,
      payrollApi.middleware,
      payrollReportsApi.middleware,

      // New API middlewares
      ordersApi.middleware,
      journalEntriesApi.middleware,
      billsApi.middleware,
      assetsApi.middleware,
      taxRatesApi.middleware,
      vendorsApi.middleware,
      chartOfAccountsApi.middleware,
      employeesApi.middleware,
      bankingApi.middleware,
      reportsApi.middleware,
      settingsApi.middleware,
      usersApi.middleware,
      customersApi.middleware,
      expensesApi.middleware,
      expenseCategoriesApi.middleware,
      dashboardApi.middleware,

      profileApi.middleware,
      integrationsApi.middleware,
      invoicesApi.middleware,
      emailApi.middleware,
      collectionsApi.middleware,
      customReportsApi.middleware,
      merchantsApi.middleware,
      organizationsApi.middleware,
      branchesApi.middleware,
      subscriptionsApi.middleware,
      auditLogsApi.middleware
    ),
});

// optional, but required for refetchOnFocus/refetchOnReconnect behaviors
// see `setupListeners` docs - takes an optional callback as the 2nd arg for customization
setupListeners(store.dispatch);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
