import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';

/**
 * Standard base query for Next.js API routes (auth, webhooks, etc.)
 * Authentication is handled by the Next.js API routes using getServerSession
 */
export const baseQuery = fetchBaseQuery({
  baseUrl: '/api/',
  prepareHeaders: (headers) => {
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

/**
 * Base query for Golang backend through the new generic services proxy
 * This routes requests through /api/[...services] to the Golang backend
 */
export const goBackendBaseQuery = fetchBaseQuery({
  baseUrl: '/api/',
  prepareHeaders: (headers, { getState }) => {
    headers.set('Content-Type', 'application/json');

    // Try to get branch context from localStorage (client-side)
    if (typeof window !== 'undefined') {
      const branchId = localStorage.getItem('selectedBranchId');
      const organizationId = localStorage.getItem('selectedOrganizationId');

      if (branchId) {
        headers.set('x-branch-id', branchId);
      }
      if (organizationId) {
        headers.set('x-organization-id', organizationId);
      }
    }

    return headers;
  },
});

/**
 * Branch-aware base query that automatically includes branch context
 * This is the recommended base query for all APIs that go to Golang backend
 */
export const branchAwareBaseQuery = goBackendBaseQuery;

/**
 * Authenticated base query for backend APIs
 * Routes through the generic [...services] proxy to the Golang backend
 */
export const authenticatedBaseQuery = goBackendBaseQuery;

/**
 * Base query for APIs that don't require authentication
 */
export const unauthenticatedBaseQuery = baseQuery;
