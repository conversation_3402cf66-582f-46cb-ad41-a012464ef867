'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, XCircle, RefreshCw, Shield, User, Building2 } from 'lucide-react';
import { useOrganizationPermissions } from '@/hooks/useOrganizationPermissions';
import { useBranchContext } from '@/contexts/BranchContext';
import { useAuth } from '@/hooks/useAuth';

/**
 * Debug component to help troubleshoot permission issues
 */
export function PermissionsDebug() {
  const { session, isAuthenticated, status } = useAuth();
  const { organizations, selectedOrganizationId, selectedBranchId } = useBranchContext();
  const {
    currentMerchantId,
    permissionLevel,
    isLoading: permissionsLoading,
    error: permissionsError,
    canCreateAccount,
    canEditAccount,
    canDeleteAccount,
    refreshPermissions,
  } = useOrganizationPermissions();

  const handleRefresh = () => {
    refreshPermissions();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permissions Debug Information
          </CardTitle>
          <CardDescription>
            This component helps debug why you can't create accounts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Authentication Status */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Authentication Status
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                {isAuthenticated ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  Status: {status}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {session?.user ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  User: {session?.user?.email || 'Not logged in'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {session?.user?.id ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  User ID: {session?.user?.id || 'Missing'}
                </span>
              </div>
            </div>
          </div>

          {/* Current Context */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Current Context
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                {organizations.length > 0 ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  Organizations: {organizations.length}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {selectedOrganizationId ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  Selected Org: {selectedOrganizationId ? 'Yes' : 'None'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {selectedBranchId ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  Selected Branch: {selectedBranchId ? 'Yes' : 'None'}
                </span>
              </div>
            </div>
          </div>

          {/* Permission System Status */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Permission System Status
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="ml-auto"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                {permissionsLoading ? (
                  <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
                ) : permissionsError ? (
                  <XCircle className="h-4 w-4 text-red-500" />
                ) : (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
                <span className="text-sm">
                  Loading: {permissionsLoading ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {permissionsError ? (
                  <XCircle className="h-4 w-4 text-red-500" />
                ) : (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
                <span className="text-sm">
                  Error: {permissionsError ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {currentMerchantId ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  Merchant ID: {currentMerchantId ? 'Set' : 'Missing'}
                </span>
              </div>
            </div>
          </div>

          {/* Permission Details */}
          <div className="space-y-3">
            <h4 className="font-medium">Permission Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Current Merchant ID:</span>
                  <Badge variant={currentMerchantId ? "default" : "destructive"}>
                    {currentMerchantId || 'None'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Permission Level:</span>
                  <Badge variant={permissionLevel ? "default" : "destructive"}>
                    {permissionLevel || 'None'}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Can Create Accounts:</span>
                  <Badge variant={canCreateAccount() ? "default" : "destructive"}>
                    {canCreateAccount() ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Can Edit Accounts:</span>
                  <Badge variant={canEditAccount() ? "default" : "destructive"}>
                    {canEditAccount() ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Error Details */}
          {permissionsError && (
            <div className="space-y-3">
              <h4 className="font-medium text-red-600">Permission Error Details</h4>
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <pre className="text-sm text-red-800 whitespace-pre-wrap">
                  {permissionsError}
                </pre>
              </div>
            </div>
          )}

          {/* System Architecture Issue */}
          <div className="space-y-3">
            <h4 className="font-medium text-orange-600">⚠️ System Architecture Issue Detected</h4>
            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="space-y-2 text-sm text-orange-800">
                <p className="font-medium">The Chart of Accounts page is using an outdated permission system:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>Current System:</strong> Uses Organizations and Branches</li>
                  <li><strong>Chart of Accounts:</strong> Still uses old Merchant-based permissions</li>
                  <li><strong>Problem:</strong> No merchant ID is set, so permissions fail</li>
                  <li><strong>Solution:</strong> Update Chart of Accounts to use Organization/Branch permissions</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Troubleshooting Steps */}
          <div className="space-y-3">
            <h4 className="font-medium">Troubleshooting Steps</h4>
            <div className="space-y-2 text-sm">
              {!isAuthenticated && (
                <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-yellow-800">Not Authenticated</div>
                    <div className="text-yellow-700">Please log in to access account creation</div>
                  </div>
                </div>
              )}
              
              {isAuthenticated && !currentMerchantId && (
                <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded">
                  <XCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-red-800">No Merchant ID</div>
                    <div className="text-red-700">
                      The Chart of Accounts page requires a merchant ID, but the system now uses organizations.
                      This is a system architecture mismatch that needs to be fixed.
                    </div>
                  </div>
                </div>
              )}

              {isAuthenticated && organizations.length === 0 && (
                <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded">
                  <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-blue-800">No Organizations</div>
                    <div className="text-blue-700">
                      You need to have access to at least one organization to create accounts.
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-3">
            <h4 className="font-medium">Quick Actions</h4>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Permissions
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => console.log('Debug Info:', { 
                  session, 
                  organizations, 
                  currentMerchantId, 
                  permissionLevel,
                  canCreateAccount: canCreateAccount(),
                  canEditAccount: canEditAccount(),
                  canDeleteAccount: canDeleteAccount()
                })}
              >
                Log Debug Info
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
