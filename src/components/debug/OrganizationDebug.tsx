'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, XCircle, RefreshCw, Database, User, Building2 } from 'lucide-react';
import { useGetOrganizationsQuery } from '@/redux/services/organizationsApi';
import { useAuth } from '@/hooks/useAuth';
import { useBranchContext } from '@/contexts/BranchContext';

/**
 * Debug component to help troubleshoot organization loading issues
 */
export function OrganizationDebug() {
  const { session, isAuthenticated, status } = useAuth();
  const { data: organizationsData, isLoading, error, refetch } = useGetOrganizationsQuery();
  const { organizations } = useBranchContext();

  const handleRefresh = () => {
    refetch();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Organization Debug Information
          </CardTitle>
          <CardDescription>
            This component helps debug why organizations might not be showing up
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Authentication Status */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Authentication Status
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                {isAuthenticated ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  Status: {status}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {session?.user ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  User: {session?.user?.email || 'Not logged in'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {session?.user?.id ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  User ID: {session?.user?.id || 'Missing'}
                </span>
              </div>
            </div>
          </div>

          {/* API Query Status */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              API Query Status
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="ml-auto"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
                ) : error ? (
                  <XCircle className="h-4 w-4 text-red-500" />
                ) : (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
                <span className="text-sm">
                  Loading: {isLoading ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {error ? (
                  <XCircle className="h-4 w-4 text-red-500" />
                ) : (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
                <span className="text-sm">
                  Error: {error ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {organizationsData ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  Data: {organizationsData ? 'Received' : 'None'}
                </span>
              </div>
            </div>
          </div>

          {/* Error Details */}
          {error && (
            <div className="space-y-3">
              <h4 className="font-medium text-red-600">Error Details</h4>
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <pre className="text-sm text-red-800 whitespace-pre-wrap">
                  {JSON.stringify(error, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Raw API Response */}
          {organizationsData && (
            <div className="space-y-3">
              <h4 className="font-medium">Raw API Response</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Organizations Count:</span>
                  <Badge variant="default">
                    {organizationsData.data?.length || 0}
                  </Badge>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <pre className="text-sm whitespace-pre-wrap overflow-auto max-h-40">
                    {JSON.stringify(organizationsData, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          )}

          {/* Organizations from Context */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Organizations from Context
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Count:</span>
                  <Badge variant={organizations.length > 0 ? "default" : "secondary"}>
                    {organizations.length}
                  </Badge>
                </div>
                {organizations.length > 0 && (
                  <div className="space-y-1">
                    {organizations.map((org, index) => (
                      <div key={org.id} className="text-sm p-2 bg-muted/50 rounded">
                        <div className="font-medium">{org.name}</div>
                        <div className="text-muted-foreground text-xs">
                          ID: {org.id} | Branches: {org.branches?.length || 0}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Troubleshooting Steps */}
          <div className="space-y-3">
            <h4 className="font-medium">Troubleshooting Steps</h4>
            <div className="space-y-2 text-sm">
              {!isAuthenticated && (
                <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-yellow-800">Not Authenticated</div>
                    <div className="text-yellow-700">Please log in to see organizations</div>
                  </div>
                </div>
              )}

              {isAuthenticated && organizations.length === 0 && !isLoading && (
                <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded">
                  <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-blue-800">No Organizations Found</div>
                    <div className="text-blue-700">
                      This could mean:
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>You don't have access to any organizations</li>
                        <li>No organizations have been created yet</li>
                        <li>There's an issue with the backend API</li>
                        <li>Your user permissions need to be set up</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {error && (
                <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded">
                  <XCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-red-800">API Error</div>
                    <div className="text-red-700">
                      Check:
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>Backend server is running (port 8050)</li>
                        <li>API endpoints are working</li>
                        <li>Authentication tokens are valid</li>
                        <li>Database connection is working</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-3">
            <h4 className="font-medium">Quick Actions</h4>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Data
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('/api/organizations', '_blank')}
              >
                <Database className="h-4 w-4 mr-2" />
                Test API Directly
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => console.log('Organizations Data:', { organizationsData, organizations, session })}
              >
                Log Debug Info
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
