'use client';

import React, { useState } from 'react';
import { Building2, MapPin, ChevronDown, Star, Settings2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useBranchContext } from '@/contexts/BranchContext';
import { BranchSelector } from '@/components/common/BranchSelector';

/**
 * Enhanced header component for organization and branch selection
 * Shows current context prominently and provides quick access to defaults
 */
export function HeaderBranchSelector() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const {
    selectedOrganizationId,
    selectedBranchId,
    organizations,
    availableBranches,
    isLoading,
    setSelectedOrganization,
    setSelectedBranch,
    setAsDefaultOrganization,
    setAsDefaultBranch,
    getSelectedOrganization,
    getSelectedBranch,
    defaultOrganizationId,
    defaultBranchId,
    isUsingDefaults,
  } = useBranchContext();

  const selectedOrganization = getSelectedOrganization();
  const selectedBranch = getSelectedBranch();

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 py-1.5 bg-muted/50 rounded-md animate-pulse">
        <div className="h-4 w-4 bg-muted rounded" />
        <div className="h-4 w-24 bg-muted rounded" />
      </div>
    );
  }

  if (!selectedOrganization && !selectedBranch) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsDialogOpen(true)}
        className="text-muted-foreground"
      >
        <Settings2 className="h-4 w-4 mr-2" />
        Select Context
      </Button>
    );
  }

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 max-w-[280px] justify-between"
                >
                  <div className="flex items-center gap-2 min-w-0">
                    {/* Organization */}
                    {selectedOrganization && (
                      <div className="flex items-center gap-1 min-w-0">
                        <Building2 className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
                        <span className="truncate text-sm font-medium">
                          {selectedOrganization.name}
                        </span>
                        {defaultOrganizationId === selectedOrganization.id && (
                          <Star className="h-3 w-3 fill-current text-yellow-500 flex-shrink-0" />
                        )}
                      </div>
                    )}
                    
                    {/* Separator */}
                    {selectedOrganization && selectedBranch && (
                      <span className="text-muted-foreground">•</span>
                    )}
                    
                    {/* Branch */}
                    {selectedBranch && (
                      <div className="flex items-center gap-1 min-w-0">
                        <MapPin className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
                        <span className="truncate text-sm">
                          {selectedBranch.name}
                        </span>
                        {defaultBranchId === selectedBranch.id && (
                          <Star className="h-3 w-3 fill-current text-yellow-500 flex-shrink-0" />
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-1 flex-shrink-0">
                    {isUsingDefaults && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                        Default
                      </Badge>
                    )}
                    <ChevronDown className="h-3.5 w-3.5 opacity-50" />
                  </div>
                </Button>
              </DropdownMenuTrigger>
              
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Organization & Branch Context
                  {isUsingDefaults && (
                    <Badge variant="secondary" className="text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      Using Defaults
                    </Badge>
                  )}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                {/* Quick Organization Switch */}
                <div className="px-2 py-1">
                  <div className="text-xs font-medium text-muted-foreground mb-2">Organizations:</div>
                  {organizations.slice(0, 3).map((org) => (
                    <DropdownMenuItem
                      key={org.id}
                      onClick={() => setSelectedOrganization(org.id)}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        <span className="truncate">{org.name}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {defaultOrganizationId === org.id && (
                          <Star className="h-3 w-3 fill-current text-yellow-500" />
                        )}
                        {selectedOrganizationId === org.id && (
                          <Badge variant="outline" className="text-xs">Current</Badge>
                        )}
                      </div>
                    </DropdownMenuItem>
                  ))}
                  {organizations.length > 3 && (
                    <DropdownMenuItem
                      onClick={() => setIsDialogOpen(true)}
                      className="text-muted-foreground text-xs"
                    >
                      View all {organizations.length} organizations...
                    </DropdownMenuItem>
                  )}
                </div>
                
                <DropdownMenuSeparator />
                
                {/* Quick Branch Switch */}
                {availableBranches.length > 0 && (
                  <div className="px-2 py-1">
                    <div className="text-xs font-medium text-muted-foreground mb-2">Branches:</div>
                    {availableBranches.slice(0, 3).map((branch) => (
                      <DropdownMenuItem
                        key={branch.id}
                        onClick={() => setSelectedBranch(branch.id)}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <span className="truncate">{branch.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {defaultBranchId === branch.id && (
                            <Star className="h-3 w-3 fill-current text-yellow-500" />
                          )}
                          {selectedBranchId === branch.id && (
                            <Badge variant="outline" className="text-xs">Current</Badge>
                          )}
                        </div>
                      </DropdownMenuItem>
                    ))}
                    {availableBranches.length > 3 && (
                      <DropdownMenuItem
                        onClick={() => setIsDialogOpen(true)}
                        className="text-muted-foreground text-xs"
                      >
                        View all {availableBranches.length} branches...
                      </DropdownMenuItem>
                    )}
                  </div>
                )}
                
                <DropdownMenuSeparator />
                
                {/* Quick Actions */}
                <div className="px-2 py-1">
                  <div className="text-xs font-medium text-muted-foreground mb-2">Quick Actions:</div>
                  {selectedOrganizationId && (
                    <DropdownMenuItem
                      onClick={() => setAsDefaultOrganization(selectedOrganizationId)}
                      className="text-xs"
                    >
                      <Star className="h-3 w-3 mr-2" />
                      Set Organization as Default
                    </DropdownMenuItem>
                  )}
                  {selectedBranchId && (
                    <DropdownMenuItem
                      onClick={() => setAsDefaultBranch(selectedBranchId)}
                      className="text-xs"
                    >
                      <Star className="h-3 w-3 mr-2" />
                      Set Branch as Default
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={() => setIsDialogOpen(true)}
                    className="text-xs"
                  >
                    <Settings2 className="h-3 w-3 mr-2" />
                    Manage Context & Defaults
                  </DropdownMenuItem>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-sm">
              <div className="font-medium">Current Context:</div>
              {selectedOrganization && (
                <div className="flex items-center gap-1 mt-1">
                  <Building2 className="h-3 w-3" />
                  {selectedOrganization.name}
                  {defaultOrganizationId === selectedOrganization.id && (
                    <Star className="h-3 w-3 fill-current text-yellow-500" />
                  )}
                </div>
              )}
              {selectedBranch && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {selectedBranch.name}
                  {defaultBranchId === selectedBranch.id && (
                    <Star className="h-3 w-3 fill-current text-yellow-500" />
                  )}
                </div>
              )}
              {isUsingDefaults && (
                <div className="text-xs text-muted-foreground mt-1">
                  Using your default selections
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Full Context Management Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings2 className="h-5 w-5" />
              Manage Organization & Branch Context
            </DialogTitle>
            <DialogDescription>
              Select your organization and branch context. You can also set defaults that will be automatically selected when you log in.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <BranchSelector showCard={false} />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
