'use client';

import React from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Building2, MapPin, ChevronRight, Star, Home } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useBranchContext } from '@/contexts/BranchContext';

interface ContextBreadcrumbProps {
  currentPage?: string;
  customItems?: Array<{
    label: string;
    href?: string;
    icon?: React.ReactNode;
  }>;
  showDefaults?: boolean;
  className?: string;
}

/**
 * Breadcrumb component that shows the current organization/branch context
 * and navigation path with default indicators
 */
export function ContextBreadcrumb({ 
  currentPage, 
  customItems = [], 
  showDefaults = true,
  className = '' 
}: ContextBreadcrumbProps) {
  const params = useParams();
  const locale = params?.locale as string || 'en';
  
  const {
    getSelectedOrganization,
    getSelectedBranch,
    defaultOrganizationId,
    defaultBranchId,
    isUsingDefaults,
  } = useBranchContext();

  const selectedOrganization = getSelectedOrganization();
  const selectedBranch = getSelectedBranch();

  const breadcrumbItems = [
    {
      label: 'Home',
      href: `/${locale}/dashboard`,
      icon: <Home className="h-4 w-4" />,
    },
  ];

  // Add organization to breadcrumb if available
  if (selectedOrganization) {
    breadcrumbItems.push({
      label: selectedOrganization.name,
      href: `/${locale}/organizations/${selectedOrganization.slug || selectedOrganization.id}`,
      icon: <Building2 className="h-4 w-4" />,
      isDefault: defaultOrganizationId === selectedOrganization.id,
    });
  }

  // Add branch to breadcrumb if available
  if (selectedBranch && selectedOrganization) {
    breadcrumbItems.push({
      label: selectedBranch.name,
      href: `/${locale}/organizations/${selectedOrganization.slug || selectedOrganization.id}/branches`,
      icon: <MapPin className="h-4 w-4" />,
      isDefault: defaultBranchId === selectedBranch.id,
    });
  }

  // Add custom items
  customItems.forEach(item => {
    breadcrumbItems.push({
      label: item.label,
      href: item.href,
      icon: item.icon,
    });
  });

  // Add current page if specified
  if (currentPage) {
    breadcrumbItems.push({
      label: currentPage,
      icon: null,
    });
  }

  return (
    <div className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}>
      {/* Default Status Indicator */}
      {showDefaults && isUsingDefaults && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="secondary" className="text-xs mr-2">
                <Star className="h-3 w-3 mr-1" />
                Using Defaults
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>You're currently using your default organization and branch selections</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Breadcrumb Items */}
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
          )}
          
          <div className="flex items-center gap-1">
            {item.href ? (
              <Link
                href={item.href}
                className="flex items-center gap-1.5 hover:text-foreground transition-colors"
              >
                {item.icon}
                <span>{item.label}</span>
                {showDefaults && (item as any).isDefault && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Star className="h-3 w-3 fill-current text-yellow-500" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>This is your default {item.icon?.type === Building2 ? 'organization' : 'branch'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </Link>
            ) : (
              <div className="flex items-center gap-1.5 text-foreground font-medium">
                {item.icon}
                <span>{item.label}</span>
                {showDefaults && (item as any).isDefault && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Star className="h-3 w-3 fill-current text-yellow-500" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>This is your default {item.icon?.type === Building2 ? 'organization' : 'branch'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            )}
          </div>
        </React.Fragment>
      ))}
    </div>
  );
}

/**
 * Compact version for mobile or tight spaces
 */
export function ContextBreadcrumbCompact({ 
  currentPage, 
  showDefaults = true,
  className = '' 
}: Pick<ContextBreadcrumbProps, 'currentPage' | 'showDefaults' | 'className'>) {
  const {
    getSelectedOrganization,
    getSelectedBranch,
    defaultOrganizationId,
    defaultBranchId,
    isUsingDefaults,
  } = useBranchContext();

  const selectedOrganization = getSelectedOrganization();
  const selectedBranch = getSelectedBranch();

  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      {/* Context Summary */}
      <div className="flex items-center gap-2 text-muted-foreground">
        {selectedOrganization && (
          <div className="flex items-center gap-1">
            <Building2 className="h-3.5 w-3.5" />
            <span className="truncate max-w-[100px]">{selectedOrganization.name}</span>
            {showDefaults && defaultOrganizationId === selectedOrganization.id && (
              <Star className="h-3 w-3 fill-current text-yellow-500" />
            )}
          </div>
        )}
        
        {selectedOrganization && selectedBranch && (
          <span className="text-muted-foreground/50">•</span>
        )}
        
        {selectedBranch && (
          <div className="flex items-center gap-1">
            <MapPin className="h-3.5 w-3.5" />
            <span className="truncate max-w-[100px]">{selectedBranch.name}</span>
            {showDefaults && defaultBranchId === selectedBranch.id && (
              <Star className="h-3 w-3 fill-current text-yellow-500" />
            )}
          </div>
        )}
      </div>

      {/* Current Page */}
      {currentPage && (
        <>
          <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
          <span className="font-medium text-foreground">{currentPage}</span>
        </>
      )}

      {/* Default Indicator */}
      {showDefaults && isUsingDefaults && (
        <Badge variant="secondary" className="text-xs ml-auto">
          <Star className="h-3 w-3 mr-1" />
          Default
        </Badge>
      )}
    </div>
  );
}
