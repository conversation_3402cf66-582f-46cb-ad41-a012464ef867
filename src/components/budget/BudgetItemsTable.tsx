'use client';

import React, { useMemo } from 'react';
import { BudgetItem } from '@/lib/types';
import { PaginatedTable, TableColumn } from '@/components/common/PaginatedTable';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useI18n } from '@/hooks/useI18n';

interface BudgetItemsTableProps {
  data: BudgetItem[];
  accounts: any[];
  isLoading?: boolean;
  error?: string | null;
  
  // Pagination
  currentPage: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  
  // Search and sorting
  searchValue: string;
  onSearchChange: (value: string) => void;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSortChange?: (field: string, order: 'asc' | 'desc') => void;
  
  // Actions
  onEdit?: (item: BudgetItem) => void;
  onDelete?: (item: BudgetItem) => void;
  onView?: (item: BudgetItem) => void;
}

export function BudgetItemsTable({
  data,
  accounts,
  isLoading = false,
  error = null,
  currentPage,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  searchValue,
  onSearchChange,
  sortBy,
  sortOrder,
  onSortChange,
  onEdit,
  onDelete,
  onView,
}: BudgetItemsTableProps) {
  const { formatCurrency, formatDate } = useI18n();

  // Create a map for quick account lookup
  const accountsMap = useMemo(() => {
    return accounts.reduce((acc, account) => {
      acc[account.id] = account;
      return acc;
    }, {} as Record<string, any>);
  }, [accounts]);

  // Define table columns
  const columns: TableColumn<BudgetItem>[] = useMemo(() => [
    {
      key: 'account',
      label: 'Account',
      sortable: true,
      render: (item) => {
        const account = accountsMap[item.account_id || item.accountId || ''];
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {account?.name || account?.account_name || 'Unknown Account'}
            </span>
            {account?.code && (
              <span className="text-sm text-muted-foreground">
                {account.code}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'period',
      label: 'Period',
      sortable: true,
      render: (item) => (
        <div className="flex flex-col">
          <span className="font-medium">
            {new Date(item.year, item.month - 1).toLocaleDateString('default', { 
              month: 'long', 
              year: 'numeric' 
            })}
          </span>
          <span className="text-sm text-muted-foreground">
            {item.year}-{item.month.toString().padStart(2, '0')}
          </span>
        </div>
      ),
    },
    {
      key: 'amount',
      label: 'Budgeted Amount',
      sortable: true,
      render: (item) => (
        <span className="font-mono">
          {formatCurrency(item.amount)}
        </span>
      ),
    },
    {
      key: 'notes',
      label: 'Notes',
      render: (item) => (
        <span className="text-sm text-muted-foreground max-w-xs truncate">
          {item.notes || '-'}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (item) => (
        <span className="text-sm text-muted-foreground">
          {formatDate(item.created_at || item.createdAt || '')}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (item) => (
        <div className="flex space-x-2">
          {onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(item)}
            >
              View
            </Button>
          )}
          {onEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(item)}
            >
              Edit
            </Button>
          )}
          {onDelete && (
            <Button
              variant="outline"
              size="sm"
              className="text-red-500 hover:text-red-700"
              onClick={() => onDelete(item)}
            >
              Delete
            </Button>
          )}
        </div>
      ),
    },
  ], [accountsMap, formatCurrency, formatDate, onView, onEdit, onDelete]);

  // Calculate pagination info
  const paginationInfo = useMemo(() => {
    const totalPages = Math.ceil(totalItems / pageSize);
    return {
      total: totalItems,
      page: currentPage,
      limit: pageSize,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
    };
  }, [totalItems, pageSize, currentPage]);

  return (
    <PaginatedTable
      data={data}
      columns={columns}
      paginationInfo={paginationInfo}
      isLoading={isLoading}
      error={error}
      
      // Search functionality
      searchable
      searchPlaceholder="Search budget items..."
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      
      // Sorting
      sortBy={sortBy}
      sortOrder={sortOrder}
      onSortChange={onSortChange}
      
      // Pagination
      currentPage={currentPage}
      pageSize={pageSize}
      onPageChange={onPageChange}
      onPageSizeChange={onPageSizeChange}
      
      // Display options
      title={`Budget Items (${totalItems})`}
      emptyMessage="No budget items found for this period"
      
      // Row interactions
      onRowClick={onView}
      getRowId={(item) => item.id}
    />
  );
}
