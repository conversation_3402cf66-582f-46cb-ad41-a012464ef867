'use client';

import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Loader2, AlertTriangle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface DeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  title: string;
  description: string;
  confirmationText?: string;
  itemDetails?: React.ReactNode;
  canDelete?: boolean;
  warningMessage?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  loadingText?: string;
  successMessage?: string;
  errorMessage?: string;
  onDeleteSuccess?: () => void;
}

export function DeleteDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmationText,
  itemDetails,
  canDelete = true,
  warningMessage,
  confirmButtonText = 'Delete',
  cancelButtonText = 'Cancel',
  loadingText = 'Deleting...',
  successMessage = 'Item deleted successfully',
  errorMessage = 'Failed to delete item',
  onDeleteSuccess,
}: DeleteDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const handleDelete = async () => {
    if (!canDelete) return;

    try {
      setIsLoading(true);
      setError('');
      
      await onConfirm();
      
      toast({
        title: 'Success',
        description: successMessage,
        variant: 'default',
      });
      
      onClose();
      onDeleteSuccess?.();
    } catch (error: any) {
      console.error('Failed to delete:', error);
      
      let displayErrorMessage = errorMessage;
      
      // Handle specific error messages from API
      if (error?.data?.error) {
        displayErrorMessage = error.data.error;
      } else if (error?.message) {
        displayErrorMessage = error.message;
      }
      
      setError(displayErrorMessage);
      
      toast({
        title: 'Error',
        description: displayErrorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <span>{title}</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>{description}</p>
            
            {confirmationText && (
              <p className="font-medium">{confirmationText}</p>
            )}
            
            {itemDetails && (
              <div className="p-3 bg-muted rounded-lg">
                {itemDetails}
              </div>
            )}
            
            {warningMessage && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <AlertTriangle className="h-4 w-4 inline mr-1" />
                  {warningMessage}
                </p>
              </div>
            )}
            
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {cancelButtonText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isLoading || !canDelete}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {loadingText}
              </>
            ) : (
              confirmButtonText
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
