'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit, Plus, FolderTree } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import {
  useGetExpenseCategoriesByBranchQuery,
  useCreateExpenseCategoryForBranchMutation,
  useUpdateExpenseCategoryMutation,
  useDeleteExpenseCategoryMutation,
  type ExpenseCategory,
} from '@/redux/services/expenseCategoriesApi';
import { useGetChartOfAccountsQuery } from '@/redux/services/chartOfAccountsApi';

const categorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  parentId: z.string().optional(),
  accountId: z.string().optional(),
  sortOrder: z.number().min(0, 'Sort order must be non-negative').default(0),
});

type CategoryFormData = z.infer<typeof categorySchema>;

interface ExpenseCategoriesManagerProps {
  branchId: string;
}

export function ExpenseCategoriesManager({ branchId }: ExpenseCategoriesManagerProps) {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ExpenseCategory | null>(null);

  // API queries
  const { data: categoriesResponse, isLoading } = useGetExpenseCategoriesByBranchQuery(branchId);
  const { data: accountsResponse } = useGetChartOfAccountsQuery();

  // Mutations
  const [createCategory, { isLoading: isCreating }] = useCreateExpenseCategoryForBranchMutation();
  const [updateCategory, { isLoading: isUpdating }] = useUpdateExpenseCategoryMutation();
  const [deleteCategory, { isLoading: isDeleting }] = useDeleteExpenseCategoryMutation();

  const categories = categoriesResponse?.data || [];
  const accounts = accountsResponse?.data || [];

  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      description: '',
      sortOrder: 0,
    },
  });

  const handleCreateCategory = () => {
    setEditingCategory(null);
    form.reset({
      name: '',
      description: '',
      sortOrder: 0,
    });
    setIsDialogOpen(true);
  };

  const handleEditCategory = (category: ExpenseCategory) => {
    setEditingCategory(category);
    form.reset({
      name: category.name,
      description: category.description || '',
      parentId: category.parentId || undefined,
      accountId: category.accountId || undefined,
      sortOrder: category.sortOrder,
    });
    setIsDialogOpen(true);
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!confirm('Are you sure you want to delete this category?')) {
      return;
    }

    try {
      await deleteCategory(categoryId).unwrap();
      toast({
        title: 'Success',
        description: 'Category deleted successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.data?.error || 'Failed to delete category',
        variant: 'destructive',
      });
    }
  };

  const onSubmit = async (data: CategoryFormData) => {
    try {
      if (editingCategory) {
        await updateCategory({
          id: editingCategory.id,
          body: data,
        }).unwrap();
        toast({
          title: 'Success',
          description: 'Category updated successfully',
        });
      } else {
        await createCategory({
          branchId,
          body: data,
        }).unwrap();
        toast({
          title: 'Success',
          description: 'Category created successfully',
        });
      }

      setIsDialogOpen(false);
      form.reset();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.data?.error || 'Failed to save category',
        variant: 'destructive',
      });
    }
  };

  const renderCategoryTree = (categories: ExpenseCategory[], level = 0) => {
    const rootCategories = categories.filter(cat => !cat.parentId);
    const getChildren = (parentId: string) => categories.filter(cat => cat.parentId === parentId);

    const renderCategory = (category: ExpenseCategory, level: number) => (
      <div key={category.id} className={`ml-${level * 4}`}>
        <div className="flex items-center justify-between p-3 border rounded-lg mb-2 bg-white">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {level > 0 && <div className="w-4 h-px bg-gray-300" />}
              <FolderTree className="h-4 w-4 text-gray-500" />
              <div>
                <h4 className="font-medium">{category.name}</h4>
                {category.description && (
                  <p className="text-sm text-gray-600">{category.description}</p>
                )}
                <div className="flex items-center space-x-2 mt-1">
                  {category.parentName && (
                    <Badge variant="outline" className="text-xs">
                      Parent: {category.parentName}
                    </Badge>
                  )}
                  {category.accountName && (
                    <Badge variant="outline" className="text-xs">
                      Account: {category.accountName}
                    </Badge>
                  )}
                  <Badge variant={category.isActive ? 'default' : 'secondary'} className="text-xs">
                    {category.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEditCategory(category)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleDeleteCategory(category.id)}
              disabled={isDeleting}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Render children */}
        {getChildren(category.id).map(child => renderCategory(child, level + 1))}
      </div>
    );

    return rootCategories.map(category => renderCategory(category, level));
  };

  if (isLoading) {
    return <div>Loading categories...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Expense Categories</CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={handleCreateCategory}>
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? 'Edit Category' : 'Create New Category'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    {...form.register('name')}
                    placeholder="Enter category name"
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-red-500 mt-1">
                      {form.formState.errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...form.register('description')}
                    placeholder="Enter category description"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="parentId">Parent Category</Label>
                  <Select
                    value={form.watch('parentId') || ''}
                    onValueChange={(value) => form.setValue('parentId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select parent category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No parent (root category)</SelectItem>
                      {categories
                        .filter(cat => cat.id !== editingCategory?.id) // Don't allow self as parent
                        .map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="accountId">Default Account</Label>
                  <Select
                    value={form.watch('accountId') || ''}
                    onValueChange={(value) => form.setValue('accountId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select default account" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No default account</SelectItem>
                      {accounts.map((account) => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="sortOrder">Sort Order</Label>
                  <Input
                    id="sortOrder"
                    type="number"
                    {...form.register('sortOrder', { valueAsNumber: true })}
                    placeholder="0"
                  />
                  {form.formState.errors.sortOrder && (
                    <p className="text-sm text-red-500 mt-1">
                      {form.formState.errors.sortOrder.message}
                    </p>
                  )}
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isCreating || isUpdating}>
                    {isCreating || isUpdating
                      ? 'Saving...'
                      : editingCategory
                      ? 'Update'
                      : 'Create'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {categories.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FolderTree className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No expense categories found.</p>
            <p className="text-sm">Create your first category to get started.</p>
          </div>
        ) : (
          <div className="space-y-2">
            {renderCategoryTree(categories)}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
