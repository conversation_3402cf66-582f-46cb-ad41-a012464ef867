'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, Settings, RefreshCw } from 'lucide-react';
import { BranchSelector } from '@/components/common/BranchSelector';
import { useDefaultOrganizationBranch } from '@/hooks/useDefaultOrganizationBranch';

/**
 * Demo component to showcase the enhanced default organization and branch functionality
 */
export function DefaultsDemo() {
  const {
    defaultOrganizationId,
    defaultBranchId,
    isUsingDefaults,
    hasDefaults,
    getDefaultOrganization,
    getDefaultBranch,
    isCurrentSelectionDefault,
    clearDefaults,
  } = useDefaultOrganizationBranch();

  const defaultOrganization = getDefaultOrganization();
  const defaultBranch = getDefaultBranch();

  return (
    <div className="space-y-6">
      {/* Main Branch Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Organization & Branch Selection
          </CardTitle>
          <CardDescription>
            Select your organization and branch. You can set defaults by clicking the star icons.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <BranchSelector />
        </CardContent>
      </Card>

      {/* Defaults Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Default Settings Status
          </CardTitle>
          <CardDescription>
            Your current default organization and branch preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Defaults */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Current Defaults:</h4>
            {hasDefaults() ? (
              <div className="space-y-2">
                {defaultOrganization && (
                  <div className="flex items-center gap-2 text-sm">
                    <Badge variant="secondary">Organization</Badge>
                    <span>{defaultOrganization.name}</span>
                    <Star className="h-3 w-3 fill-current text-yellow-500" />
                  </div>
                )}
                {defaultBranch && (
                  <div className="flex items-center gap-2 text-sm">
                    <Badge variant="secondary">Branch</Badge>
                    <span>{defaultBranch.name}</span>
                    <Star className="h-3 w-3 fill-current text-yellow-500" />
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No defaults set</p>
            )}
          </div>

          {/* Status Indicators */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Status:</h4>
            <div className="flex flex-wrap gap-2">
              <Badge variant={isUsingDefaults ? "default" : "outline"}>
                {isUsingDefaults ? "Using Defaults" : "Manual Selection"}
              </Badge>
              <Badge variant={isCurrentSelectionDefault() ? "default" : "outline"}>
                {isCurrentSelectionDefault() ? "Matches Defaults" : "Different from Defaults"}
              </Badge>
              <Badge variant={hasDefaults() ? "default" : "outline"}>
                {hasDefaults() ? "Defaults Configured" : "No Defaults"}
              </Badge>
            </div>
          </div>

          {/* Actions */}
          {hasDefaults() && (
            <div className="pt-2 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={clearDefaults}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Reset to First Available
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use Defaults</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div className="space-y-2">
            <h4 className="font-medium">Setting Defaults:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li>Select an organization or branch from the dropdowns above</li>
              <li>Click the star icon next to the label to set it as your default</li>
              <li>Default selections will be automatically chosen when you log in</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Visual Indicators:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li><Star className="h-3 w-3 fill-current text-yellow-500 inline mr-1" /> Yellow star = Default item</li>
              <li><Badge variant="secondary" className="text-xs inline mr-1">Default</Badge> Badge = Currently using defaults</li>
              <li>Star icons in dropdowns show which items are set as defaults</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Persistence:</h4>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li>Defaults are saved to your browser's local storage</li>
              <li>They will be remembered across browser sessions</li>
              <li>Future versions will sync defaults to your user profile</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
