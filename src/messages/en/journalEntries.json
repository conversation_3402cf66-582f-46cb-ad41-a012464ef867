{"title": "Journal Entries", "subtitle": "View and manage all journal entries in your accounting system", "searchPlaceholder": "Search journal entries...", "table": {"title": "Journal Entries", "entryDate": "Entry Date", "entryNumber": "Entry Number", "description": "Description", "sourceType": "Source Type", "reference": "Reference", "debitAmount": "Debit Amount", "creditAmount": "Credit Amount", "totalAmount": "Total Amount", "status": "Status", "actions": "Actions", "noEntriesFound": "No journal entries found", "loading": "Loading journal entries...", "error": "Failed to load journal entries"}, "actions": {"viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "duplicate": "Duplicate", "export": "Export", "print": "Print"}, "details": {"title": "Journal Entry Details", "entryNumber": "Entry Number", "date": "Date", "description": "Description", "reference": "Reference", "sourceType": "Source Type", "totalAmount": "Total Amount", "lines": "Journal Entry Lines", "account": "Account", "debit": "Debit", "credit": "Credit", "totals": "Totals", "close": "Close"}, "status": {"draft": "Draft", "posted": "Posted", "reversed": "Reversed", "pending": "Pending"}, "sourceTypes": {"manual": "Manual", "invoice": "Invoice", "bill": "Bill", "payment": "Payment", "adjustment": "Adjustment", "depreciation": "Depreciation", "closing": "Closing Entry"}, "filters": {"all": "All Entries", "thisMonth": "This Month", "lastMonth": "Last Month", "thisYear": "This Year", "dateRange": "Date Range", "status": "Status", "sourceType": "Source Type"}, "summary": {"totalEntries": "Total Entries", "thisMonth": "This Month", "totalDebits": "Total Debits", "totalCredits": "Total Credits", "outOfBalance": "Out of Balance"}, "errors": {"loadFailed": "Failed to load journal entries", "detailsFailed": "Failed to load journal entry details", "notFound": "Journal entry not found", "unauthorized": "You don't have permission to view this journal entry"}, "notifications": {"deleteSuccess": "Journal entry deleted successfully", "deleteError": "Failed to delete journal entry", "updateSuccess": "Journal entry updated successfully", "updateError": "Failed to update journal entry"}}