{"app": {"name": "ADC Account", "tagline": "Financial Management"}, "employees": {"title": "Employee Management", "subtitle": "Manage your employees and their information", "addEmployee": "Add Employee", "filters": {"search": "Search employees...", "status": "Status", "allStatuses": "All Statuses", "active": "Active", "inactive": "Inactive"}, "table": {"name": "Name", "contact": "Contact", "status": "Status", "actions": "Actions", "noEmployeesFound": "No employees found.", "addOnePrompt": "Add one using the button above."}, "dialog": {"add": {"title": "Add New Employee", "description": "Enter the details for the new employee."}, "edit": {"title": "Edit Employee", "description": "Update the details for this employee."}, "delete": {"title": "Delete Employee", "description": "Are you sure you want to delete this employee? This action cannot be undone."}, "form": {"name": "Name", "namePlaceholder": "Enter employee name", "contact": "Contact", "contactPlaceholder": "Enter contact information", "status": "Status", "selectStatus": "Select status"}, "buttons": {"cancel": "Cancel", "save": "Save", "saving": "Saving...", "update": "Update", "updating": "Updating...", "delete": "Delete", "deleting": "Deleting..."}}, "notifications": {"createSuccess": "Employee created successfully", "createError": "Failed to create employee", "updateSuccess": "Employee updated successfully", "updateError": "Failed to update employee", "deleteSuccess": "Employee deleted successfully", "deleteError": "Failed to delete employee"}}, "payroll": {"title": "Payroll Management", "manageEmployees": "Manage Employees", "viewReports": "View Reports", "tabs": {"overview": "Overview", "payPeriods": "Pay Periods", "taxes": "Taxes", "benefits": "Benefits"}, "overview": {"title": "Payroll Overview", "description": "View and manage your payroll information", "nextPayroll": "Next Payroll", "totalEmployees": "Total Employees", "totalPayroll": "Total Payroll", "averageSalary": "Average Salary"}, "payPeriods": {"title": "Pay Periods", "description": "Manage your pay periods and payment schedules", "createPayPeriod": "Create Pay Period", "noPayPeriods": "No pay periods found."}, "taxes": {"title": "Payroll Taxes", "description": "Manage tax settings for payroll", "federalTax": "Federal Tax", "stateTax": "State Tax", "medicareTax": "Medicare Tax", "socialSecurityTax": "Social Security Tax"}, "benefits": {"title": "Employee Benefits", "description": "Manage employee benefits and deductions", "healthInsurance": "Health Insurance", "retirement": "Retirement", "otherBenefits": "Other Benefits"}}, "banking": {"title": "Banking", "subtitle": "Manage bank accounts and reconciliations", "bankAccounts": {"title": "Bank Account Management", "addAccount": "Add Bank Account", "linkAccount": "Link to Chart of Account", "accountName": "Account Name", "bankName": "Bank Name", "accountNumber": "Account Number", "currency": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Balance", "lastUpdated": "Last Updated", "status": "Status", "actions": "Actions", "noAccounts": "No bank accounts found", "addYourFirst": "Add your first bank account to get started", "edit": "Edit Account", "delete": "Delete Account", "view": "View Account", "reconcile": "Reconcile", "import": "Import Transactions", "export": "Export Transactions", "statuses": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "closed": "Closed"}}, "reconciliation": {"title": "Bank Reconciliation", "description": "Reconcile your bank accounts with your accounting records", "startDate": "Start Date", "endDate": "End Date", "startingBalance": "Starting Balance", "endingBalance": "Ending Balance", "unreconciled": "Unreconciled Transactions", "reconciled": "Reconciled Transactions", "difference": "Difference", "createReconciliation": "Create Reconciliation", "noReconciliations": "No reconciliations found", "addYourFirst": "Create your first reconciliation to get started", "bankTransactions": "Bank Transactions", "accountTransactions": "Account Transactions", "match": "Match Transactions", "unmatch": "Unmatch", "createAdjustment": "Create Adjustment", "completeReconciliation": "Complete Reconciliation", "errors": {"idMissing": "Reconciliation ID is missing", "notFound": "Reconciliation not found", "alreadyCompleted": "This reconciliation is already completed"}, "status": {"inProgress": "In Progress", "completed": "Completed", "abandoned": "Abandoned"}, "dialog": {"adjustment": {"title": "Create Adjustment Entry", "description": "Create an adjustment entry to reconcile differences", "amount": "Adjustment Amount", "descriptionLabel": "Description", "date": "Date", "account": "Adjustment Account"}}}, "statements": {"title": "Bank Statements", "upload": "Upload Statement", "view": "View Statement", "download": "Download Statement", "delete": "Delete Statement", "noStatements": "No statements found", "uploadYourFirst": "Upload your first bank statement", "uploadSuccess": "Statement uploaded successfully", "uploadError": "Failed to upload statement", "deleteSuccess": "Statement deleted successfully", "deleteError": "Failed to delete statement"}, "form": {"accountName": "Account Name", "accountNamePlaceholder": "Enter account name", "bankName": "Bank Name", "bankNamePlaceholder": "Enter bank name", "accountNumber": "Account Number (masked)", "accountNumberPlaceholder": "Last 4 digits only (e.g., XXXX1234)", "accountNumberDescription": "Enter the last 4 digits or a masked version.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currencyPlaceholder": "Select currency", "linkedAccount": "Linked Chart of Account", "selectLinkedAccount": "Select linked account", "noUnlinkedAccounts": "No unlinked Asset accounts found. Please create a new Asset account first.", "cannotChangeLinkedAccount": "You cannot change the linked Chart of Account for an existing bank account.", "status": "Status", "selectStatus": "Select status", "statusDescription": "Inactive accounts cannot be used for new transactions.", "validation": {"accountNameRequired": "Account name is required", "accountNameMin": "Account name must be at least 2 characters", "bankNameRequired": "Bank name is required", "bankNameMin": "Bank name must be at least 2 characters", "accountNumberRequired": "Account number is required", "accountNumberMin": "Account number must be at least 4 characters", "currencyRequired": "Currency is required", "currencyLength": "Currency must be a 3-letter code (e.g., USD)", "linkedAccountRequired": "Linked account is required"}}, "dialog": {"add": {"title": "Add New Bank Account", "description": "Enter the details for the new bank account."}, "edit": {"title": "Edit Bank Account", "description": "Update the details of the bank account."}, "delete": {"title": "Delete Bank Account", "description": "Are you sure you want to delete this bank account? This action cannot be undone."}}, "notifications": {"createSuccess": "Bank account created successfully", "createError": "Failed to create bank account", "updateSuccess": "Bank account updated successfully", "updateError": "Failed to update bank account", "deleteSuccess": "Bank account deleted successfully", "deleteError": "Failed to delete bank account", "uploadSuccess": "Statement uploaded successfully", "uploadError": "Failed to upload statement", "reconcileSuccess": "Reconciliation completed successfully", "reconcileError": "Failed to complete reconciliation", "create": "Create Account", "creating": "Creating...", "update": "Save Changes", "updating": "Saving..."}}, "bills": {"title": "Bills", "subtitle": "Manage vendor bills and payments", "actions": {"addNewBill": "Add New Bill", "viewDetails": "View Details", "editBill": "Edit Bill", "voidBill": "Void Bill", "deleteBill": "Delete Bill", "payBill": "Pay Bill", "downloadPdf": "Download PDF", "printBill": "Print Bill", "sendReminder": "Send Reminder", "batchActions": "Batch Actions", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "markAsVoid": "<PERSON> as <PERSON><PERSON>"}, "filters": {"search": "Search bills...", "status": "Status", "vendor": "<PERSON><PERSON><PERSON>", "dateRange": "Date Range", "amount": "Amount", "dueDate": "Due Date", "allStatuses": "All Statuses", "allVendors": "All Vendors", "resetFilters": "Reset Filters", "applyFilters": "Apply Filters"}, "table": {"billNumber": "<PERSON>", "vendor": "<PERSON><PERSON><PERSON>", "issueDate": "Issue Date", "billDate": "<PERSON>", "dueDate": "Due Date", "amount": "Amount", "status": "Status", "actions": "Actions", "noBillsFound": "No bills found.", "noBillsMatchingCriteria": "No bills found matching your search criteria.", "createYourFirst": "Create your first bill!", "selected": "{count} selected", "billsFound": "{count} {count, plural, one {bill} other {bills}} found", "matching": "matching \"{query}\"", "showingBills": "Showing {showing} of {total} bills", "page": "Page {current} of {total}", "goToPage": "Go to page"}, "status": {"draft": "Draft", "open": "Open", "partial": "Partial", "paid": "Paid", "overdue": "Overdue", "void": "Void", "pending": "Pending", "partiallyPaid": "Partially Paid", "due": "Due"}, "dialog": {"add": {"title": "Add New Bill", "description": "Enter the details for the new bill."}, "edit": {"title": "Edit Bill", "description": "Update the bill details.", "errorLoadingDates": "Failed to load bill dates for editing."}, "delete": {"title": "Delete Bill", "description": "Are you sure you want to delete this bill? This action cannot be undone.", "confirmationText": "You are about to delete bill #{number}."}, "void": {"title": "Void Bill", "description": "Are you sure you want to void this bill? This action cannot be undone.", "confirmationText": "Bill #{number} has been voided."}, "batchDelete": {"title": "Delete Bills", "description": "Are you sure you want to delete the selected bills? This action cannot be undone.", "confirmationText": "You are about to delete {count} bills."}, "batchUpdate": {"title": "Update Bill Status", "description": "Update the status of the selected bills."}, "form": {"vendor": "<PERSON><PERSON><PERSON>", "selectVendor": "Select vendor", "billNumber": "<PERSON>", "billDate": "<PERSON>", "issueDate": "Issue Date", "dueDate": "Due Date", "terms": "Terms", "termsPlaceholder": "Enter payment terms...", "notes": "Notes", "notesPlaceholder": "Enter any additional notes...", "items": "<PERSON>", "addItem": "Add Item", "item": "<PERSON><PERSON>", "description": "Description", "quantity": "Quantity", "price": "Price", "tax": "Tax", "taxAmount": "Tax Amount", "totalAmount": "Total Amount", "total": "Total", "subtotal": "Subtotal", "taxTotal": "Tax Total", "grandTotal": "Grand Total", "removeItem": "Remove Item", "taxRate": "Tax Rate", "selectTaxRate": "Select tax rate", "account": "Account", "selectAccount": "Select account", "loadingVendors": "Loading vendors...", "errorLoadingVendors": "Error loading vendors", "noVendorsAvailable": "No vendors available"}, "buttons": {"cancel": "Cancel", "create": "Create Bill", "creating": "Creating...", "update": "Update Status", "updating": "Updating...", "delete": "Delete Bills", "deleting": "Deleting...", "save": "Save Changes", "saving": "Saving...", "void": "Void Bill", "voiding": "Voiding..."}}, "notifications": {"createSuccess": "Bill created successfully", "createError": "Failed to create bill", "updateSuccess": "Bill updated successfully", "updateError": "Failed to update bill", "deleteSuccess": "Bill deleted successfully", "deleteError": "Failed to delete bill", "voidSuccess": "Bill voided successfully", "voidError": "Failed to void bill", "error": "Error", "batchDeleteSuccess": "{count} bills deleted successfully", "batchDeleteError": "Failed to delete bills", "batchUpdateSuccess": "{count} bills updated successfully", "batchUpdateError": "Failed to update bills", "paymentSuccess": "Payment recorded successfully", "paymentError": "Failed to record payment"}}, "notDone": {"title": "Under Construction", "message": "This page is currently under development and will be available soon.", "backToHome": "Back to Home", "exampleContent": "This is an example of the NotDone component with additional content."}, "common": {"loading": "Loading...", "redirecting": "Redirecting to login page...", "refresh": "Refresh", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "view": "View", "search": "Search", "filter": "Filter", "actions": "Actions", "status": "Status", "details": "Details", "back": "Back", "next": "Next", "previous": "Previous", "first": "First", "last": "Last", "submit": "Submit", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "noData": "No data available", "noResults": "No results found", "all": "All", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "yes": "Yes", "no": "No", "date": "Date", "time": "Time", "amount": "Amount", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "name": "Name", "description": "Description", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "state": "State", "country": "Country", "zipCode": "Zip Code", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "theme": "Theme", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "dashboard": "Dashboard", "reports": "Reports", "help": "Help", "support": "Support", "documentation": "Documentation", "about": "About", "version": "Version", "copyright": "Copyright", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "contactUs": "Contact Us", "allRightsReserved": "All rights reserved.", "required": "Required", "optional": "Optional", "invalidInput": "Invalid input", "passwordMismatch": "Passwords do not match", "passwordRequirements": "Password must be at least 8 characters long and include a number and a special character", "emailInvalid": "Please enter a valid email address", "phoneInvalid": "Please enter a valid phone number", "dateInvalid": "Please enter a valid date", "timeInvalid": "Please enter a valid time", "numberInvalid": "Please enter a valid number", "currencyInvalid": "Please enter a valid currency amount", "fieldRequired": "{field} is required", "fieldInvalid": "{field} is invalid", "fieldTooShort": "{field} is too short", "fieldTooLong": "{field} is too long", "fieldMinValue": "{field} must be at least {min}", "fieldMaxValue": "{field} must be at most {max}", "unknownError": "An unknown error occurred", "noItemsSelected": "Please select at least one item to perform this action", "title": "Budget Management", "tabs": {"budget": "Budget Items", "templates": "Templates", "reports": "Reports"}, "periodSelection": {"title": "Period Selection", "year": "Year", "month": "Month", "selectYear": "Select year", "selectMonth": "Select month"}, "export": {"button": "Export"}}, "budget": {"title": "Budget Management", "tabs": {"budget": "Budget Items", "templates": "Templates", "reports": "Reports"}, "periodSelection": {"title": "Period Selection", "year": "Year", "month": "Month", "selectYear": "Select year", "selectMonth": "Select month"}, "export": {"button": "Export"}}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember Me", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "orContinueWith": "Or continue with", "passwordResetSent": "Password reset link has been sent to your email", "passwordResetSuccess": "Your password has been reset successfully", "passwordChangeSuccess": "Your password has been changed successfully", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "accountNotFound": "Account not found", "accountLocked": "Account is locked", "accountDisabled": "Account is disabled", "sessionExpired": "Your session has expired, please sign in again", "unauthorized": "You are not authorized to access this resource", "verifyEmail": "<PERSON><PERSON><PERSON>", "emailVerified": "Email verified successfully", "emailVerificationSent": "Email verification link has been sent to your email", "emailAlreadyVerified": "Email is already verified", "emailNotVerified": "Email is not verified", "enterEmailToLogin": "Enter your email below to login to your account", "loginFailed": "Login Failed", "loginSuccess": "Login successful! Redirecting to dashboard...", "loggingIn": "Logging in...", "loginError": "Login error", "unexpectedError": "An unexpected error occurred during login. Please try again.", "unknownError": "An unknown error occurred", "alreadyLoggedIn": "Already logged in", "password": "Password", "enterDetailsToRegister": "Enter your details below to create an account", "registering": "Registering...", "registrationFailed": "Registration failed", "emailAlreadyExists": "Email already exists", "redirectingToLogin": "Your account has been created. Redirecting to login..."}, "dashboard": {"title": "Dashboard", "refresh": "Refresh", "welcome": "Welcome, {name}", "quickActions": "Quick Actions", "recentActivity": "Recent Activity", "overview": "Overview", "statistics": "Statistics", "performance": "Performance", "trends": "Trends", "alerts": "<PERSON><PERSON><PERSON>", "notifications": "Notifications", "tasks": "Tasks", "calendar": "Calendar", "upcomingEvents": "Upcoming Events", "recentTransactions": "Recent Transactions", "pendingApprovals": "Pending Approvals", "accountSummary": "Account Summary", "cashFlow": "Cash Flow", "income": "Income", "expenses": "Expenses", "profit": "Profit", "loss": "Loss", "revenue": "Revenue", "budget": "Budget", "forecast": "Forecast", "actualVsBudget": "Actual vs Budget", "thisMonth": "This Month", "lastMonth": "Last Month", "thisYear": "This Year", "lastYear": "Last Year", "ytd": "Year to Date", "mtd": "Month to Date", "selectPeriod": "Select period", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days", "last6Months": "Last 6 Months", "last12Months": "Last 12 Months", "totalRevenue": "Total Revenue", "totalExpenses": "Total Expenses", "netIncome": "Net Income", "cashBalance": "Cash Balance", "accountsReceivable": "Accounts Receivable", "accountsPayable": "Accounts Payable", "forSelectedPeriod": "For the selected period", "currentBalance": "Current balance", "overdue90Days": "overdue > 90 days", "dueNext30Days": "due in next 30 days", "incomeVsExpenses": "Income vs. Expenses over time", "monthlyCashFlow": "Monthly Cash Flow", "month": "Month", "net": "Net", "summary": "Summary", "totalIncome": "Total Income", "noCashFlowData": "No cash flow data available for the selected period.", "topCustomers": "Top Customers", "topCustomersDesc": "Your top customers by sales volume for the selected period.", "customer": "Customer", "totalSales": "Total Sales", "openInvoices": "Open Invoices", "noCustomerData": "No customer data available.", "topVendors": "Top Vendors", "topVendorsDesc": "Your top vendors by purchase volume for the selected period.", "vendor": "<PERSON><PERSON><PERSON>", "totalPurchases": "Total Purchases", "openBills": "Open Bills", "noVendorData": "No vendor data available.", "recentTransactionsDesc": "Your most recent financial transactions.", "date": "Date", "description": "Description", "type": "Type", "amount": "Amount", "noRecentTransactions": "No recent transactions.", "agingSummary": "Aging Summary", "arAging": "Accounts Receivable Aging", "arAgingDesc": "Summary of outstanding customer invoices by age.", "apAging": "Accounts Payable Aging", "apAgingDesc": "Summary of outstanding vendor bills by age.", "agingPeriod": "Aging Period", "current": "Current", "days1_30": "1-30 Days", "days31_60": "31-60 Days", "days61_90": "61-90 Days", "daysOver90": "Over 90 Days", "total": "Total", "noDataAvailable": "No data available."}, "settings": {"title": "Settings", "general": "General", "account": "Account", "preferencesTab": "Preferences", "notifications": "Notifications", "security": "Security", "appearance": "Appearance", "language": "Language", "dateFormat": "Date Format", "timeFormat": "Time Format", "timezone": "Timezone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemMode": "System Mode", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "smsNotifications": "SMS Notifications", "twoFactorAuth": "Two-Factor Authentication", "changePassword": "Change Password", "deleteAccount": "Delete Account", "exportData": "Export Data", "importData": "Import Data", "resetSettings": "Reset Settings", "saveChanges": "Save Changes", "settingsSaved": "Setting<PERSON> saved successfully", "settingsReset": "Settings reset to defaults", "startPage": "Start Page", "displayDensity": "Display Density", "tablePageSize": "Table Page Size", "compact": "Compact", "comfortable": "Comfortable", "spacious": "Spacious", "companySettings": {"title": "Company Settings", "description": "Update your company's information.", "form": {"name": "Company Name", "namePlaceholder": "Your Company Inc.", "address": "Address", "addressPlaceholder": "123 Business St, City, State, ZIP", "phone": "Phone Number", "phonePlaceholder": "+****************", "email": "Primary Contact Email", "emailPlaceholder": "<EMAIL>"}, "buttons": {"save": "Save Changes", "saving": "Saving...", "cancel": "Cancel"}, "notifications": {"saveSuccess": "Company settings updated successfully", "saveError": "Failed to update company settings"}}, "preferences": {"title": "User Preferences", "description": "Manage your application preferences and settings.", "theme": {"description": "Choose your preferred theme for the application."}, "dateFormat": {"description": "Choose how dates are displayed."}, "language": {"description": "Choose your preferred language."}, "notifications": {"email": {"description": "Receive notifications via email."}, "browser": {"description": "Receive notifications in your browser."}, "mobile": {"description": "Receive notifications on your mobile device."}}, "displayDensity": {"description": "Choose how dense the UI elements should be."}, "tablePageSize": {"description": "Choose how many items to show per page in tables."}, "errors": {"loginRequired": "You must be logged in to update preferences.", "updateFailed": "Could not update preferences. Please try again."}, "success": {"updated": "Preferences updated successfully."}}, "loginRequired": "You need to be logged in to access settings", "loginButton": "Log In", "tabs": {"preferences": "Preferences", "company": "Company", "merchants": "Merchants", "users": "Users", "subscription": "Subscription", "audit": "<PERSON><PERSON>"}, "company": {"title": "Company Settings", "description": "Manage your company information and settings.", "button": "Go to Company Settings"}, "merchants": {"title": "Merchant Management", "description": "Manage merchants and user permissions for each merchant.", "button": "Go to Merchant Management"}, "users": {"title": "User Management", "description": "Manage users and their permissions.", "button": "Go to User Management"}, "subscription": {"title": "Subscription Management", "description": "Manage your subscription plan and billing details.", "button": "Manage Subscription"}, "audit": {"title": "<PERSON><PERSON>", "description": "View system activity and user actions for security and compliance.", "button": "View Audit Logs"}}, "docs": {"overview": {"title": "Documentation Overview", "description": "Welcome to the ADC Account API documentation. Here you'll find comprehensive guides and documentation to help you start working with our API as quickly as possible."}}, "help": {"title": "How can we help you?", "subtitle": "Find answers to your questions and learn how to get the most out of ADC Account", "searchPlaceholder": "Search for help articles...", "tabs": {"faq": "FAQs", "guides": "Guides", "videos": "Videos", "contact": "Contact Us"}, "categories": {"gettingStarted": "Getting Started", "financialManagement": "Financial Management", "billingSubscription": "Billing & Subscription", "troubleshooting": "Troubleshooting"}, "descriptions": {"gettingStarted": "Basic questions about using ADC Account", "financialManagement": "Questions about financial features", "billingSubscription": "Questions about billing and plans", "troubleshooting": "Common issues and solutions"}, "faq": {"createAccount": "How do I create an account?", "systemRequirements": "What are the system requirements?", "resetPassword": "How do I reset my password?", "dataSecure": "Is my data secure?", "inviteTeam": "How do I invite team members?", "connectBank": "How do I connect my bank account?", "createJournal": "How do I create a journal entry?", "reconcileAccounts": "How do I reconcile my accounts?", "generateReports": "How do I generate financial reports?", "trackExpenses": "How do I track expenses?", "plansOffered": "What plans do you offer?", "upgradePlan": "How do I upgrade my plan?", "updateBilling": "How do I update my billing information?", "getRefund": "Can I get a refund?", "discounts": "Do you offer discounts?", "cantLogin": "Why can't I log in?", "bankNotWorking": "Why is my bank connection not working?", "reportsNotGenerating": "Why are my reports not generating?", "fixImportErrors": "How do I fix import errors?", "transactionsMissing": "Why are my transactions missing?"}, "viewAll": "View All", "guides": {"gettingStarted": "Getting Started Guide", "financialManagement": "Financial Management", "reporting": "Reporting Guide", "bankingIntegration": "Banking Integration", "taxManagement": "Tax Management", "advancedFeatures": "Advanced Features"}, "guideDescriptions": {"gettingStarted": "Learn the basics of setting up and using ADC Account.", "financialManagement": "Learn how to manage your finances effectively with ADC Account.", "reporting": "Learn how to generate and interpret financial reports.", "bankingIntegration": "Learn how to connect and manage your bank accounts.", "taxManagement": "Learn how to manage taxes and prepare for tax season.", "advancedFeatures": "Explore advanced features and customizations."}, "readGuide": "Read Guide", "watchVideo": "Watch Video", "videos": {"gettingStarted": "Getting Started Tutorial", "financialReporting": "Financial Reporting", "bankingIntegration": "Banking Integration", "taxManagement": "Tax Management"}, "videoDescriptions": {"gettingStarted": "A comprehensive guide to getting started with ADC Account.", "financialReporting": "Learn how to generate and interpret financial reports.", "bankingIntegration": "Learn how to connect and manage your bank accounts.", "taxManagement": "Learn how to manage taxes and prepare for tax season."}, "contact": {"title": "Contact Support", "description": "Get in touch with our support team for personalized assistance", "liveChat": "Live Chat", "liveChatDesc": "Chat with our support team in real-time", "startChat": "Start Chat", "emailSupport": "Email Support", "emailSupportDesc": "Send us an email and we'll respond within 24 hours", "emailUs": "Email Us", "phoneSupport": "Phone Support", "phoneSupportDesc": "Call us directly for immediate assistance", "callUs": "Call Us", "supportHours": "Support Hours", "mondayFriday": "Monday - Friday", "mondayFridayHours": "9:00 AM - 8:00 PM EST", "saturday": "Saturday", "saturdayHours": "10:00 AM - 6:00 PM EST", "sunday": "Sunday", "sundayClosed": "Closed", "holidays": "Holidays", "holidaysHours": "Hours may vary"}, "stillNeedHelp": {"title": "Still need help?", "description": "If you couldn't find what you were looking for, our support team is always ready to help. Contact us and we'll get back to you as soon as possible."}}, "users": {"title": "User Management", "addUser": "Add User", "filters": {"title": "Filters", "search": "Search", "searchPlaceholder": "Search by name or email", "role": "Role", "allRoles": "All roles", "status": "Status", "allStatuses": "All statuses", "resetFilters": "Reset Filters"}, "table": {"title": "Users", "description": "Manage your application users", "name": "Name", "email": "Email", "role": "Role", "status": "Status", "merchants": "Merchants", "createdAt": "Created At", "actions": "Actions", "active": "Active", "inactive": "Inactive", "none": "None", "more": "more", "noUsersFound": "No users found."}, "dialog": {"create": {"title": "Create New User", "description": "Add a new user to the system."}, "edit": {"title": "Edit User", "description": "Update user information."}, "editName": {"title": "Edit User Name", "nameLabel": "Name"}, "delete": {"title": "Delete User", "description": "Are you sure you want to delete this user? This action cannot be undone.", "confirmationText": "You are about to delete user:", "confirmTitle": "Are you absolutely sure?", "confirmDescription": "This action cannot be undone. This will permanently delete the user account for {email}."}, "tabs": {"general": "General", "merchants": "Merchants"}, "form": {"name": "Name", "namePlaceholder": "<PERSON>", "email": "Email", "emailPlaceholder": "<EMAIL>", "password": "Password", "passwordPlaceholder": "••••••••", "passwordEdit": "Password (leave blank to keep current)", "role": "Role", "selectRole": "Select role", "active": "Active", "merchantAccess": "Merchant Access", "merchantAccessDescription": "Select which merchants this user can access", "noMerchantsAvailable": "No merchants available", "defaultMerchant": "De<PERSON><PERSON>", "selectDefaultMerchant": "Select default merchant", "defaultMerchantDescription": "This merchant will be selected by default when the user logs in", "none": "None"}, "buttons": {"cancel": "Cancel", "create": "Create User", "creating": "Creating...", "update": "Update User", "updating": "Updating...", "save": "Save Changes", "saving": "Saving...", "delete": "Delete User", "deleting": "Deleting..."}}, "notifications": {"createSuccess": "User created successfully", "createError": "Failed to create user", "updateSuccess": "User updated successfully", "updateError": "Failed to update user", "nameUpdateSuccess": "User name updated successfully", "nameUpdateError": "Failed to update user name", "deleteSuccess": "User deleted successfully", "deleteError": "Failed to delete user", "validationError": "Validation Error", "nameEmpty": "Name cannot be empty."}}, "chartOfAccounts": {"title": "Chart of Accounts", "subtitle": "Manage your financial accounts and their structure", "addNewAccount": "Add New Account", "journalEntries": "Journal Entries", "banking": "Banking", "assets": "Assets", "searchPlaceholder": "Search accounts...", "tabs": {"all": "All", "assets": "Assets", "liabilities": "Liabilities", "equity": "Equity", "revenue": "Revenue", "expenses": "Expenses", "assetSubtypes": {"current": "Current", "fixed": "Fixed", "intangible": "Intangible", "other": "Other"}, "liabilitySubtypes": {"current": "Current", "longTerm": "Long Term", "other": "Other"}, "equitySubtypes": {"commonStock": "Common Stock", "retainedEarnings": "Retained Earnings", "other": "Other"}, "revenueSubtypes": {"operating": "Operating", "nonOperating": "Non-Operating", "other": "Other"}, "expenseSubtypes": {"operating": "Operating", "nonOperating": "Non-Operating", "other": "Other"}}, "table": {"title": "Account List", "accountsFound": "{count} {count, plural, one {account} other {accounts}} found", "inCategory": "in {category} category", "matching": "matching \"{query}\"", "code": "Code", "name": "Name", "type": "Type", "description": "Description", "status": "Status", "banking": "Banking", "actions": "Actions", "noAccountsFound": "No accounts found.", "addOnePrompt": "Add one using the button above.", "showingAccounts": "Showing {showing} of {total} accounts", "rowsPerPage": "Rows per page:", "page": "Page {current} of {total}", "goToPage": "Go to page"}, "status": {"active": "Active", "inactive": "Inactive", "notLinked": "Not Linked", "na": "N/A"}, "dialog": {"add": {"title": "Add New Account", "description": "Enter the details for the new account. Click save when you're done."}, "edit": {"title": "Edit Account", "description": "Update the account details. Click save when you're done."}, "delete": {"title": "Are you sure?", "description": "This action cannot be undone. This will permanently delete the account {code} - {name}."}, "bankLink": {"cannotLink": "Cannot link to bank account", "onlyAssets": "Only Asset type accounts can be linked to bank accounts.", "title": "Link to Bank Account", "description": "Create a new bank account linked to the chart of account: {accountName}", "alreadyLinkedWarning": "This account is already linked to bank account \"{accountName}\". Each Chart of Account can only be linked to one bank account.", "errors": {"alreadyLinked": "This account is already linked to bank account \"{accountName}\". Each Chart of Account can only be linked to one bank account.", "unknown": "An unknown error occurred during submission"}, "form": {"accountName": "Account Name", "bankName": "Bank Name", "accountNumber": "Masked Account Number", "accountNumberHelp": "Enter the last 4 digits or a masked version.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "validation": {"accountNameMin": "Account name must be at least 2 characters.", "bankNameMin": "Bank name must be at least 2 characters.", "accountNumberMin": "Masked account number must be at least 4 characters.", "currencyLength": "Currency must be a 3-letter code (e.g., USD)."}}, "buttons": {"creating": "Creating...", "create": "Create Bank Account"}}, "form": {"code": "Code", "name": "Name", "type": "Type", "description": "Description", "status": "Status", "selectStatus": "Select status", "selectType": "Select account type", "assetTypes": {"general": "<PERSON><PERSON> (General)", "current": "Current Asset", "fixed": "Fixed Asset", "intangible": "Intangible Asset", "other": "Other Asset"}, "liabilityTypes": {"general": "Liability (General)", "current": "Current Liability", "longTerm": "Long Term Liability", "other": "Other Liability"}, "equityTypes": {"general": "Equity (General)", "commonStock": "Common Stock", "retainedEarnings": "Retained Earnings", "other": "Other Equity"}, "revenueTypes": {"general": "Revenue (General)", "operating": "Operating Revenue", "nonOperating": "Non-Operating Revenue", "other": "Other Revenue"}, "expenseTypes": {"general": "Expense (General)", "operating": "Operating Expense", "nonOperating": "Non-Operating Expense", "other": "Other Expense"}}, "buttons": {"cancel": "Cancel", "save": "Save Account", "saving": "Saving...", "update": "Save Changes", "updating": "Saving...", "delete": "Delete Account", "deleting": "Deleting..."}, "messages": {"createSuccess": "Account created successfully", "updateSuccess": "Account updated successfully", "deleteSuccess": "Account deleted successfully", "linkSuccess": "Bank account linked successfully", "error": "Error: {message}"}}, "permissions": {"error": {"title": "Permission Error", "message": "Please contact your administrator if you believe you should have access to this feature."}, "staffRequired": "Staff permission required", "managerRequired": "Requires Manager", "adminRequired": "Requires Admin"}}, "assets": {"title": "Asset Management", "subtitle": "Manage your fixed assets, track depreciation, and record disposals.", "actions": {"runDepreciation": "Run Depreciation", "addNewAsset": "Add New Asset"}, "tabs": {"allAssets": "All Assets", "active": "Active", "disposed": "Disposed"}, "search": {"placeholder": "Search assets..."}, "errors": {"loadingTitle": "Error loading assets", "generic": "An error occurred"}, "noAssets": {"title": "No assets found", "searchMessage": "Try adjusting your search term", "tabMessage": "No {tab} assets found", "emptyMessage": "Get started by adding your first asset"}, "assetCard": {"noImage": "No image", "purchaseCost": "Purchase Cost", "currentValue": "Current Value", "purchaseDate": "Purchase Date", "depreciation": "Depreciation", "actions": {"edit": "Edit", "dispose": "Dispose", "delete": "Delete"}}, "deleteDialog": {"title": "Are you absolutely sure?", "description": "This will permanently delete the asset \"{name}\" and all associated records. This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete"}, "formDialog": {"addTitle": "Add New Asset", "editTitle": "Edit Asset", "addDescription": "Enter the details of your new fixed asset. Click save when you're done.", "editDescription": "Update the details of your fixed asset.", "form": {"name": "Name", "namePlaceholder": "Enter asset name", "description": "Description", "descriptionPlaceholder": "Enter asset description", "purchaseDate": "Purchase Date", "pickDate": "Pick a date", "purchaseCost": "Purchase Cost", "salvageValue": "Salvage Value", "depreciationMethod": "Depreciation Method", "selectMethod": "Select method", "none": "None", "straightLine": "Straight Line", "doubleDeclining": "Double Declining Balance", "usefulLife": "Useful Life (Months)", "assetAccount": "Asset Account", "selectAssetAccount": "Select asset account", "accumulatedDepreciation": "Accumulated Depreciation Account", "selectAccumulatedDepreciation": "Select accumulated depreciation account", "depreciationExpense": "Depreciation Expense Account", "selectDepreciationExpense": "Select depreciation expense account", "assetImage": "Asset Image", "uploadImage": "Upload Asset Image", "imageDescription": "Upload an image of this asset for easier identification", "currentImageUrl": "Current image URL:"}, "buttons": {"cancel": "Cancel", "saving": "Saving...", "update": "Update Asset", "create": "Create Asset"}}, "disposalDialog": {"title": "Dispose <PERSON>", "description": "Record the disposal of this asset.", "form": {"disposalDate": "Disposal Date", "pickDate": "Pick a date", "disposalProceeds": "Disposal Proceeds", "disposalMethod": "Disposal Method", "selectMethod": "Select method", "sold": "Sold", "scrapped": "Scrapped", "donated": "Donated", "stolen": "<PERSON><PERSON><PERSON>", "notes": "Notes", "notesPlaceholder": "Enter any additional notes about the disposal"}, "buttons": {"cancel": "Cancel", "saving": "Saving...", "dispose": "Record Disposal"}}, "depreciationDialog": {"title": "Run Depreciation", "description": "Calculate and record depreciation for your assets.", "form": {"runUntilDate": "Run Until Date", "pickDate": "Pick a date", "selectAssets": "Select Assets", "allAssets": "All Active Assets", "selectedAssets": "Selected Assets", "selectAssetsToDepreciate": "Select assets to depreciate", "noAssetsAvailable": "No active assets available for depreciation"}, "buttons": {"cancel": "Cancel", "saving": "Processing...", "run": "Run Depreciation"}}}, "invoices": {"title": "Invoices", "subtitle": "Manage your customer invoices", "actions": {"createInvoice": "Create Invoice", "recurringInvoices": "Recurring Invoices", "templates": "Templates", "batchActions": "Batch Actions", "sendEmails": "Send Emails", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "markAsVoid": "<PERSON> as <PERSON><PERSON>", "delete": "Delete"}, "filters": {"search": "Search invoices...", "status": "Status", "allStatuses": "All Statuses", "dateRange": "Date Range", "customer": "Customer", "allCustomers": "All Customers", "resetFilters": "Reset Filters"}, "table": {"title": "Invoice List", "invoiceNumber": "Invoice #", "customer": "Customer", "issueDate": "Issue Date", "dueDate": "Due Date", "amount": "Amount", "status": "Status", "actions": "Actions", "noInvoicesFound": "No invoices found.", "noInvoicesMatchingCriteria": "No invoices found matching your search criteria.", "createYourFirst": "Create your first invoice!", "selected": "{count} selected", "accountsFound": "{count} {count, plural, one {invoice} other {invoices}} found", "matching": "matching \"{query}\"", "showingAccounts": "Showing {showing} of {total} invoices", "page": "Page {current} of {total}", "goToPage": "Go to page"}, "status": {"draft": "Draft", "sent": "<PERSON><PERSON>", "viewed": "Viewed", "partial": "Partial", "paid": "Paid", "overdue": "Overdue", "void": "Void"}, "dialog": {"create": {"title": "Create Invoice", "description": "Enter the details for the new invoice."}, "edit": {"title": "Edit Invoice", "description": "Update the invoice details."}, "delete": {"title": "Delete Invoice", "description": "Are you sure you want to delete this invoice? This action cannot be undone.", "confirmationText": "You are about to delete invoice #{number}."}, "batchDelete": {"title": "Delete Invoices", "description": "Are you sure you want to delete the selected invoices? This action cannot be undone.", "confirmationText": "You are about to delete {count} invoices."}, "batchUpdate": {"title": "Update Invoice Status", "description": "Update the status of the selected invoices."}, "batchEmail": {"title": "Send Email Notifications", "description": "Send email notifications for the selected invoices.", "selectTemplate": "Select Email Template", "customMessage": "Custom Message (Optional)", "customMessagePlaceholder": "Enter a custom message to include in the email..."}, "form": {"customer": "Customer", "selectCustomer": "Select customer", "invoiceNumber": "Invoice Number", "issueDate": "Issue Date", "dueDate": "Due Date", "terms": "Terms", "termsPlaceholder": "Enter payment terms...", "notes": "Notes", "notesPlaceholder": "Enter any additional notes...", "items": "Invoice Items", "addItem": "Add Item", "item": "<PERSON><PERSON>", "description": "Description", "quantity": "Quantity", "price": "Price", "tax": "Tax", "total": "Total", "subtotal": "Subtotal", "taxTotal": "Tax Total", "grandTotal": "Grand Total", "removeItem": "Remove Item", "taxRate": "Tax Rate", "selectTaxRate": "Select tax rate", "account": "Account", "selectAccount": "Select account"}, "buttons": {"cancel": "Cancel", "create": "Create Invoice", "creating": "Creating...", "update": "Update Status", "updating": "Updating...", "delete": "Delete Invoices", "deleting": "Deleting...", "send": "Send Emails", "sending": "Sending..."}}, "notifications": {"createSuccess": "Invoice created successfully", "createError": "Failed to create invoice", "updateSuccess": "Invoice updated successfully", "updateError": "Failed to update invoice", "deleteSuccess": "Invoice deleted successfully", "deleteError": "Failed to delete invoice", "batchDeleteSuccess": "{count} invoices deleted successfully", "batchDeleteError": "Failed to delete invoices", "batchUpdateSuccess": "{count} invoices updated successfully", "batchUpdateError": "Failed to update invoices", "batchEmailSuccess": "Emails sent successfully", "batchEmailError": "Failed to send emails"}}, "reports": {"title": "Reports", "subtitle": "View and generate financial reports", "mainPage": {"title": "Financial Reports", "subtitle": "View and generate financial reports for your business", "tabs": {"profitLoss": "Profit & Loss", "balanceSheet": "Balance Sheet", "apAging": "AP Aging", "arAging": "AR Aging", "trialBalance": "Trial Balance", "generalLedger": "General <PERSON><PERSON>"}, "apAging": {"title": "Accounts Payable Aging", "description": "View your outstanding bills categorized by age", "asOfDate": "As of Date", "generateReport": "Generate Report", "loading": "Loading...", "selectDatePrompt": "Please select a date to generate the report"}, "arAging": {"title": "Accounts Receivable Aging", "description": "View your outstanding invoices categorized by age", "asOfDate": "As of Date", "generateReport": "Generate Report", "loading": "Loading...", "selectDatePrompt": "Please select a date to generate the report"}, "generalLedger": {"title": "General <PERSON><PERSON>", "description": "View all transactions for a specific account within a date range", "account": "Account", "selectAccount": "Select an account", "loadingAccounts": "Loading accounts...", "startDate": "Start Date", "endDate": "End Date", "generateReport": "Generate Report", "loading": "Loading...", "selectAccountPrompt": "Please select an account to generate the report", "noTransactions": "No transactions found for the selected account and date range"}, "trialBalance": {"title": "Trial Balance", "description": "View the balance of all accounts as of a specific date", "asOfDate": "As of Date", "generateReport": "Generate Report", "loading": "Loading...", "selectDatePrompt": "Please select a date to generate the report", "account": "Account", "debit": "Debit", "credit": "Credit", "totalDebits": "Total Debits", "totalCredits": "Total Credits", "difference": "Difference"}}, "profitLoss": "Profit & Loss", "balanceSheet": "Balance Sheet", "cashFlow": "Cash Flow", "taxReports": "Tax Reports", "salesReports": "Sales Reports", "inventoryReports": "Inventory Reports", "customReports": "Custom Reports", "generateReport": "Generate Report", "exportReport": "Export Report", "printReport": "Print Report", "saveReport": "Save Report", "scheduleReport": "Schedule Report", "shareReport": "Share Report", "reportSettings": "Report Settings", "reportHistory": "Report History", "noReportsFound": "No reports found", "lastGenerated": "Last Generated", "reportType": "Report Type", "reportPeriod": "Report Period", "startDate": "Start Date", "endDate": "End Date", "asOfDate": "As of Date", "comparisonPeriod": "Comparison Period", "includeSubaccounts": "Include Subaccounts", "showPercentages": "Show Percentages", "showPreviousPeriod": "Show Previous Period", "showBudgetComparison": "Show Budget Comparison", "columns": "Columns", "rows": "Rows", "filters": "Filters", "groupBy": "Group By", "sortBy": "Sort By", "ascending": "Ascending", "descending": "Descending", "applyFilters": "Apply Filters", "resetFilters": "Reset Filters", "saveFilters": "Save Filters", "loadFilters": "Load Filters", "savedFilters": "Saved Filters", "filterName": "Filter Name", "saveAsNew": "Save as New", "overwriteExisting": "Overwrite Existing", "deleteFilter": "Delete Filter", "confirmDeleteFilter": "Are you sure you want to delete this filter?", "filterDeleted": "Filter deleted successfully", "filterSaved": "Filter saved successfully", "errorSavingFilter": "Error saving filter", "errorDeletingFilter": "Error deleting filter", "errorLoadingFilter": "Error loading filter", "noFiltersFound": "No saved filters found", "loading": "Loading report data...", "error": "Error loading report", "tryAgain": "Try Again", "noData": "No data available for the selected period", "total": "Total", "subtotal": "Subtotal", "netIncome": "Net Income", "grossProfit": "Gross Profit", "operatingIncome": "Operating Income", "totalAssets": "Total Assets", "totalLiabilities": "Total Liabilities", "totalEquity": "Total Equity", "totalLiabilitiesAndEquity": "Total Liabilities & Equity", "currentAssets": "Current Assets", "fixedAssets": "Fixed Assets", "otherAssets": "Other Assets", "currentLiabilities": "Current Liabilities", "longTermLiabilities": "Long-Term Liabilities", "otherLiabilities": "Other Liabilities", "revenue": "Revenue", "expenses": "Expenses", "costOfGoodsSold": "Cost of Goods Sold", "operatingExpenses": "Operating Expenses", "otherIncome": "Other Income", "otherExpenses": "Other Expenses", "taxes": "Taxes", "cashInflows": "Cash Inflows", "cashOutflows": "Cash Outflows", "netCashFlow": "Net Cash Flow", "beginningBalance": "Beginning Balance", "endingBalance": "Ending Balance", "period": "Period", "amount": "Amount", "percentage": "Percentage", "variance": "<PERSON><PERSON><PERSON>", "budget": "Budget", "actual": "Actual", "difference": "Difference", "account": "Account", "accountType": "Account Type", "description": "Description", "date": "Date", "reference": "Reference", "debit": "Debit", "credit": "Credit", "balance": "Balance", "customer": "Customer", "vendor": "<PERSON><PERSON><PERSON>", "item": "<PERSON><PERSON>", "quantity": "Quantity", "unitPrice": "Unit Price", "discount": "Discount", "tax": "Tax", "shipping": "Shipping", "subtotalAmount": "Subtotal Amount", "taxAmount": "Tax Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "dueAmount": "Due Amount", "overdue": "Overdue", "current": "Current", "aging": "Aging", "days1_30": "1-30 Days", "days31_60": "31-60 Days", "days61_90": "61-90 Days", "daysOver90": "Over 90 Days", "paymentStatus": "Payment Status", "paid": "Paid", "unpaid": "Unpaid", "partial": "Partial", "void": "Void", "refunded": "Refunded", "paymentMethod": "Payment Method", "paymentDate": "Payment Date", "dueDate": "Due Date", "invoiceNumber": "Invoice Number", "billNumber": "<PERSON>", "purchaseOrderNumber": "Purchase Order Number", "salesOrderNumber": "Sales Order Number", "estimateNumber": "Estimate Number", "receiptNumber": "Receipt Number", "checkNumber": "Check Number", "transactionNumber": "Transaction Number", "journalEntryNumber": "Journal Entry Number", "accountNumber": "Account Number", "category": "Category", "class": "Class", "location": "Location", "department": "Department", "project": "Project", "employee": "Employee", "salesRep": "Sales Rep", "terms": "Terms", "notes": "Notes", "attachments": "Attachments", "createdBy": "Created By", "createdDate": "Created Date", "modifiedBy": "Modified By", "modifiedDate": "Modified Date", "status": "Status", "active": "Active", "inactive": "Inactive", "draft": "Draft", "submitted": "Submitted", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "closed": "Closed", "reopened": "Reopened", "all": "All", "custom": "Custom", "today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "thisQuarter": "This Quarter", "lastQuarter": "Last Quarter", "thisYear": "This Year", "lastYear": "Last Year", "yearToDate": "Year to Date", "quarterToDate": "Quarter to Date", "monthToDate": "Month to Date", "weekToDate": "Week to Date", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly", "comparative": "Comparative", "summary": "Summary", "detailed": "Detailed", "transactional": "Transactional", "standard": "Standard", "expanded": "Expanded", "compact": "Compact"}, "integrations": {"title": "Integrations", "subtitle": "Manage API keys and integrations for your account.", "merchant": {"select": "Select Merchant:", "placeholder": "Select a merchant", "loading": "Loading merchants...", "none": "No merchants available"}, "tabs": {"apiKeys": "API Keys"}, "apiKeys": {"title": "API Keys", "description": "Create and manage API keys to authenticate your API requests.", "selectMerchantPrompt": "Please select a merchant to create API keys", "viewMerchantPrompt": "Please select a merchant to view API keys.", "yourApiKeys": "Your API Keys", "securityNotice": "API keys provide access to the ADC Account API. Keep your API keys secure and never share them publicly.", "list": {"loading": "Loading API keys...", "noKeysFound": "No API keys found. Create your first API key to get started.", "table": {"name": "Name", "prefix": "Prefix", "created": "Created", "expires": "Expires", "status": "Status", "scopes": "<PERSON><PERSON><PERSON>", "actions": "Actions", "never": "Never", "expired": "Expired", "active": "Active", "revoked": "Revoked"}, "actions": {"openMenu": "Open menu", "viewDetails": "View Details", "revoke": "Revoke", "delete": "Delete"}, "dialogs": {"revoke": {"title": "Revoke API Key", "description": "Are you sure you want to revoke this API key? This will immediately invalidate the key and it can no longer be used to authenticate API requests.", "cancel": "Cancel", "confirm": "Revoke"}, "delete": {"title": "Delete API Key", "description": "Are you sure you want to delete this API key? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete"}}, "notifications": {"revokeSuccess": "API key revoked", "revokeSuccessDescription": "The API key \"{name}\" has been revoked.", "revokeFailed": "Failed to revoke API key", "revokeFailedDescription": "An error occurred while revoking the API key.", "deleteSuccess": "API key deleted", "deleteSuccessDescription": "The API key \"{name}\" has been deleted.", "deleteFailed": "Failed to delete API key", "deleteFailedDescription": "An error occurred while deleting the API key."}}}, "documentation": {"title": "API Documentation", "description": "Learn how to use the ADC Account API to integrate with your applications.", "apiReference": {"title": "API Reference", "description": "Comprehensive API reference documentation for all endpoints."}, "authGuide": {"title": "Authentication Guide", "description": "Learn how to authenticate your API requests using API keys."}, "sdkLibraries": {"title": "SDK & Libraries", "description": "Client libraries and SDKs for various programming languages."}, "exampleRequest": {"title": "Example API Request", "code": "curl -X GET https://api.adcaccount.com/v1/customers \n  -H \"Authorization: Bearer YOUR_API_KEY\" \n  -H \"Content-Type: application/json\""}, "endpoints": {"title": "Available Endpoints", "customers": "/api/customers - Manage customers", "invoices": "/api/invoices - Manage invoices", "bills": "/api/bills - Manage bills", "vendors": "/api/vendors - Manage vendors", "coa": "/api/coa - Chart of accounts", "reports": "/api/reports - Financial reports"}, "security": {"title": "Security Best Practices", "practices": {"clientSide": "Never hardcode API keys in client-side code", "envVars": "Store API keys securely in environment variables", "leastPrivilege": "Use the principle of least privilege when assigning scopes", "rotate": "Rotate API keys regularly", "revoke": "Revoke unused or compromised API keys immediately"}}, "viewFull": "View Full Documentation"}}, "exampleFeature": {"title": "Example Feature", "tabs": {"overview": "Overview", "settings": "Settings", "advanced": "Advanced"}, "overview": {"title": "Feature Overview", "description": "This is an example of a completed feature tab.", "content": "This tab is fully implemented and shows content.", "action": "Example Action"}, "settings": {"title": "Settings Coming Soon", "message": "The settings tab is still under development.", "backText": "Go back to Overview"}, "advanced": {"title": "Advanced Features Not Available", "message": "Advanced features will be available in a future update.", "backText": "Go back to Overview"}}, "customNotDone": {"title": "Feature Coming Soon", "message": "This feature is planned for our next release. Stay tuned for updates!", "backText": "Back to Dashboard", "releaseInfo": "Expected release: Q3 2023", "subscribeButton": "Subscribe to Updates"}, "utilityNotDone": {"title": "Development Status", "message": "This feature is currently in development and will be available in our next release."}, "notDoneDoc": {"title": "Not Done Component", "description": "A reusable component for pages or features that are still under development.", "tabs": {"usage": "Usage", "examples": "Examples", "api": "API"}, "usage": {"title": "Basic Usage", "description": "How to use the NotDone component in your pages", "content": "The NotDone component is designed to be used as a placeholder for pages or features that are still under development. It provides a consistent way to communicate to users that a feature is coming soon.", "utilityFunction": "You can also use the utility function to create a not done page:", "alert": {"title": "Internationalization", "description": "The NotDone component is fully internationalized and will automatically use the correct language based on the current locale."}}, "examples": {"title": "Examples", "description": "Different ways to use the NotDone component", "basic": {"title": "Basic Example"}, "custom": {"title": "Custom Title and Message", "featureTitle": "Feature Coming Soon", "featureMessage": "This feature will be available in our next release."}, "additional": {"title": "With Additional Content", "content": "This is an example of additional content in the NotDone component."}}, "api": {"title": "API Reference", "description": "Props and options for the NotDone component", "propsTitle": "NotDone Component Props", "table": {"prop": "Prop", "type": "Type", "default": "<PERSON><PERSON><PERSON>", "description": "Description"}, "props": {"title": "Custom title to override the default", "message": "Custom message to override the default", "backUrl": "URL to navigate to when clicking the back button", "backText": "Custom text for the back button", "children": "Additional content to display below the message"}}}, "customers": {"title": "Customers", "addNew": "Add New Customer", "searchPlaceholder": "Search customers...", "filters": {"filterByStatus": "Filter by status"}, "errors": {"loadingTitle": "Error loading customers", "loadingDescription": "An error occurred: {error}"}, "status": {"all": "All", "active": "Active", "inactive": "Inactive"}, "table": {"title": "Customer List", "customersFound": "{count} {count, plural, one {customer} other {customers}} found", "matching": " matching \"{query}\"", "withStatus": " with status: {status}", "name": "Name", "email": "Email", "phone": "Phone", "status": "Status", "actions": "Actions", "noCustomersFound": "No customers found.", "noCustomersMatchingCriteria": "No customers found matching your criteria.", "addOnePrompt": "Add one using the button above."}, "actions": {"edit": "Edit", "delete": "Delete"}, "dialog": {"add": {"title": "Add New Customer", "description": "Enter the customer details below. Click save when you're done."}, "edit": {"title": "Edit Customer", "description": "Update the customer details below. Click save when you're done."}, "delete": {"title": "Delete Customer", "description": "Are you sure you want to delete this customer? This action cannot be undone."}, "form": {"name": "Name*", "email": "Email*", "phone": "Phone", "taxId": "Tax ID", "address": "Address", "city": "City", "state": "State", "zipCode": "ZIP Code", "country": "Country", "notes": "Notes", "status": "Status", "selectStatus": "Select status"}, "buttons": {"cancel": "Cancel", "create": "Create Customer", "creating": "Creating...", "save": "Save Changes", "saving": "Saving...", "delete": "Delete Customer", "deleting": "Deleting..."}, "messages": {"createSuccess": "Customer created successfully", "updateSuccess": "Customer updated successfully", "deleteSuccess": "Customer deleted successfully", "error": "Error: {message}"}}}, "vendors": {"title": "Vend<PERSON>", "subtitle": "Manage your vendors and suppliers", "addNew": "Add New Vendor", "searchPlaceholder": "Search vendors...", "errors": {"loadingTitle": "Error loading vendors", "loadingDescription": "An error occurred: {error}"}, "table": {"title": "Vendor List", "vendorsFound": "{count} {count, plural, one {vendor} other {vendors}} found", "matching": "matching \"{query}\"", "image": "Image", "name": "Name", "contactPerson": "Contact Person", "email": "Email", "phone": "Phone", "address": "Address", "actions": "Actions", "noVendorsFound": "No vendors found.", "addOnePrompt": "Add one using the button above.", "na": "N/A", "showingVendors": "Showing {showing} of {total} vendors", "page": "Page {current} of {total}"}, "actions": {"edit": "Edit", "delete": "Delete"}, "dialog": {"add": {"title": "Add New Vendor", "description": "Enter the details for the new vendor. Click save when you're done."}, "edit": {"title": "<PERSON>", "description": "Update the vendor details below. Click save when you're done."}, "delete": {"title": "Are you sure?", "description": "This action cannot be undone. This will permanently delete the vendor and remove their data from our servers.", "noVendorSelected": "No vendor selected for deletion."}, "form": {"nameRequired": "Name*", "contact": "Contact Person", "emailRequired": "Email*", "phone": "Phone", "address": "Address"}, "buttons": {"cancel": "Cancel", "save": "Save <PERSON><PERSON>or", "saving": "Saving...", "update": "Save Changes", "updating": "Saving...", "delete": "Delete", "deleting": "Deleting..."}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> created successfully", "updateSuccess": "Vendor updated successfully", "deleteSuccess": "<PERSON><PERSON><PERSON> deleted successfully", "noVendorForEdit": "No vendor selected for editing", "error": "Error: {message}"}}}, "integrationGuide": {"title": "Integration Guide", "description": "Learn how to integrate your applications with the ADC Account API.", "alert": {"title": "Important", "description": "This documentation is for the latest version of the ADC Account API (v1). Make sure you're using the correct version in your integration."}, "tabs": {"overview": "Overview", "authentication": "Authentication", "requests": "Making Requests", "examples": "Examples"}, "overview": {"title": "API Overview", "description": "Understanding the ADC Account API structure and capabilities", "baseUrl": {"title": "Base URL", "description": "All API requests should be made to the following base URL:"}, "resources": {"title": "Available Resources", "description": "The ADC Account API provides access to the following resources:", "customers": "Customers - Manage customer information and relationships", "invoices": "Invoices - Create, update, and manage invoices", "payments": "Payments - Process and track payments", "vendors": "Vendors - Manage vendor information", "bills": "Bills - Create and manage bills", "coa": "Chart of Accounts - Access and manage your chart of accounts", "journalEntries": "Journal Entries - Create and manage journal entries", "reports": "Reports - Generate financial reports"}, "rateLimits": {"title": "Rate Limits", "description": "The API has rate limits to ensure fair usage and system stability:", "standard": "Standard tier: 60 requests per minute", "professional": "Professional tier: 120 requests per minute", "enterprise": "Enterprise tier: 300 requests per minute", "headers": "Rate limit information is included in the response headers:"}}, "authentication": {"title": "Authentication", "description": "How to authenticate your API requests", "apiKeys": {"title": "API Keys", "description": "All requests to the ADC Account API must be authenticated using an API key. You can create and manage API keys in the Integrations section of your account."}, "header": {"title": "Authentication Header", "description": "Include your API key in the Authorization header of your requests:"}, "example": {"title": "Example Request with Authentication"}, "security": {"title": "API Key Security", "description": "Keep your API keys secure and never share them publicly. If you believe an API key has been compromised, revoke it immediately and create a new one."}}, "requests": {"title": "Making Requests", "description": "How to structure your API requests and handle responses", "format": {"title": "Request Format", "description": "All requests should use HTTPS and include the appropriate headers:"}, "response": {"title": "Response Format", "description": "All responses are returned in JSON format. Successful responses will have a 2xx status code, while errors will have a 4xx or 5xx status code."}, "pagination": {"title": "Pagination", "description": "List endpoints support pagination using the page and limit query parameters:", "info": "Pagination information is included in the response:"}, "errors": {"title": "Erro<PERSON>", "description": "Error responses include a message and sometimes additional details:"}}, "examples": {"title": "Code Examples", "description": "Example API requests in various programming languages", "languages": {"curl": "cURL", "javascript": "JavaScript", "python": "Python", "php": "PHP"}, "listCustomers": "List Customers", "createInvoice": "Create Invoice"}}}