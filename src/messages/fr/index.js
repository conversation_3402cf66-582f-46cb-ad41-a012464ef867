import app from './app.json';
import common from './common.json';
import auth from './auth.json';
import dashboard from './dashboard.json';
import settings from './settings.json';
import docs from './docs.json';
import help from './help.json';
import users from './users.json';
import chartOfAccounts from './chartOfAccounts.json';
import chartOfAccountsSetup from './chartOfAccountsSetup.json';
import assets from './assets.json';
import invoices from './invoices.json';
import reports from './reports.json';
import integrations from './integrations.json';
import api from './api.json';
import notDone from './notDone.json';
import banking from './banking.json';
import bills from './bills.json';
import cashflow from './cashflow.json';
import employees from './employees.json';
import payroll from './payroll.json';
import exampleFeature from './exampleFeature.json';
import customNotDone from './customNotDone.json';
import utilityNotDone from './utilityNotDone.json';
import notDoneDoc from './notDoneDoc.json';
import integrationGuide from './integrationGuide.json';
import customers from './customers.json';
import vendors from './vendors.json';
import sidebar from './sidebar.json';
import expenses from './expenses.json';
import journalEntries from './journalEntries.json';
import merchants from './merchants.json';
import auditLogs from './auditLogs.json';
import subscription from './subscription.json';
import pricing from './pricing.json';
import emailTemplates from './emailTemplates.json';
import merchantDetail from './merchantDetail.json';
import merchantUsers from './merchantUsers.json';
import terms from './terms.json';
import accountsReceivableAging from './accounts-receivable-aging.json';
import creditNotes from './creditNotes.json';
import paymentReminders from './paymentReminders.json';
import invoiceDetail from './invoiceDetail.json';
import recurringInvoice from './recurringInvoice.json';
import invoiceTemplate from './invoiceTemplate.json';
import budget from './budget.json';

const messages = {
  app,
  common,
  auth,
  dashboard,
  settings,
  docs,
  help,
  users,
  chartOfAccounts,
  chartOfAccountsSetup,
  assets,
  invoices,
  reports,
  integrations,
  api,
  notDone,
  banking,
  bills,
  cashflow,
  employees,
  payroll,
  exampleFeature,
  customNotDone,
  utilityNotDone,
  notDoneDoc,
  integrationGuide,
  customers,
  vendors,
  sidebar,
  expenses,
  journalEntries,
  merchants,
  auditLogs,
  subscription,
  pricing,
  emailTemplates,
  merchantDetail,
  merchantUsers,
  terms,
  accountsReceivableAging,
  creditNotes,
  paymentReminders,
  invoiceDetail,
  recurringInvoice,
  invoiceTemplate,
  budget
};

export default messages;
