{"title": "คอมโพเนนต์ Not Done", "description": "คอมโพเนนต์ที่สามารถนำกลับมาใช้ได้สำหรับหน้าหรือฟีเจอร์ที่ยังอยู่ระหว่างการพัฒนา", "tabs": {"usage": "การใช้งาน", "examples": "ตัวอย่าง", "api": "API"}, "usage": {"title": "การใช้งานพื้นฐาน", "description": "วิธีการใช้คอมโพเนนต์ NotDone ในหน้าของคุณ", "content": "คอมโพเนนต์ NotDone ถูกออกแบบมาเพื่อใช้เป็นตัวยึดตำแหน่งสำหรับหน้าหรือฟีเจอร์ที่ยังอยู่ระหว่างการพัฒนา มันให้วิธีที่สม่ำเสมอในการสื่อสารกับผู้ใช้ว่าฟีเจอร์กำลังจะมา", "utilityFunction": "คุณยังสามารถใช้ฟังก์ชันยูทิลิตี้เพื่อสร้างหน้า not done:", "alert": {"title": "การแปลภาษา", "description": "คอมโพเนนต์ NotDone รองรับการแปลภาษาอย่างสมบูรณ์และจะใช้ภาษาที่ถูกต้องโดยอัตโนมัติตามโลแคลปัจจุบัน"}}, "examples": {"title": "ตัวอย่าง", "description": "วิธีต่างๆ ในการใช้คอมโพเนนต์ NotDone", "basic": {"title": "ตัวอย่างพื้นฐาน"}, "custom": {"title": "หัวข้อและข้อความที่กำหนดเอง", "featureTitle": "ฟีเจอร์กำลังจะมา", "featureMessage": "ฟีเจอร์นี้จะพร้อมใช้งานในการเปิดตัวครั้งต่อไป"}, "additional": {"title": "พร้อมเนื้อหาเพิ่มเติม", "content": "นี่คือตัวอย่างของเนื้อหาเพิ่มเติมในคอมโพเนนต์ NotDone"}}, "api": {"title": "การอ้างอิง API", "description": "Props และตัวเลือกสำหรับคอมโพเนนต์ NotDone", "propsTitle": "Props ของคอมโพเนนต์ NotDone", "table": {"prop": "Prop", "type": "ประเภท", "default": "ค่าเริ่มต้น", "description": "คำอธิบาย"}, "props": {"title": "หัวข้อที่กำหนดเองเพื่อแทนที่ค่าเริ่มต้น", "message": "ข้อความที่กำหนดเองเพื่อแทนที่ค่าเริ่มต้น", "backUrl": "URL ที่จะนำทางไปเมื่อคลิกปุ่มกลับ", "backText": "ข้อความที่กำหนดเองสำหรับปุ่มกลับ", "children": "เนื้อหาเพิ่มเติมที่จะแสดงด้านล่างข้อความ"}}}