'use client';

import { useBranchContext } from '@/contexts/BranchContext';

/**
 * Hook for managing default organization and branch settings
 * Provides convenient methods for working with user defaults
 */
export function useDefaultOrganizationBranch() {
  const {
    selectedOrganizationId,
    selectedBranchId,
    defaultOrganizationId,
    defaultBranchId,
    isUsingDefaults,
    setAsDefaultOrganization,
    setAsDefaultBranch,
    getSelectedOrganization,
    getSelectedBranch,
    organizations,
  } = useBranchContext();

  /**
   * Check if the current selection matches the user's defaults
   */
  const isCurrentSelectionDefault = () => {
    return (
      selectedOrganizationId === defaultOrganizationId &&
      selectedBranchId === defaultBranchId
    );
  };

  /**
   * Check if the user has any defaults set
   */
  const hasDefaults = () => {
    return defaultOrganizationId !== null || defaultBranchId !== null;
  };

  /**
   * Get the default organization object
   */
  const getDefaultOrganization = () => {
    return defaultOrganizationId 
      ? organizations.find(org => org.id === defaultOrganizationId) || null
      : null;
  };

  /**
   * Get the default branch object
   */
  const getDefaultBranch = () => {
    if (!defaultBranchId) return null;
    
    for (const org of organizations) {
      const branch = org.branches.find(b => b.id === defaultBranchId);
      if (branch) return branch;
    }
    return null;
  };

  /**
   * Set both organization and branch as defaults
   */
  const setAsDefaults = (organizationId: string, branchId?: string) => {
    setAsDefaultOrganization(organizationId);
    if (branchId) {
      setAsDefaultBranch(branchId);
    }
  };

  /**
   * Clear all defaults (remove from localStorage)
   */
  const clearDefaults = () => {
    // This would need to be implemented in the BranchContext
    // For now, we can set defaults to the first available options
    if (organizations.length > 0) {
      const firstOrg = organizations[0];
      setAsDefaultOrganization(firstOrg.id);
      if (firstOrg.branches.length > 0) {
        setAsDefaultBranch(firstOrg.branches[0].id);
      }
    }
  };

  return {
    // Current state
    selectedOrganizationId,
    selectedBranchId,
    defaultOrganizationId,
    defaultBranchId,
    isUsingDefaults,
    
    // Helper functions
    isCurrentSelectionDefault,
    hasDefaults,
    getDefaultOrganization,
    getDefaultBranch,
    getSelectedOrganization,
    getSelectedBranch,
    
    // Actions
    setAsDefaultOrganization,
    setAsDefaultBranch,
    setAsDefaults,
    clearDefaults,
  };
}
