'use client';

import { useState } from 'react';
import { DeleteDialog } from '@/components/ui/delete-dialog';

interface UseDeleteDialogProps {
  title: string;
  description: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  loadingText?: string;
  successMessage?: string;
  errorMessage?: string;
}

interface DeleteDialogState {
  isOpen: boolean;
  item: any;
  onConfirm?: () => Promise<void>;
  onDeleteSuccess?: () => void;
  canDelete?: boolean;
  warningMessage?: string;
  itemDetails?: React.ReactNode;
}

export function useDeleteDialog({
  title,
  description,
  confirmButtonText = 'Delete',
  cancelButtonText = 'Cancel',
  loadingText = 'Deleting...',
  successMessage = 'Item deleted successfully',
  errorMessage = 'Failed to delete item',
}: UseDeleteDialogProps) {
  const [dialogState, setDialogState] = useState<DeleteDialogState>({
    isOpen: false,
    item: null,
  });

  const openDialog = ({
    item,
    onConfirm,
    onDeleteSuccess,
    canDelete = true,
    warningMessage,
    itemDetails,
  }: {
    item: any;
    onConfirm: () => Promise<void>;
    onDeleteSuccess?: () => void;
    canDelete?: boolean;
    warningMessage?: string;
    itemDetails?: React.ReactNode;
  }) => {
    setDialogState({
      isOpen: true,
      item,
      onConfirm,
      onDeleteSuccess,
      canDelete,
      warningMessage,
      itemDetails,
    });
  };

  const closeDialog = () => {
    setDialogState({
      isOpen: false,
      item: null,
    });
  };

  const DeleteDialogComponent = () => (
    <DeleteDialog
      isOpen={dialogState.isOpen}
      onClose={closeDialog}
      onConfirm={dialogState.onConfirm || (() => Promise.resolve())}
      title={title}
      description={description}
      itemDetails={dialogState.itemDetails}
      canDelete={dialogState.canDelete}
      warningMessage={dialogState.warningMessage}
      confirmButtonText={confirmButtonText}
      cancelButtonText={cancelButtonText}
      loadingText={loadingText}
      successMessage={successMessage}
      errorMessage={errorMessage}
      onDeleteSuccess={dialogState.onDeleteSuccess}
    />
  );

  return {
    openDialog,
    closeDialog,
    DeleteDialogComponent,
    isOpen: dialogState.isOpen,
    item: dialogState.item,
  };
}
