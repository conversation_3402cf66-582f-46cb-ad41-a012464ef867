'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useBranchContext } from '@/contexts/BranchContext';

// Permission levels for organizations and branches
export enum OrganizationPermissionLevel {
  Owner = 'Owner',
  Admin = 'Admin',
  Manager = 'Manager',
  Staff = 'Staff',
  ReadOnly = 'ReadOnly'
}

/**
 * Modern permissions hook that works with the organization/branch system
 * This replaces the old merchant-based permission system
 * Now focuses on branch-level permissions with organization fallback
 */
export function useOrganizationPermissions() {
  const { session, isAuthenticated } = useAuth();
  const { selectedOrganizationId, selectedBranchId, organizations, getSelectedBranch } = useBranchContext();

  const [isLoading] = useState(false);
  const [error] = useState<string | null>(null);
  const [permissionLevel, setPermissionLevel] = useState<OrganizationPermissionLevel | null>(null);
  const [branchPermissionLevel, setBranchPermissionLevel] = useState<OrganizationPermissionLevel | null>(null);

  // Get current organization and branch
  const currentOrganization = organizations.find(org => org.id === selectedOrganizationId);
  const currentBranch = getSelectedBranch();

  // Determine permission level based on branch and organization data
  useEffect(() => {
    if (!isAuthenticated) {
      setPermissionLevel(null);
      setBranchPermissionLevel(null);
      return;
    }

    let orgPermission: OrganizationPermissionLevel | null = null;
    let branchPermission: OrganizationPermissionLevel | null = null;

    // Check organization-level permissions
    if (currentOrganization) {
      const userOrgPermission = currentOrganization.userPermissions?.find(
        perm => perm.userId === session?.user?.id
      );
      if (userOrgPermission) {
        orgPermission = userOrgPermission.permissionLevel as OrganizationPermissionLevel;
      }
    }

    // Check branch-level permissions (if branch is selected)
    if (currentBranch) {
      const userBranchPermission = currentBranch.userPermissions?.find(
        perm => perm.userId === session?.user?.id
      );
      if (userBranchPermission) {
        branchPermission = userBranchPermission.permissionLevel as OrganizationPermissionLevel;
      }
    }

    // Set permissions - branch permissions take precedence over organization permissions
    setBranchPermissionLevel(branchPermission);
    setPermissionLevel(branchPermission || orgPermission || OrganizationPermissionLevel.ReadOnly);

  }, [isAuthenticated, currentOrganization, currentBranch, session?.user?.id]);

  /**
   * Check if the user has the required permission level
   */
  const hasPermission = (requiredLevel: OrganizationPermissionLevel): boolean => {
    if (!permissionLevel) return false;

    const permissionLevels = {
      [OrganizationPermissionLevel.Owner]: 5,
      [OrganizationPermissionLevel.Admin]: 4,
      [OrganizationPermissionLevel.Manager]: 3,
      [OrganizationPermissionLevel.Staff]: 2,
      [OrganizationPermissionLevel.ReadOnly]: 1,
    };

    return permissionLevels[permissionLevel] >= permissionLevels[requiredLevel];
  };

  /**
   * Check if the user can create accounts (Staff+)
   */
  const canCreateAccount = (): boolean => {
    return hasPermission(OrganizationPermissionLevel.Staff);
  };

  /**
   * Check if the user can edit accounts (Manager+)
   */
  const canEditAccount = (): boolean => {
    return hasPermission(OrganizationPermissionLevel.Manager);
  };

  /**
   * Check if the user can delete accounts (Admin+)
   */
  const canDeleteAccount = (): boolean => {
    return hasPermission(OrganizationPermissionLevel.Admin);
  };

  /**
   * Check if the user can manage users (Admin+)
   */
  const canManageUsers = (): boolean => {
    return hasPermission(OrganizationPermissionLevel.Admin);
  };

  /**
   * Check if the user can manage organization settings (Owner)
   */
  const canManageOrganization = (): boolean => {
    return hasPermission(OrganizationPermissionLevel.Owner);
  };

  /**
   * Check if the user has any permissions in the current context
   * Now prioritizes branch context over organization context
   */
  const hasAnyPermission = (): boolean => {
    return permissionLevel !== null && isAuthenticated && (!!selectedBranchId || !!selectedOrganizationId);
  };

  /**
   * Check if the user has branch-specific permissions
   */
  const hasBranchPermission = (): boolean => {
    return branchPermissionLevel !== null && isAuthenticated && !!selectedBranchId;
  };

  /**
   * Get the effective permission level (branch takes precedence over organization)
   */
  const getEffectivePermissionLevel = (): OrganizationPermissionLevel | null => {
    return branchPermissionLevel || permissionLevel;
  };

  /**
   * Get permission level display name
   */
  const getPermissionDisplayName = (): string => {
    if (!permissionLevel) return 'No Access';

    switch (permissionLevel) {
      case OrganizationPermissionLevel.Owner:
        return 'Owner';
      case OrganizationPermissionLevel.Admin:
        return 'Administrator';
      case OrganizationPermissionLevel.Manager:
        return 'Manager';
      case OrganizationPermissionLevel.Staff:
        return 'Staff';
      case OrganizationPermissionLevel.ReadOnly:
        return 'Read Only';
      default:
        return 'Unknown';
    }
  };

  /**
   * Get permission requirements for different actions
   */
  const getPermissionRequirements = () => {
    return {
      createAccount: OrganizationPermissionLevel.Staff,
      editAccount: OrganizationPermissionLevel.Manager,
      deleteAccount: OrganizationPermissionLevel.Admin,
      manageUsers: OrganizationPermissionLevel.Admin,
      manageOrganization: OrganizationPermissionLevel.Owner,
    };
  };

  return {
    // Current state
    isLoading,
    error,
    permissionLevel,
    branchPermissionLevel,
    currentOrganization,
    currentBranch,
    selectedOrganizationId,
    selectedBranchId,

    // Permission checks
    hasPermission,
    canCreateAccount,
    canEditAccount,
    canDeleteAccount,
    canManageUsers,
    canManageOrganization,
    hasAnyPermission,
    hasBranchPermission,

    // Utilities
    getPermissionDisplayName,
    getPermissionRequirements,
    getEffectivePermissionLevel,

    // Context info
    isAuthenticated,
    hasOrganizationContext: !!selectedOrganizationId,
    hasBranchContext: !!selectedBranchId,
  };
}
