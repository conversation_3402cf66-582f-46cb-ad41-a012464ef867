import { toast as sonnerToast } from 'sonner';

export interface ToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  duration?: number;
}

export function toast({ title, description, variant = 'default', duration }: ToastProps) {
  const message = title || description || '';
  const fullMessage = title && description ? `${title}: ${description}` : message;

  switch (variant) {
    case 'destructive':
      return sonnerToast.error(fullMessage, { duration });
    case 'success':
      return sonnerToast.success(fullMessage, { duration });
    case 'warning':
      return sonnerToast.warning(fullMessage, { duration });
    default:
      return sonnerToast(fullMessage, { duration });
  }
}

export function useToast() {
  return { toast };
}
