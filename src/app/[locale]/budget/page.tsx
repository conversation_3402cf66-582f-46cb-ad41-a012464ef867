'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { useI18n } from '@/hooks/useI18n';
import { useBranchContext, usePermissionContext } from '@/contexts/BranchContext';
import { useOrganizationPermissions } from '@/hooks/useOrganizationPermissions';
import { useToast } from '@/hooks/use-toast';
import {
  useGetBudgetItemsQuery,
  useCreateBudgetItemMutation,
  useUpdateBudgetItemMutation,
  useDeleteBudgetItemMutation,
  useGetBudgetReportQuery,
  useExportBudgetQuery,
  useImportBudgetMutation,
  useGetBudgetTemplatesQuery,
  useCreateBudgetTemplateMutation,
  useApplyBudgetTemplateMutation,
  useDeleteBudgetTemplateMutation
} from '@/redux/services/budgetApi';
import { BudgetItem } from '@/lib/types';
import { useGetAccountsQuery } from '@/redux/services/chartOfAccountsApi';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Advanced pagination components
import { PaginatedTable, type TableColumn } from '@/components/common';
import { useAdvancedPagination } from '@/hooks';

export default function BudgetPage() {
  // i18n and context
  const t = useTranslations('budget');
  const common = useTranslations('common');
  const { formatCurrency, formatDate } = useI18n();
  const { toast } = useToast();

  // Branch context for permissions
  const { selectedOrganizationId, selectedBranchId, hasPermissionContext } = useBranchContext();
  const { addContextToParams } = usePermissionContext();

  // Check if we have permission context
  const hasContext = hasPermissionContext();

  const {
    canCreateAccount: canCreateBudget,
    canEditAccount: canEditBudget,
    canDeleteAccount: canDeleteBudget,
    isLoading: permissionsLoading
  } = useOrganizationPermissions();

  const [year, setYear] = useState(new Date().getFullYear());
  const [month, setMonth] = useState(new Date().getMonth() + 1); // 1-12
  const [activeTab, setActiveTab] = useState("budget"); // "budget", "templates", "reports"

  // Budget Items and Reports with branch context
  const budgetItemsParams = useMemo(() => {
    const baseParams = { year, month };
    return hasContext ? addContextToParams(baseParams) : baseParams;
  }, [hasContext, addContextToParams, year, month]);

  const budgetReportParams = useMemo(() => {
    const baseParams = { year, month };
    return hasContext ? addContextToParams(baseParams) : baseParams;
  }, [hasContext, addContextToParams, year, month]);

  const { data: budgetItems, isLoading: isLoadingItems } = useGetBudgetItemsQuery(
    budgetItemsParams as { year?: number; month?: number; branchId?: string; organizationId?: string }
  );


  const { data: budgetReport, isLoading: isLoadingReport } = useGetBudgetReportQuery(
    budgetReportParams as { year: number; month?: number; branchId?: string; organizationId?: string }
  );
  const [createBudgetItem, { isLoading: isCreating }] = useCreateBudgetItemMutation();
  const [updateBudgetItem, { isLoading: isUpdating }] = useUpdateBudgetItemMutation();
  const [deleteBudgetItem, { isLoading: isDeleting }] = useDeleteBudgetItemMutation();

  // Chart of Accounts with branch context
  const accountsParams = useMemo(() => {
    return hasContext ? addContextToParams({}) : {};
  }, [hasContext, addContextToParams]);

  const { data: accountsData, isLoading: isLoadingAccounts } = useGetAccountsQuery(
    accountsParams
  );
  const accounts = useMemo(() => accountsData?.data || [], [accountsData]);

  // Budget Templates with branch context
  const templatesParams = useMemo(() => {
    return hasContext ? addContextToParams({}) : {};
  }, [hasContext, addContextToParams]);

  const { data: budgetTemplates, isLoading: isLoadingTemplates } = useGetBudgetTemplatesQuery(
    templatesParams as { branchId?: string; organizationId?: string } | void
  );
  const [createBudgetTemplate, { isLoading: isCreatingTemplate }] = useCreateBudgetTemplateMutation();
  const [applyBudgetTemplate, { isLoading: isApplyingTemplate }] = useApplyBudgetTemplateMutation();
  const [deleteBudgetTemplate] = useDeleteBudgetTemplateMutation();

  // Import/Export
  const [importBudget, { isLoading: isImporting }] = useImportBudgetMutation();

  // Dialog states
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isApplyTemplateDialogOpen, setIsApplyTemplateDialogOpen] = useState(false);
  const [isBudgetItemDialogOpen, setIsBudgetItemDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [selectedBudgetItemId, setSelectedBudgetItemId] = useState<string | null>(null);

  // New budget item state
  const [newItem, setNewItem] = useState({
    account_id: '', // Will be set from chart of accounts
    year,
    month,
    amount: 0,
    notes: '',
  });

  // Edit budget item state
  const [editItem, setEditItem] = useState({
    id: '',
    account_id: '',
    year,
    month,
    amount: 0,
    notes: '',
  });

  // New template state
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    isDefault: false,
    items: [] as { accountId: string; amount: number; notes?: string }[],
  });

  // Import data state
  const [importData, setImportData] = useState('');

  // Error states
  const [error, setError] = useState<React.ReactNode | null>(null);
  const [templateError, setTemplateError] = useState<string | null>(null);
  const [importError, setImportError] = useState<string | null>(null);

  // Advanced pagination for budget items
  const { state, actions } = useAdvancedPagination({
    initialSort: {
      sortBy: 'created_at',
      sortOrder: 'desc'
    },
    initialLimit: 10,
    persist: true,
    persistKey: 'budget-items'
  });

  // Budget items table columns
  const budgetItemsColumns: TableColumn<BudgetItem>[] = useMemo(() => [
    {
      key: 'account',
      header: 'Account',
      sortable: true,
      render: (_, item) => {
        const account = accounts.find(a => a.id === item.account_id);

        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {account?.name || `Unknown Account (${item.account_id})`}
            </span>
            {account?.code && (
              <span className="text-sm text-muted-foreground">
                {account.code}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'period',
      header: 'Period',
      sortable: true,
      render: (_, item) => (
        <div className="flex flex-col">
          <span className="font-medium">
            {new Date(item.year, item.month - 1).toLocaleDateString('default', {
              month: 'long',
              year: 'numeric'
            })}
          </span>
          <span className="text-sm text-muted-foreground">
            {item.year}-{item.month.toString().padStart(2, '0')}
          </span>
        </div>
      ),
    },
    {
      key: 'amount',
      header: 'Budgeted Amount',
      sortable: true,
      render: (_, item) => (
        <span className="font-mono font-medium">
          {formatCurrency(item.amount)}
        </span>
      ),
    },
    {
      key: 'notes',
      header: 'Notes',
      render: (_, item) => (
        <span className="text-sm text-muted-foreground max-w-xs truncate">
          {item.notes || '-'}
        </span>
      ),
    },
    {
      key: 'created_at',
      header: 'Created',
      sortable: true,
      render: (_, item) => (
        <span className="text-sm text-muted-foreground">
          {formatDate(item.created_at || item.createdAt || '')}
        </span>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, item) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditItem(item)}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-red-500 hover:text-red-700"
            onClick={() => handleDeleteItem(item.id)}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ], [accounts, formatCurrency, formatDate]);

  // Calculate pagination info for budget items
  const budgetItemsPaginationInfo = useMemo(() => {
    const items = budgetItems || [];
    const total = items.length;
    const totalPages = Math.ceil(total / state.limit);
    return {
      total,
      page: state.page,
      limit: state.limit,
      totalPages,
      hasNextPage: state.page < totalPages,
      hasPreviousPage: state.page > 1,
    };
  }, [budgetItems, state.page, state.limit]);

  // Helper function to check if a budget item already exists
  const checkForExistingBudgetItem = useCallback((item: typeof newItem) => {
    if (!budgetItems) return;

    const existingItem = budgetItems.find(
      budgetItem =>
        budgetItem.account_id === item.account_id &&
        budgetItem.year === item.year &&
        budgetItem.month === item.month
    );

    if (existingItem) {
      // Find account name from accounts data
      const account = accounts?.find((a: any) => a.id === item.account_id);
      const accountName = account ? account.account_name : item.account_id;

      const monthName = new Date(2000, item.month - 1, 1).toLocaleString('default', { month: 'long' });

      setError(
        <div className="text-amber-500 mt-2">
          Warning: A budget item already exists for <strong>{accountName}</strong> in <strong>{monthName} {item.year}</strong>.
          Submitting will result in an error.
        </div>
      );
    } else {
      setError(null);
    }
  }, [budgetItems, accounts]);

  // Check for existing budget items when component mounts or when budgetItems changes
  useEffect(() => {
    if (budgetItems) {
      checkForExistingBudgetItem(newItem);
    }
  }, [budgetItems, newItem, checkForExistingBudgetItem]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Skip validation for notes field
    if (name === 'notes') {
      setNewItem({
        ...newItem,
        notes: value,
      });
      return;
    }

    const updatedItem = {
      ...newItem,
      [name]: name === 'amount' ? parseFloat(value) :
              (name === 'year' || name === 'month') ? parseInt(value) : value,
    };

    setNewItem(updatedItem);

    // Only check for existing items if changing account, year, or month
    if (name === 'accountId' || name === 'year' || name === 'month') {
      checkForExistingBudgetItem(updatedItem);
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    console.log(`Changing ${name} to ${value}`); // Debug log

    const updatedItem = {
      ...newItem,
      [name === 'accountId' ? 'account_id' : name]: name === 'year' || name === 'month' ? parseInt(value) : value,
    };

    console.log('Updated item:', updatedItem); // Debug log

    setNewItem(updatedItem);

    // Check if a budget item already exists for this combination
    checkForExistingBudgetItem(updatedItem);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      // Transform the data to match backend expectations
      const budgetItemData = {
        accountId: newItem.account_id, // Transform account_id to accountId
        year: newItem.year,
        month: newItem.month,
        amount: newItem.amount,
        notes: newItem.notes || undefined,
        branchId: selectedBranchId || undefined, // Use the branch ID from context
      };

      await createBudgetItem(budgetItemData).unwrap();

      // Reset form and close dialog
      setNewItem({
        ...newItem,
        amount: 0,
        notes: '',
      });
      setIsBudgetItemDialogOpen(false);
    } catch (err: unknown) {
      const error = err as { status?: number; message?: string; data?: { message?: string } };
      console.error('Failed to create budget item:', err);

      // Check if it's a duplicate budget item error (409 Conflict)
      if (error.status === 409) {
        // Find account name from accounts data
        const account = accounts?.find((a: any) => a.id === newItem.account_id);
        const accountName = account ? account.account_name : newItem.account_id;

        const monthName = new Date(2000, newItem.month - 1, 1).toLocaleString('default', { month: 'long' });

        setError(
          <div className="text-red-500 mt-2">
            A budget item already exists for <strong>{accountName}</strong> in <strong>{monthName} {newItem.year}</strong>.
            Please edit the existing item instead.
          </div>
        );
      } else {
        setError(
          <div className="text-red-500 mt-2">
            {error.data?.message || error.message || 'Failed to create budget item'}
          </div>
        );
      }
    }
  };

  // Function to handle export
  const handleExport = () => {
    // Create a URL to the export endpoint
    const exportUrl = `/api/budget/export?year=${year}&month=${month}&format=csv`;

    // Open the URL in a new tab
    window.open(exportUrl, '_blank');
  };

  // Function to handle template creation
  const handleCreateTemplate = async (e: React.FormEvent) => {
    e.preventDefault();
    setTemplateError(null);

    if (!budgetItems || budgetItems.length === 0) {
      setTemplateError("No budget items to create template from");
      return;
    }

    if (!newTemplate.name) {
      setTemplateError("Template name is required");
      return;
    }

    try {
      // Convert budget items to template items
      const templateItems = budgetItems.map(item => ({
        accountId: item.account_id, // Map to the expected API format
        amount: item.amount,
        notes: item.notes,
      }));

      // Create the template
      await createBudgetTemplate({
        name: newTemplate.name,
        description: newTemplate.description,
        isDefault: newTemplate.isDefault,
        items: templateItems,
      }).unwrap();

      // Reset form and close dialog
      setNewTemplate({
        name: '',
        description: '',
        isDefault: false,
        items: [],
      });
      setIsTemplateDialogOpen(false);
    } catch (err) {
      console.error('Failed to create template:', err);
      setTemplateError('Failed to create template');
    }
  };

  // Function to handle editing a budget item
  const handleEditItem = (item: BudgetItem) => {
    setEditItem({
      id: item.id,
      account_id: item.account_id,
      year: item.year,
      month: item.month,
      amount: item.amount,
      notes: item.notes || '',
    });
    setIsEditDialogOpen(true);
  };

  // Function to handle updating a budget item
  const handleUpdateItem = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      // Transform the data to match backend expectations
      const updateData = {
        accountId: editItem.account_id, // Transform account_id to accountId
        year: editItem.year,
        month: editItem.month,
        amount: editItem.amount,
        notes: editItem.notes || undefined,
      };

      await updateBudgetItem({
        id: editItem.id,
        body: updateData
      }).unwrap();

      setIsEditDialogOpen(false);
    } catch (err: unknown) {
      const error = err as { status?: number; message?: string; data?: { message?: string } };
      console.error('Failed to update budget item:', err);

      if (error.status === 409) {
        // Find account name from accounts data
        const account = accounts?.find((a: any) => a.id === editItem.account_id);
        const accountName = account ? account.account_name : editItem.account_id;

        const monthName = new Date(2000, editItem.month - 1, 1).toLocaleString('default', { month: 'long' });

        setError(
          <div className="text-red-500 mt-2">
            A budget item already exists for <strong>{accountName}</strong> in <strong>{monthName} {editItem.year}</strong>.
            Please edit the existing item instead.
          </div>
        );
      } else {
        setError(
          <div className="text-red-500 mt-2">
            {error.data?.message || error.message || 'Failed to update budget item'}
          </div>
        );
      }
    }
  };

  // Function to handle deleting a budget item
  const handleDeleteItem = (id: string) => {
    setSelectedBudgetItemId(id);
    setIsDeleteDialogOpen(true);
  };

  // Function to confirm deletion of a budget item
  const confirmDeleteItem = async () => {
    if (!selectedBudgetItemId) return;

    try {
      await deleteBudgetItem(selectedBudgetItemId).unwrap();
      setIsDeleteDialogOpen(false);
      setSelectedBudgetItemId(null);
    } catch (err) {
      console.error('Failed to delete budget item:', err);
      setError(
        <div className="text-red-500 mt-2">
          Failed to delete budget item
        </div>
      );
    }
  };

  // Function to handle template application
  const handleApplyTemplate = async () => {
    if (!selectedTemplateId) {
      return;
    }

    try {
      await applyBudgetTemplate({
        id: selectedTemplateId,
        year,
        month,
      }).unwrap();

      setIsApplyTemplateDialogOpen(false);
      setSelectedTemplateId(null);
    } catch (err) {
      console.error('Failed to apply template:', err);
    }
  };

  // Function to handle import
  const handleImport = async () => {
    setImportError(null);

    if (!importData) {
      setImportError('No data to import');
      return;
    }

    try {
      // Parse the CSV or JSON data
      let parsedData;
      try {
        // Try parsing as JSON first
        parsedData = JSON.parse(importData);
      } catch {
        // If JSON parsing fails, try parsing as CSV
        // This is a simple CSV parser, you might want to use a library for more complex CSVs
        const lines = importData.trim().split('\n');
        const headers = lines[0].split(',');

        parsedData = lines.slice(1).map(line => {
          const values = line.split(',');
          const item: {
            accountId?: string;
            year?: number;
            month?: number;
            amount?: number;
            notes?: string;
          } = {};

          headers.forEach((header, index) => {
            const value = values[index];
            if (header.toLowerCase().includes('amount')) {
              item.amount = parseFloat(value);
            } else if (header.toLowerCase().includes('account')) {
              item.accountId = value;
            } else if (header.toLowerCase().includes('year')) {
              item.year = parseInt(value);
            } else if (header.toLowerCase().includes('month')) {
              item.month = parseInt(value);
            } else if (header.toLowerCase().includes('notes')) {
              item.notes = value;
            }
          });

          return item;
        });
      }

      // Import the data
      await importBudget(parsedData).unwrap();

      // Reset form and close dialog
      setImportData('');
      setIsImportDialogOpen(false);
    } catch (err) {
      console.error('Failed to import data:', err);
      setImportError('Failed to import data');
    }
  };

  // Function to get chart data from budget report
  const getChartData = () => {
    if (!budgetReport || !budgetReport.items || !Array.isArray(budgetReport.items)) return [];

    // Calculate totals from items since the backend structure is different
    const totals = budgetReport.items.reduce((acc, item) => {
      acc.budgeted += Number(item.budgetedAmount || 0);
      acc.actual += Number(item.actualAmount || 0);
      return acc;
    }, { budgeted: 0, actual: 0 });

    return [
      {
        name: 'Total Budget',
        budgeted: totals.budgeted,
        actual: totals.actual,
      },
    ];
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">{t('title')}</h1>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="budget">{t('tabs.budget')}</TabsTrigger>
          <TabsTrigger value="templates">{t('tabs.templates')}</TabsTrigger>
          <TabsTrigger value="reports">{t('tabs.reports')}</TabsTrigger>
        </TabsList>

        <TabsContent value="budget" className="space-y-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">{t('periodSelection.title')}</h2>
            <div className="flex space-x-4 mb-4">
              <div className="w-32">
                <Label htmlFor="year-select" className="mb-2">{t('periodSelection.year')}</Label>
                <Select
                  value={year.toString()}
                  onValueChange={(value) => setYear(parseInt(value))}
                >
                  <SelectTrigger id="year-select">
                    <SelectValue placeholder={t('periodSelection.selectYear')} />
                  </SelectTrigger>
                  <SelectContent>
                    {[2024, 2025, 2026].map(y => (
                      <SelectItem key={y} value={y.toString()}>
                        {y}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="w-40">
                <Label htmlFor="month-select" className="mb-2">{t('periodSelection.month')}</Label>
                <Select
                  value={month.toString()}
                  onValueChange={(value) => setMonth(parseInt(value))}
                >
                  <SelectTrigger id="month-select">
                    <SelectValue placeholder={t('periodSelection.selectMonth')} />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(m => (
                      <SelectItem key={m} value={m.toString()}>
                        {new Date(2000, m - 1, 1).toLocaleString('default', { month: 'long' })}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleExport}>
                {t('export.button')}
              </Button>

              <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">Import Budget</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Import Budget Data</DialogTitle>
                    <DialogDescription>
                      Paste CSV or JSON data to import budget items.
                    </DialogDescription>
                  </DialogHeader>

                  <div className="space-y-4">
                    <Textarea
                      value={importData}
                      onChange={(e) => setImportData(e.target.value)}
                      rows={10}
                      placeholder="Paste your CSV or JSON data here..."
                    />

                    {importError && (
                      <div className="text-red-500">{importError}</div>
                    )}
                  </div>

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleImport} disabled={isImporting}>
                      {isImporting ? 'Importing...' : 'Import'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {budgetItems && budgetItems.length > 0 && (
                <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline">Save as Template</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Save as Budget Template</DialogTitle>
                      <DialogDescription>
                        Create a reusable template from the current budget items.
                      </DialogDescription>
                    </DialogHeader>

                    <form onSubmit={handleCreateTemplate} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="template-name">Template Name</Label>
                        <Input
                          id="template-name"
                          value={newTemplate.name}
                          onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="template-description">Description (Optional)</Label>
                        <Textarea
                          id="template-description"
                          value={newTemplate.description}
                          onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
                          rows={3}
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="template-default"
                          checked={newTemplate.isDefault}
                          onChange={(e) => setNewTemplate({...newTemplate, isDefault: e.target.checked})}
                        />
                        <Label htmlFor="template-default">Set as default template</Label>
                      </div>

                      {templateError && (
                        <div className="text-red-500">{templateError}</div>
                      )}

                      <DialogFooter>
                        <Button variant="outline" type="button" onClick={() => setIsTemplateDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button type="submit" disabled={isCreatingTemplate}>
                          {isCreatingTemplate ? 'Saving...' : 'Save Template'}
                        </Button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Budget Items</h2>
              <Dialog open={isBudgetItemDialogOpen} onOpenChange={setIsBudgetItemDialogOpen}>
                <DialogTrigger asChild>
                  <Button>Add Budget Item</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Budget Item</DialogTitle>
                    <DialogDescription>
                      Create a new budget item for {new Date(2000, month - 1, 1).toLocaleString('default', { month: 'long' })} {year}.
                    </DialogDescription>
                  </DialogHeader>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="account-select">Account</Label>
                      <Select
                        value={newItem.account_id}
                        onValueChange={(value) => handleSelectChange('account_id', value)}
                        required
                      >
                        <SelectTrigger id="account-select">
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingAccounts ? (
                            <SelectItem value="" disabled>Loading accounts...</SelectItem>
                          ) : accounts && accounts.length > 0 ? (
                            accounts.map((account: any) => (
                              <SelectItem key={account.id} value={account.id}>
                                {account.name || account.account_name} {account.code && `(${account.code})`}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="" disabled>No accounts available</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="amount-input">Amount</Label>
                      <Input
                        id="amount-input"
                        type="number"
                        name="amount"
                        value={newItem.amount}
                        onChange={handleInputChange}
                        required
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="notes-textarea">Notes</Label>
                      <Textarea
                        id="notes-textarea"
                        name="notes"
                        value={newItem.notes}
                        onChange={handleInputChange}
                        rows={3}
                      />
                    </div>

                    {error && <div className="mb-4">{error}</div>}

                    <DialogFooter>
                      <Button variant="outline" type="button" onClick={() => setIsBudgetItemDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isCreating}>
                        {isCreating ? 'Adding...' : 'Add Budget Item'}
                      </Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>

              {/* Edit Budget Item Dialog */}
              <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Edit Budget Item</DialogTitle>
                    <DialogDescription>
                      Update budget item for {new Date(2000, editItem.month - 1, 1).toLocaleString('default', { month: 'long' })} {editItem.year}.
                    </DialogDescription>
                  </DialogHeader>

                  <form onSubmit={handleUpdateItem} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-account-select">Account</Label>
                      <Select
                        value={editItem.account_id}
                        onValueChange={(value) => setEditItem({...editItem, account_id: value})}
                        required
                      >
                        <SelectTrigger id="edit-account-select">
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingAccounts ? (
                            <SelectItem value="" disabled>Loading accounts...</SelectItem>
                          ) : accounts && accounts.length > 0 ? (
                            accounts.map((account: any) => (
                              <SelectItem key={account.id} value={account.id}>
                                {account.name || account.account_name} {account.code && `(${account.code})`}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="" disabled>No accounts available</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-amount-input">Amount</Label>
                      <Input
                        id="edit-amount-input"
                        type="number"
                        value={editItem.amount}
                        onChange={(e) => setEditItem({...editItem, amount: parseFloat(e.target.value)})}
                        required
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-notes-textarea">Notes</Label>
                      <Textarea
                        id="edit-notes-textarea"
                        value={editItem.notes}
                        onChange={(e) => setEditItem({...editItem, notes: e.target.value})}
                        rows={3}
                      />
                    </div>

                    {error && <div className="mb-4">{error}</div>}

                    <DialogFooter>
                      <Button variant="outline" type="button" onClick={() => setIsEditDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isUpdating}>
                        {isUpdating ? 'Updating...' : 'Update Budget Item'}
                      </Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>

              {/* Delete Confirmation Dialog */}
              <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Delete Budget Item</DialogTitle>
                    <DialogDescription>
                      Are you sure you want to delete this budget item? This action cannot be undone.
                    </DialogDescription>
                  </DialogHeader>

                  <div className="py-4">
                    <p className="text-amber-600">This will permanently remove the budget item from your records.</p>
                  </div>

                  {error && <div className="mb-4">{error}</div>}

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={confirmDeleteItem}
                      disabled={isDeleting}
                    >
                      {isDeleting ? 'Deleting...' : 'Delete Budget Item'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Quick Status */}
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm">
              📊 <strong>{budgetItems?.length || 0}</strong> budget items •
              🏦 <strong>{accounts?.length || 0}</strong> accounts available
              {budgetItems?.length === 0 && accounts?.length > 0 && (
                <span className="ml-2 text-blue-600">
                  → Click &quot;Add Budget Item&quot; to create your first budget entry
                </span>
              )}
            </p>
          </div>

          {/* Advanced Budget Items Table */}
          <PaginatedTable
            data={budgetItems || []}
            columns={budgetItemsColumns}
            paginationInfo={budgetItemsPaginationInfo}
            isLoading={isLoadingItems}

            // Search functionality
            searchable
            searchPlaceholder="Search budget items..."
            searchValue={state.search}
            onSearchChange={actions.handleSearch}

            // Sorting
            sortBy={state.sortBy}
            sortOrder={state.sortOrder}
            onSortChange={actions.handleSort}

            // Pagination
            currentPage={state.page}
            pageSize={state.limit}
            onPageChange={actions.setPage}
            onPageSizeChange={actions.setLimit}

            // Display options
            title={`Budget Items for ${new Date(year, month - 1).toLocaleDateString('default', { month: 'long', year: 'numeric' })}`}
            emptyMessage="No budget items found for this period"

            // Row interactions
            onRowClick={(item) => handleEditItem(item)}
            getRowId={(item) => item.id}
          />
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Budget Templates</h2>
            <p className="mb-4">Create and manage reusable budget templates.</p>

            {isLoadingTemplates ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardHeader>
                      <div className="h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                      <div className="h-4 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </CardContent>
                    <CardFooter className="bg-gray-50 flex justify-between">
                      <div className="h-9 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      <div className="h-9 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : budgetTemplates && budgetTemplates.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {budgetTemplates.map(template => (
                  <Card key={template.id} className="overflow-hidden">
                    <CardHeader>
                      <CardTitle>{template.name}</CardTitle>
                      <CardDescription>{template.description || 'No description'}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">Items: {template.itemCount || template.items?.length || 0}</p>
                      {template.isDefault && (
                        <p className="text-sm text-green-600 font-semibold mt-1">Default Template</p>
                      )}
                    </CardContent>
                    <CardFooter className="bg-gray-50 flex justify-between">
                      <Dialog open={isApplyTemplateDialogOpen && selectedTemplateId === template.id}
                              onOpenChange={(open) => {
                                setIsApplyTemplateDialogOpen(open);
                                if (!open) setSelectedTemplateId(null);
                              }}>
                        <DialogTrigger asChild>
                          <Button variant="outline" onClick={() => setSelectedTemplateId(template.id)}>
                            Apply
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Apply Budget Template</DialogTitle>
                            <DialogDescription>
                              Apply this template to the selected period?
                            </DialogDescription>
                          </DialogHeader>

                          <div className="py-4">
                            <p>This will create or update budget items for {new Date(2000, month - 1, 1).toLocaleString('default', { month: 'long' })} {year}.</p>
                            <p className="mt-2 text-amber-600">Existing budget items for the same accounts will be updated.</p>
                          </div>

                          <DialogFooter>
                            <Button variant="outline" onClick={() => {
                              setIsApplyTemplateDialogOpen(false);
                              setSelectedTemplateId(null);
                            }}>
                              Cancel
                            </Button>
                            <Button onClick={handleApplyTemplate} disabled={isApplyingTemplate}>
                              {isApplyingTemplate ? 'Applying...' : 'Apply Template'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>

                      <Button variant="ghost" onClick={() => {
                        if (confirm('Are you sure you want to delete this template?')) {
                          deleteBudgetTemplate(template.id);
                        }
                      }}>
                        Delete
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center p-8 border rounded-md">
                <p className="mb-4">No templates found.</p>
                <p className="text-sm text-gray-500">Create a template from your current budget items by clicking &quot;Save as Template&quot; on the Budget Items tab.</p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-2">Budget Report</h2>
            {isLoadingReport ? (
              <>
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2">Budget vs. Actual</h3>
                  <div className="h-80 w-full bg-gray-100 dark:bg-gray-800 rounded-md animate-pulse flex items-center justify-center">
                    <div className="text-gray-400 dark:text-gray-600">Loading chart...</div>
                  </div>
                </div>

                <div className="overflow-x-auto border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Category</TableHead>
                        <TableHead>Budgeted</TableHead>
                        <TableHead>Actual</TableHead>
                        <TableHead>Variance</TableHead>
                        <TableHead>Variance %</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Array.from({ length: 3 }).map((_, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell>
                            <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell>
                            <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell>
                            <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell>
                            <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-2">Account Details</h3>
                  <div className="overflow-x-auto border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Account</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Budgeted</TableHead>
                          <TableHead>Actual</TableHead>
                          <TableHead>Variance</TableHead>
                          <TableHead>Variance %</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {Array.from({ length: 5 }).map((_, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                            </TableCell>
                            <TableCell>
                              <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                            </TableCell>
                            <TableCell>
                              <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                            </TableCell>
                            <TableCell>
                              <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                            </TableCell>
                            <TableCell>
                              <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                            </TableCell>
                            <TableCell>
                              <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </>
            ) : budgetReport ? (
              <>
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2">Budget vs. Actual</h3>
                  <div className="h-80 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={getChartData()} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip formatter={(value) => typeof value === 'number' ? `$${value.toFixed(2)}` : value} />
                        <Legend />
                        <Bar dataKey="budgeted" fill="#8884d8" name="Budgeted" />
                        <Bar dataKey="actual" fill="#82ca9d" name="Actual" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                <div className="overflow-x-auto border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Summary</TableHead>
                        <TableHead>Budgeted</TableHead>
                        <TableHead>Actual</TableHead>
                        <TableHead>Variance</TableHead>
                        <TableHead>Variance %</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow className="font-bold">
                        <TableCell>Total</TableCell>
                        <TableCell>${Number(budgetReport?.totalBudgeted || 0).toFixed(2)}</TableCell>
                        <TableCell>${Number(budgetReport?.totalActual || 0).toFixed(2)}</TableCell>
                        <TableCell>${Number(budgetReport?.totalVariance || 0).toFixed(2)}</TableCell>
                        <TableCell>{Number(budgetReport?.totalVariancePercent || 0).toFixed(2)}%</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-2">Account Details</h3>
                  <div className="overflow-x-auto border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Account</TableHead>
                          <TableHead>Code</TableHead>
                          <TableHead>Budgeted</TableHead>
                          <TableHead>Actual</TableHead>
                          <TableHead>Variance</TableHead>
                          <TableHead>Variance %</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {budgetReport?.items && Array.isArray(budgetReport.items) ? (
                          budgetReport.items.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell>{item.accountName}</TableCell>
                              <TableCell>{item.accountCode}</TableCell>
                              <TableCell>${Number(item.budgetedAmount || 0).toFixed(2)}</TableCell>
                              <TableCell>${Number(item.actualAmount || 0).toFixed(2)}</TableCell>
                              <TableCell>${Number(item.variance || 0).toFixed(2)}</TableCell>
                              <TableCell>{Number(item.variancePercent || 0).toFixed(2)}%</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center text-muted-foreground">
                              {isLoadingReport ? 'Loading report data...' : 'No report data available'}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center p-8 border rounded-md">
                <p className="mb-4">No budget report data available.</p>
                <p className="text-sm text-gray-500">Create budget items for this period to generate a report.</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
