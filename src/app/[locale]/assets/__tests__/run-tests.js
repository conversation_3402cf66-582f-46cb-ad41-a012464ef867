#!/usr/bin/env node

// Simple test runner to verify our tests work
const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Running Assets Tests...\n');

const testFiles = [
  'page.test.tsx',
  'asset-list-client.test.tsx',
  'assets-api.test.tsx',
];

let passedTests = 0;
let totalTests = testFiles.length;

testFiles.forEach((testFile, index) => {
  try {
    console.log(`${index + 1}. Testing ${testFile}...`);
    
    // Check if file exists
    const fs = require('fs');
    const testPath = path.join(__dirname, testFile);
    
    if (!fs.existsSync(testPath)) {
      console.log(`   ❌ File not found: ${testFile}`);
      return;
    }
    
    // Basic syntax check
    const content = fs.readFileSync(testPath, 'utf8');
    
    // Check for basic test structure
    if (!content.includes('describe(') || !content.includes('it(')) {
      console.log(`   ❌ Invalid test structure in ${testFile}`);
      return;
    }
    
    // Check for imports
    if (!content.includes('import')) {
      console.log(`   ❌ Missing imports in ${testFile}`);
      return;
    }
    
    console.log(`   ✅ ${testFile} - Basic structure valid`);
    passedTests++;
    
  } catch (error) {
    console.log(`   ❌ Error in ${testFile}: ${error.message}`);
  }
});

console.log(`\n📊 Test Summary:`);
console.log(`   Passed: ${passedTests}/${totalTests}`);
console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 All tests have valid structure!');
  process.exit(0);
} else {
  console.log('\n❌ Some tests have issues.');
  process.exit(1);
}
