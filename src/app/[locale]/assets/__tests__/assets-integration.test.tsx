import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'

describe('Assets Page Integration Tests', () => {
  let browser: Browser
  let page: Page
  const baseUrl = process.env.TEST_BASE_URL || 'http://localhost:3001'

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: process.env.CI === 'true',
      slowMo: process.env.CI === 'true' ? 0 : 50,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    })
  })

  afterAll(async () => {
    if (browser) {
      await browser.close()
    }
  })

  beforeEach(async () => {
    page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })
    
    // Mock console to reduce noise in tests
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.error('Page error:', msg.text())
      }
    })
  })

  afterEach(async () => {
    if (page) {
      await page.close()
    }
  })

  describe('Assets List Page', () => {
    it('should load the assets page successfully', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      
      // Wait for the page to load
      await page.waitForSelector('[data-testid="asset-management-client"]', { timeout: 10000 })
      
      // Check if the page title is present
      const title = await page.$eval('h1', el => el.textContent)
      expect(title).toContain('Asset')
    })

    it('should display filters section', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Check for filters
      const searchInput = await page.$('input[placeholder*="search"]')
      expect(searchInput).toBeTruthy()
      
      // Check for status filter
      const statusFilter = await page.$('[data-testid="select"]')
      expect(statusFilter).toBeTruthy()
    })

    it('should handle search functionality', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Find search input
      const searchInput = await page.$('input[placeholder*="search"]')
      if (searchInput) {
        await searchInput.type('Test Asset')
        
        // Wait for search to be processed
        await page.waitForTimeout(500)
        
        // Verify search input has value
        const searchValue = await page.$eval('input[placeholder*="search"]', el => (el as HTMLInputElement).value)
        expect(searchValue).toBe('Test Asset')
      }
    })

    it('should toggle between grid and list views', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Look for view toggle button
      const viewToggleButton = await page.$('button:has-text("Grid"), button:has-text("List")')
      if (viewToggleButton) {
        await viewToggleButton.click()
        await page.waitForTimeout(300)
        
        // Verify the view has changed
        const buttonText = await viewToggleButton.textContent()
        expect(buttonText).toBeTruthy()
      }
    })

    it('should display asset cards or table rows', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Wait for assets to load (either cards or table)
      try {
        await page.waitForSelector('[data-testid="asset-card"], table tbody tr', { timeout: 5000 })
        
        // Check if we have either asset cards or table rows
        const assetCards = await page.$$('[data-testid="asset-card"]')
        const tableRows = await page.$$('table tbody tr')
        
        expect(assetCards.length > 0 || tableRows.length > 0).toBe(true)
      } catch (error) {
        // If no assets are found, check for empty state
        const emptyMessage = await page.$('text="No assets found"')
        expect(emptyMessage).toBeTruthy()
      }
    })

    it('should handle pagination when multiple pages exist', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Look for pagination controls
      const paginationNext = await page.$('button:has-text("Next"), button:has-text("next")')
      const paginationPrev = await page.$('button:has-text("Previous"), button:has-text("previous")')
      
      // If pagination exists, test it
      if (paginationNext || paginationPrev) {
        if (paginationNext && !(await paginationNext.isDisabled())) {
          await paginationNext.click()
          await page.waitForTimeout(500)
        }
      }
      
      // Test passes if no errors occur
      expect(true).toBe(true)
    })

    it('should open batch action dialog', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Look for batch action bar
      const batchActionBar = await page.$('[data-testid="batch-action-bar"]')
      if (batchActionBar) {
        // Try to find and click a batch action button
        const statusButton = await page.$('button:has-text("Status"), button:has-text("Update Status")')
        if (statusButton) {
          await statusButton.click()
          
          // Wait for dialog to appear
          await page.waitForSelector('[data-testid="dialog"]', { timeout: 2000 })
          
          const dialog = await page.$('[data-testid="dialog"]')
          expect(dialog).toBeTruthy()
        }
      }
    })

    it('should handle sorting by clicking column headers', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Look for sortable column headers
      const nameHeader = await page.$('th:has-text("Name"), th:has-text("name")')
      if (nameHeader) {
        await nameHeader.click()
        await page.waitForTimeout(300)
        
        // Verify sorting indicator appears
        const headerText = await nameHeader.textContent()
        expect(headerText).toBeTruthy()
      }
    })

    it('should handle date range filters', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Look for date picker inputs
      const datePickers = await page.$$('[data-testid="date-picker"], input[type="date"]')
      if (datePickers.length >= 2) {
        // Set date range
        await datePickers[0].type('2023-01-01')
        await datePickers[1].type('2023-12-31')
        
        await page.waitForTimeout(500)
        
        // Verify dates were set
        const fromDate = await page.$eval('[data-testid="date-picker"]:first-of-type, input[type="date"]:first-of-type', 
          el => (el as HTMLInputElement).value)
        expect(fromDate).toBe('2023-01-01')
      }
    })

    it('should clear filters when clear button is clicked', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Set some filters first
      const searchInput = await page.$('input[placeholder*="search"]')
      if (searchInput) {
        await searchInput.type('test')
      }
      
      // Click clear button
      const clearButton = await page.$('button:has-text("Clear"), button:has-text("clear")')
      if (clearButton) {
        await clearButton.click()
        await page.waitForTimeout(300)
        
        // Verify search input is cleared
        const searchValue = await page.$eval('input[placeholder*="search"]', 
          el => (el as HTMLInputElement).value)
        expect(searchValue).toBe('')
      }
    })

    it('should navigate to asset details when clicking view action', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Look for view action links
      const viewLink = await page.$('a:has-text("View"), a:has-text("view")')
      if (viewLink) {
        const href = await viewLink.getAttribute('href')
        expect(href).toContain('/assets/')
      }
    })

    it('should handle page size changes', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Look for page size selector
      const pageSizeSelect = await page.$('select, [data-testid="select"]')
      if (pageSizeSelect) {
        // Try to change page size
        await pageSizeSelect.click()
        await page.waitForTimeout(300)
        
        // Look for page size options
        const option25 = await page.$('option[value="25"], [value="25"]')
        if (option25) {
          await option25.click()
          await page.waitForTimeout(500)
        }
      }
      
      // Test passes if no errors occur
      expect(true).toBe(true)
    })

    it('should display loading state initially', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      
      // Check for loading indicators immediately after navigation
      try {
        await page.waitForSelector('[data-testid="skeleton"], .loading, text="Loading"', { timeout: 1000 })
        const loadingElement = await page.$('[data-testid="skeleton"], .loading, text="Loading"')
        expect(loadingElement).toBeTruthy()
      } catch (error) {
        // If loading state is too fast to catch, that's also acceptable
        expect(true).toBe(true)
      }
    })

    it('should handle error states gracefully', async () => {
      // Test with a potentially invalid URL or simulate network error
      await page.goto(`${baseUrl}/en/assets`)
      
      // Wait for page to load
      await page.waitForSelector('[data-testid="asset-management-client"]', { timeout: 10000 })
      
      // Look for error messages or retry buttons
      const errorMessage = await page.$('text="Error", text="Failed", text="Retry"')
      const retryButton = await page.$('button:has-text("Retry"), button:has-text("retry")')
      
      // If error state exists, test retry functionality
      if (retryButton) {
        await retryButton.click()
        await page.waitForTimeout(1000)
      }
      
      // Test passes if page doesn't crash
      expect(true).toBe(true)
    })

    it('should be responsive on mobile viewport', async () => {
      // Set mobile viewport
      await page.setViewport({ width: 375, height: 667 })
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Check if page renders without horizontal scroll
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
      const viewportWidth = await page.evaluate(() => window.innerWidth)
      
      expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 20) // Allow small margin
    })

    it('should maintain state when navigating back', async () => {
      await page.goto(`${baseUrl}/en/assets`)
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Set a search filter
      const searchInput = await page.$('input[placeholder*="search"]')
      if (searchInput) {
        await searchInput.type('test search')
        await page.waitForTimeout(500)
      }
      
      // Navigate to another page and back
      await page.goto(`${baseUrl}/en/dashboard`)
      await page.waitForTimeout(500)
      await page.goBack()
      await page.waitForSelector('[data-testid="asset-management-client"]')
      
      // Check if search state is maintained (this depends on implementation)
      // For now, just verify page loads correctly
      const title = await page.$eval('h1', el => el.textContent)
      expect(title).toContain('Asset')
    })
  })
})
