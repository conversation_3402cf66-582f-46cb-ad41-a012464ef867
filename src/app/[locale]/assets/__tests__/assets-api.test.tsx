import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { configureStore } from '@reduxjs/toolkit'
import { assetsApi, type AssetsQueryParams } from '@/redux/services/assetsApi'
import { AssetStatus } from '@/lib/types'

// Mock data
const mockAssets = [
  {
    id: 'asset-1',
    restaurant_id: 'restaurant-1',
    name: 'Test Asset 1',
    description: 'Test description 1',
    purchase_date: '2023-01-01',
    purchase_cost: 10000,
    depreciation_method: 'Straight-Line',
    useful_life_months: 60,
    salvage_value: 1000,
    asset_account_id: 'account-1',
    accumulated_depreciation_account_id: 'account-2',
    depreciation_expense_account_id: 'account-3',
    status: AssetStatus.Active,
    image_url: 'https://example.com/image1.jpg',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: 'asset-2',
    restaurant_id: 'restaurant-1',
    name: 'Test Asset 2',
    description: 'Test description 2',
    purchase_date: '2023-02-01',
    purchase_cost: 15000,
    depreciation_method: 'Declining Balance',
    useful_life_months: 48,
    salvage_value: 2000,
    asset_account_id: 'account-1',
    accumulated_depreciation_account_id: 'account-2',
    depreciation_expense_account_id: 'account-3',
    status: AssetStatus.Disposed,
    image_url: null,
    created_at: '2023-02-01T00:00:00Z',
    updated_at: '2023-02-01T00:00:00Z',
  },
]

const mockAssetsResponse = {
  assets: mockAssets,
  pagination: {
    total: 2,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNextPage: false,
    hasPreviousPage: false,
  },
}

// Setup MSW server
const server = setupServer(
  // Get assets with filters, sort, and pagination
  rest.get('/api/assets', (req, res, ctx) => {
    const url = new URL(req.url)
    const search = url.searchParams.get('search')
    const status = url.searchParams.get('status')
    const depreciationMethod = url.searchParams.get('depreciation_method')
    const purchaseDateFrom = url.searchParams.get('purchase_date_from')
    const purchaseDateTo = url.searchParams.get('purchase_date_to')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const sortBy = url.searchParams.get('sortBy') || 'purchase_date'
    const sortOrder = url.searchParams.get('sortOrder') || 'desc'

    let filteredAssets = [...mockAssets]

    // Apply search filter
    if (search) {
      filteredAssets = filteredAssets.filter(asset =>
        asset.name.toLowerCase().includes(search.toLowerCase()) ||
        (asset.description && asset.description.toLowerCase().includes(search.toLowerCase()))
      )
    }

    // Apply status filter
    if (status) {
      filteredAssets = filteredAssets.filter(asset => asset.status === status)
    }

    // Apply depreciation method filter
    if (depreciationMethod) {
      filteredAssets = filteredAssets.filter(asset => asset.depreciation_method === depreciationMethod)
    }

    // Apply date range filters
    if (purchaseDateFrom) {
      filteredAssets = filteredAssets.filter(asset => asset.purchase_date >= purchaseDateFrom)
    }
    if (purchaseDateTo) {
      filteredAssets = filteredAssets.filter(asset => asset.purchase_date <= purchaseDateTo)
    }

    // Apply sorting
    filteredAssets.sort((a, b) => {
      let aValue: any = a[sortBy as keyof typeof a]
      let bValue: any = b[sortBy as keyof typeof b]

      if (sortBy === 'purchase_cost') {
        aValue = Number(aValue)
        bValue = Number(bValue)
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedAssets = filteredAssets.slice(startIndex, endIndex)

    const response = {
      assets: paginatedAssets,
      pagination: {
        total: filteredAssets.length,
        page,
        limit,
        totalPages: Math.ceil(filteredAssets.length / limit),
        hasNextPage: endIndex < filteredAssets.length,
        hasPreviousPage: page > 1,
      },
    }

    return res(ctx.json(response))
  }),

  // Get single asset
  rest.get('/api/assets/:id', (req, res, ctx) => {
    const { id } = req.params
    const asset = mockAssets.find(a => a.id === id)
    
    if (!asset) {
      return res(ctx.status(404), ctx.json({ error: 'Asset not found' }))
    }

    return res(ctx.json(asset))
  }),

  // Create asset
  rest.post('/api/assets', (req, res, ctx) => {
    return res(ctx.json({
      id: 'new-asset-id',
      ...req.body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }))
  }),

  // Update asset
  rest.put('/api/assets/:id', (req, res, ctx) => {
    const { id } = req.params
    const asset = mockAssets.find(a => a.id === id)
    
    if (!asset) {
      return res(ctx.status(404), ctx.json({ error: 'Asset not found' }))
    }

    return res(ctx.json({
      ...asset,
      ...req.body,
      updated_at: new Date().toISOString(),
    }))
  }),

  // Delete asset
  rest.delete('/api/assets/:id', (req, res, ctx) => {
    const { id } = req.params
    const asset = mockAssets.find(a => a.id === id)
    
    if (!asset) {
      return res(ctx.status(404), ctx.json({ error: 'Asset not found' }))
    }

    return res(ctx.status(204))
  }),

  // Batch update assets
  rest.post('/api/assets/batch-update', (req, res, ctx) => {
    return res(ctx.json({ updated_count: 2 }))
  }),

  // Batch delete assets
  rest.post('/api/assets/batch-delete', (req, res, ctx) => {
    return res(ctx.json({ deleted_count: 2 }))
  }),

  // Run depreciation
  rest.post('/api/assets/depreciation', (req, res, ctx) => {
    return res(ctx.json({
      success: true,
      message: 'Depreciation completed successfully',
      journal_entries_created_count: 5,
    }))
  }),

  // Dispose asset
  rest.post('/api/assets/:id/dispose', (req, res, ctx) => {
    return res(ctx.json({
      success: true,
      message: 'Asset disposed successfully',
      journal_entry_id: 'journal-entry-id',
    }))
  }),
)

// Create test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      [assetsApi.reducerPath]: assetsApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(assetsApi.middleware),
  })
}

describe('Assets API', () => {
  beforeEach(() => {
    server.listen()
  })

  afterEach(() => {
    server.resetHandlers()
  })

  afterEach(() => {
    server.close()
  })

  describe('getAssets', () => {
    it('fetches assets without parameters', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssets.initiate()
      )

      expect(result.data).toEqual(mockAssetsResponse)
      expect(result.data?.assets).toHaveLength(2)
      expect(result.data?.pagination.total).toBe(2)
    })

    it('fetches assets with search parameter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssets.initiate({ search: 'Test Asset 1' })
      )

      expect(result.data?.assets).toHaveLength(1)
      expect(result.data?.assets[0].name).toBe('Test Asset 1')
    })

    it('fetches assets with status filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssets.initiate({ status: AssetStatus.Active })
      )

      expect(result.data?.assets).toHaveLength(1)
      expect(result.data?.assets[0].status).toBe(AssetStatus.Active)
    })

    it('fetches assets with depreciation method filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssets.initiate({ depreciation_method: 'Straight-Line' })
      )

      expect(result.data?.assets).toHaveLength(1)
      expect(result.data?.assets[0].depreciation_method).toBe('Straight-Line')
    })

    it('fetches assets with date range filters', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssets.initiate({
          purchase_date_from: '2023-01-01',
          purchase_date_to: '2023-01-31'
        })
      )

      expect(result.data?.assets).toHaveLength(1)
      expect(result.data?.assets[0].purchase_date).toBe('2023-01-01')
    })

    it('fetches assets with pagination', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssets.initiate({ page: 1, limit: 1 })
      )

      expect(result.data?.assets).toHaveLength(1)
      expect(result.data?.pagination.page).toBe(1)
      expect(result.data?.pagination.limit).toBe(1)
      expect(result.data?.pagination.totalPages).toBe(2)
    })

    it('fetches assets with sorting', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssets.initiate({
          sortBy: 'purchase_cost',
          sortOrder: 'asc'
        })
      )

      expect(result.data?.assets).toHaveLength(2)
      expect(result.data?.assets[0].purchase_cost).toBe(10000)
      expect(result.data?.assets[1].purchase_cost).toBe(15000)
    })
  })

  describe('getAssetById', () => {
    it('fetches a single asset by ID', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssetById.initiate('asset-1')
      )

      expect(result.data).toEqual(mockAssets[0])
    })

    it('returns error for non-existent asset', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.getAssetById.initiate('non-existent')
      )

      expect(result.error).toBeDefined()
    })
  })

  describe('createAsset', () => {
    it('creates a new asset', async () => {
      const store = createTestStore()
      const newAsset = {
        name: 'New Asset',
        description: 'New asset description',
        purchase_date: '2023-03-01',
        purchase_cost: 20000,
        depreciation_method: 'Straight-Line',
        useful_life_months: 36,
        salvage_value: 3000,
        status: AssetStatus.Active,
      }
      
      const result = await store.dispatch(
        assetsApi.endpoints.createAsset.initiate(newAsset)
      )

      expect(result.data).toMatchObject(newAsset)
      expect(result.data?.id).toBe('new-asset-id')
    })
  })

  describe('updateAsset', () => {
    it('updates an existing asset', async () => {
      const store = createTestStore()
      const updateData = { name: 'Updated Asset Name' }
      
      const result = await store.dispatch(
        assetsApi.endpoints.updateAsset.initiate({
          id: 'asset-1',
          body: updateData
        })
      )

      expect(result.data?.name).toBe('Updated Asset Name')
    })
  })

  describe('deleteAsset', () => {
    it('deletes an asset', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.deleteAsset.initiate('asset-1')
      )

      expect(result.data).toBeUndefined() // 204 No Content
    })
  })

  describe('batchUpdateAssets', () => {
    it('updates multiple assets', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.batchUpdateAssets.initiate({
          asset_ids: ['asset-1', 'asset-2'],
          status: AssetStatus.Disposed
        })
      )

      expect(result.data?.updated_count).toBe(2)
    })
  })

  describe('batchDeleteAssets', () => {
    it('deletes multiple assets', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.batchDeleteAssets.initiate({
          ids: ['asset-1', 'asset-2']
        })
      )

      expect(result.data?.deleted_count).toBe(2)
    })
  })

  describe('runDepreciation', () => {
    it('runs depreciation calculation', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.runDepreciation.initiate({
          run_until_date: '2023-12-31',
          asset_ids: ['asset-1', 'asset-2']
        })
      )

      expect(result.data?.success).toBe(true)
      expect(result.data?.journal_entries_created_count).toBe(5)
    })
  })

  describe('disposeAsset', () => {
    it('disposes an asset', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        assetsApi.endpoints.disposeAsset.initiate({
          id: 'asset-1',
          disposal_date: '2023-12-31',
          disposal_proceeds: 5000
        })
      )

      expect(result.data?.success).toBe(true)
      expect(result.data?.journal_entry_id).toBe('journal-entry-id')
    })
  })
})
