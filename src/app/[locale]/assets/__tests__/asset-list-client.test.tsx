import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import AssetListClient from '../_components/asset-list-client'
import { assetsApi } from '@/redux/services/assetsApi'
import { AssetStatus } from '@/lib/types'

// Mock next-intl
vi.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}))

// Mock useI18n hook
vi.mock('@/hooks/useI18n', () => ({
  useI18n: () => ({
    formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
    formatDate: (date: Date) => date.toLocaleDateString(),
  }),
}))

// Mock Lucide React icons
vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) =>
    ({ className, ...props }: any) => <span data-testid={`${name}-icon`} className={className} {...props} />

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return createMockIcon(prop)
      }
      return undefined
    }
  })
})

// Mock UI components
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, variant, size, asChild, ...props }: any) => 
    asChild ? children : (
      <button 
        onClick={onClick} 
        disabled={disabled} 
        data-variant={variant}
        data-size={size}
        {...props}
      >
        {children}
      </button>
    )
}))

vi.mock('@/components/ui/input', () => ({
  Input: ({ onChange, value, placeholder, ...props }: any) => (
    <input 
      onChange={onChange} 
      value={value} 
      placeholder={placeholder}
      {...props}
    />
  )
}))

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <button onClick={() => onValueChange && onValueChange('test-value')}>
        {children}
      </button>
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}))

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardDescription: ({ children }: any) => <p>{children}</p>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
}))

vi.mock('@/components/ui/table', () => ({
  Table: ({ children }: any) => <table>{children}</table>,
  TableBody: ({ children }: any) => <tbody>{children}</tbody>,
  TableCell: ({ children, colSpan }: any) => <td colSpan={colSpan}>{children}</td>,
  TableHead: ({ children, onClick, className }: any) => (
    <th onClick={onClick} className={className}>{children}</th>
  ),
  TableHeader: ({ children }: any) => <thead>{children}</thead>,
  TableRow: ({ children }: any) => <tr>{children}</tr>,
}))

vi.mock('@/components/ui/skeleton', () => ({
  Skeleton: ({ className }: any) => <div className={className} data-testid="skeleton">Loading...</div>
}))

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className }: any) => <span className={className}>{children}</span>
}))

vi.mock('@/components/ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: any) => <div>{children}</div>,
  DropdownMenuContent: ({ children }: any) => <div>{children}</div>,
  DropdownMenuItem: ({ children, asChild }: any) => asChild ? children : <div>{children}</div>,
  DropdownMenuLabel: ({ children }: any) => <div>{children}</div>,
  DropdownMenuTrigger: ({ children, asChild }: any) => asChild ? children : <div>{children}</div>,
}))

vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div>{children}</div>,
  DialogDescription: ({ children }: any) => <p>{children}</p>,
  DialogFooter: ({ children }: any) => <div>{children}</div>,
  DialogHeader: ({ children }: any) => <div>{children}</div>,
  DialogTitle: ({ children }: any) => <h2>{children}</h2>,
}))

vi.mock('@/components/ui/label', () => ({
  Label: ({ children, htmlFor }: any) => <label htmlFor={htmlFor}>{children}</label>
}))

vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

vi.mock('@/components/ui/date-picker', () => ({
  DatePicker: ({ date, setDate, placeholder }: any) => (
    <input 
      type="date" 
      value={date ? date.toISOString().split('T')[0] : ''} 
      onChange={(e) => setDate && setDate(new Date(e.target.value))}
      placeholder={placeholder}
      data-testid="date-picker"
    />
  )
}))

vi.mock('@/components/batch-selection', () => ({
  BatchSelectionProvider: ({ children }: any) => <div>{children}</div>,
  BatchActionBar: ({ entityName, actions, onAction }: any) => (
    <div data-testid="batch-action-bar">
      <span>{entityName}</span>
      {actions.map((action: any) => (
        <button key={action.id} onClick={() => onAction(action.id, ['test-id'])}>
          {action.label}
        </button>
      ))}
    </div>
  ),
  BatchSelectionCheckbox: ({ id }: any) => (
    <input type="checkbox" data-testid={`checkbox-${id}`} />
  ),
  BatchSelectAll: ({ ids }: any) => (
    <input type="checkbox" data-testid="select-all" />
  ),
  useBatchSelection: () => ({
    isSelected: vi.fn(),
    getSelectedIds: () => ['test-id-1', 'test-id-2'],
  }),
}))

vi.mock('next/link', () => ({
  default: ({ children, href }: any) => <a href={href}>{children}</a>
}))

// Mock data
const mockAssetsResponse = {
  assets: [
    {
      id: 'asset-1',
      name: 'Test Asset 1',
      description: 'Test description 1',
      purchase_date: '2023-01-01',
      purchase_cost: 10000,
      depreciation_method: 'Straight-Line',
      useful_life_months: 60,
      salvage_value: 1000,
      status: AssetStatus.Active,
      image_url: 'https://example.com/image1.jpg',
    },
    {
      id: 'asset-2',
      name: 'Test Asset 2',
      description: 'Test description 2',
      purchase_date: '2023-02-01',
      purchase_cost: 15000,
      depreciation_method: 'Declining Balance',
      useful_life_months: 48,
      salvage_value: 2000,
      status: AssetStatus.Disposed,
      image_url: null,
    },
  ],
  pagination: {
    total: 2,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNextPage: false,
    hasPreviousPage: false,
  },
}

// Create mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      [assetsApi.reducerPath]: assetsApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(assetsApi.middleware),
    preloadedState: {
      [assetsApi.reducerPath]: {
        queries: {
          'getAssets(undefined)': {
            status: 'fulfilled',
            data: mockAssetsResponse,
            endpointName: 'getAssets',
            requestId: 'test-request-id',
            startedTimeStamp: Date.now(),
            fulfilledTimeStamp: Date.now(),
          },
        },
        mutations: {},
        provided: {},
        subscriptions: {},
        config: {
          online: true,
          focused: true,
          middlewareRegistered: true,
        },
      },
      ...initialState,
    },
  })
}

describe('AssetListClient', () => {
  let store: ReturnType<typeof createMockStore>

  beforeEach(() => {
    vi.clearAllMocks()
    store = createMockStore()
  })

  const renderWithProvider = (component: React.ReactElement) => {
    return render(
      <Provider store={store}>
        {component}
      </Provider>
    )
  }

  it('renders asset list with data', () => {
    renderWithProvider(<AssetListClient />)

    expect(screen.getByText('title')).toBeInTheDocument()
    expect(screen.getByText('Test Asset 1')).toBeInTheDocument()
    expect(screen.getByText('Test Asset 2')).toBeInTheDocument()
  })

  it('renders filters section', () => {
    renderWithProvider(<AssetListClient />)

    expect(screen.getByText('filters.title')).toBeInTheDocument()
    expect(screen.getByText('filters.description')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('filters.search')).toBeInTheDocument()
  })

  it('handles search input', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    const searchInput = screen.getByPlaceholderText('filters.search')
    await user.type(searchInput, 'Test Asset')

    expect(searchInput).toHaveValue('Test Asset')
  })

  it('handles status filter selection', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    const statusSelect = screen.getByTestId('select')
    await user.click(statusSelect)

    expect(statusSelect).toBeInTheDocument()
  })

  it('handles view mode toggle', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    const viewToggleButton = screen.getByText('actions.gridView')
    await user.click(viewToggleButton)

    // Should toggle to list view
    expect(screen.getByText('actions.listView')).toBeInTheDocument()
  })

  it('renders pagination when multiple pages exist', () => {
    const storeWithPagination = createMockStore({
      [assetsApi.reducerPath]: {
        queries: {
          'getAssets(undefined)': {
            status: 'fulfilled',
            data: {
              ...mockAssetsResponse,
              pagination: {
                ...mockAssetsResponse.pagination,
                totalPages: 3,
                hasNextPage: true,
              },
            },
            endpointName: 'getAssets',
            requestId: 'test-request-id',
            startedTimeStamp: Date.now(),
            fulfilledTimeStamp: Date.now(),
          },
        },
        mutations: {},
        provided: {},
        subscriptions: {},
        config: {
          online: true,
          focused: true,
          middlewareRegistered: true,
        },
      },
    })

    render(
      <Provider store={storeWithPagination}>
        <AssetListClient />
      </Provider>
    )

    expect(screen.getByText('pagination.next')).toBeInTheDocument()
  })

  it('renders batch action bar', () => {
    renderWithProvider(<AssetListClient />)

    expect(screen.getByTestId('batch-action-bar')).toBeInTheDocument()
    expect(screen.getByText('actions.updateStatus')).toBeInTheDocument()
    expect(screen.getByText('actions.deleteAssets')).toBeInTheDocument()
  })

  it('handles batch status update', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    const statusButton = screen.getByText('actions.updateStatus')
    await user.click(statusButton)

    expect(screen.getByTestId('dialog')).toBeInTheDocument()
    expect(screen.getByText('dialog.batchUpdate.title')).toBeInTheDocument()
  })

  it('renders asset cards in grid view', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    // Switch to grid view
    const viewToggleButton = screen.getByText('actions.gridView')
    await user.click(viewToggleButton)

    // Should show asset cards
    expect(screen.getByText('Test Asset 1')).toBeInTheDocument()
    expect(screen.getByText('Test Asset 2')).toBeInTheDocument()
  })

  it('displays loading state', () => {
    const loadingStore = createMockStore({
      [assetsApi.reducerPath]: {
        queries: {
          'getAssets(undefined)': {
            status: 'pending',
            endpointName: 'getAssets',
            requestId: 'test-request-id',
            startedTimeStamp: Date.now(),
          },
        },
        mutations: {},
        provided: {},
        subscriptions: {},
        config: {
          online: true,
          focused: true,
          middlewareRegistered: true,
        },
      },
    })

    render(
      <Provider store={loadingStore}>
        <AssetListClient />
      </Provider>
    )

    expect(screen.getAllByTestId('skeleton')).toHaveLength(10) // Default page size
  })

  it('displays error state', () => {
    const errorStore = createMockStore({
      [assetsApi.reducerPath]: {
        queries: {
          'getAssets(undefined)': {
            status: 'rejected',
            error: { message: 'Failed to load assets' },
            endpointName: 'getAssets',
            requestId: 'test-request-id',
            startedTimeStamp: Date.now(),
          },
        },
        mutations: {},
        provided: {},
        subscriptions: {},
        config: {
          online: true,
          focused: true,
          middlewareRegistered: true,
        },
      },
    })

    render(
      <Provider store={errorStore}>
        <AssetListClient />
      </Provider>
    )

    expect(screen.getByText('errors.loadingFailed')).toBeInTheDocument()
    expect(screen.getByText('actions.retry')).toBeInTheDocument()
  })

  it('handles sorting', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    const nameHeader = screen.getByText('table.name ↓')
    await user.click(nameHeader)

    // Should toggle sort order
    expect(nameHeader).toBeInTheDocument()
  })

  it('handles date range filters', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    const datePickers = screen.getAllByTestId('date-picker')
    expect(datePickers).toHaveLength(2)

    await user.type(datePickers[0], '2023-01-01')
    await user.type(datePickers[1], '2023-12-31')

    expect(datePickers[0]).toHaveValue('2023-01-01')
    expect(datePickers[1]).toHaveValue('2023-12-31')
  })

  it('clears filters when clear button is clicked', async () => {
    const user = userEvent.setup()
    renderWithProvider(<AssetListClient />)

    // Set some filters first
    const searchInput = screen.getByPlaceholderText('filters.search')
    await user.type(searchInput, 'test')

    const clearButton = screen.getByText('filters.clear')
    await user.click(clearButton)

    expect(searchInput).toHaveValue('')
  })

  it('renders asset actions in dropdown menu', () => {
    renderWithProvider(<AssetListClient />)

    expect(screen.getByText('actions.view')).toBeInTheDocument()
    expect(screen.getByText('actions.edit')).toBeInTheDocument()
    expect(screen.getByText('actions.dispose')).toBeInTheDocument()
  })

  it('shows no assets message when list is empty', () => {
    const emptyStore = createMockStore({
      [assetsApi.reducerPath]: {
        queries: {
          'getAssets(undefined)': {
            status: 'fulfilled',
            data: {
              assets: [],
              pagination: {
                total: 0,
                page: 1,
                limit: 10,
                totalPages: 0,
                hasNextPage: false,
                hasPreviousPage: false,
              },
            },
            endpointName: 'getAssets',
            requestId: 'test-request-id',
            startedTimeStamp: Date.now(),
            fulfilledTimeStamp: Date.now(),
          },
        },
        mutations: {},
        provided: {},
        subscriptions: {},
        config: {
          online: true,
          focused: true,
          middlewareRegistered: true,
        },
      },
    })

    render(
      <Provider store={emptyStore}>
        <AssetListClient />
      </Provider>
    )

    expect(screen.getByText('table.noAssetsFound')).toBeInTheDocument()
  })
})
