'use client';

import React, { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/i18n/navigation';
import {
  CheckCircle,
  Circle,
  Search,
  Filter,
  ArrowLeft,
  ArrowRight,
  Loader2,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';

// Import components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';

// Import hooks and services
import { useBranchContext } from '@/contexts/BranchContext';
import { useBulkCreateAccountsMutation } from '@/redux/services/chartOfAccountsApi';

// Import template data
import {
  defaultChartOfAccountsTemplate,
  convertTemplateToAccounts,
  type AccountTemplate
} from '@/lib/chart-of-accounts-templates';

type SetupStep = 'welcome' | 'selection' | 'confirmation' | 'success';

export default function ChartOfAccountsSetupPage() {
  const t = useTranslations('chartOfAccountsSetup');
  const router = useRouter();
  const { toast } = useToast();
  const { selectedBranchId } = useBranchContext();

  // State
  const [currentStep, setCurrentStep] = useState<SetupStep>('welcome');
  const [selectedAccounts, setSelectedAccounts] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');

  // RTK Query
  const [bulkCreateAccounts, { isLoading: isCreating }] = useBulkCreateAccountsMutation();

  // Convert template to flat array
  const allAccounts = useMemo(() => {
    return convertTemplateToAccounts(defaultChartOfAccountsTemplate);
  }, []);

  // Filter accounts based on search and category
  const filteredAccounts = useMemo(() => {
    let filtered = allAccounts;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(account =>
        account.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        account.code.includes(searchQuery)
      );
    }

    // Apply category filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(account => {
        switch (activeFilter) {
          case 'assets':
            return account.type.includes('Asset');
          case 'liabilities':
            return account.type.includes('Liability');
          case 'equity':
            return account.type.includes('Equity') || account.type.includes('Stock') || account.type.includes('Earnings');
          case 'revenue':
            return account.type.includes('Revenue');
          case 'expenses':
            return account.type.includes('Expense');
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [allAccounts, searchQuery, activeFilter]);

  // Group accounts by category for display
  const groupedAccounts = useMemo(() => {
    const groups: Record<string, typeof allAccounts> = {
      assets: [],
      liabilities: [],
      equity: [],
      revenue: [],
      expenses: []
    };

    filteredAccounts.forEach(account => {
      if (account.type.includes('Asset')) {
        groups.assets.push(account);
      } else if (account.type.includes('Liability')) {
        groups.liabilities.push(account);
      } else if (account.type.includes('Equity') || account.type.includes('Stock') || account.type.includes('Earnings')) {
        groups.equity.push(account);
      } else if (account.type.includes('Revenue')) {
        groups.revenue.push(account);
      } else if (account.type.includes('Expense')) {
        groups.expenses.push(account);
      }
    });

    return groups;
  }, [filteredAccounts]);

  // Handle account selection
  const toggleAccount = (accountCode: string) => {
    const newSelected = new Set(selectedAccounts);
    if (newSelected.has(accountCode)) {
      newSelected.delete(accountCode);
    } else {
      newSelected.add(accountCode);
    }
    setSelectedAccounts(newSelected);
  };

  // Handle select all/deselect all
  const handleSelectAll = () => {
    if (selectedAccounts.size === filteredAccounts.length) {
      setSelectedAccounts(new Set());
    } else {
      setSelectedAccounts(new Set(filteredAccounts.map(account => account.code)));
    }
  };

  // Handle account creation
  const handleCreateAccounts = async () => {
    if (!selectedBranchId) {
      toast({
        title: t('messages.error', { message: 'No branch selected' }),
        variant: 'destructive'
      });
      return;
    }

    const accountsToCreate = allAccounts.filter(account =>
      selectedAccounts.has(account.code)
    );

    try {
      const result = await bulkCreateAccounts({
        branchId: selectedBranchId,
        accounts: accountsToCreate
      }).unwrap();

      if (result.errors && result.errors.length > 0) {
        toast({
          title: t('messages.partialSuccess', {
            created: result.createdCount,
            total: accountsToCreate.length,
            errors: result.errors.length
          }),
          variant: 'default'
        });
      } else {
        toast({
          title: t('steps.success.accountsCreated', { count: result.createdCount }),
          variant: 'default'
        });
      }

      setCurrentStep('success');
    } catch (error: any) {
      toast({
        title: t('messages.error', { message: error.message || 'Unknown error' }),
        variant: 'destructive'
      });
    }
  };

  // Render welcome step
  const renderWelcomeStep = () => (
    <div className="text-center space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">{t('steps.welcome.title')}</h1>
        <p className="text-muted-foreground text-lg">{t('steps.welcome.description')}</p>
      </div>

      <div className="flex justify-center">
        <Button
          onClick={() => setCurrentStep('selection')}
          size="lg"
          className="px-8"
        >
          {t('steps.welcome.getStarted')}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  // Render selection step
  const renderSelectionStep = () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">{t('steps.selection.title')}</h1>
        <p className="text-muted-foreground">{t('steps.selection.description')}</p>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('search.placeholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <Tabs value={activeFilter} onValueChange={setActiveFilter} className="w-full sm:w-auto">
          <TabsList className="grid grid-cols-6">
            <TabsTrigger value="all">{t('filters.all')}</TabsTrigger>
            <TabsTrigger value="assets">{t('filters.assets')}</TabsTrigger>
            <TabsTrigger value="liabilities">{t('filters.liabilities')}</TabsTrigger>
            <TabsTrigger value="equity">{t('filters.equity')}</TabsTrigger>
            <TabsTrigger value="revenue">{t('filters.revenue')}</TabsTrigger>
            <TabsTrigger value="expenses">{t('filters.expenses')}</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Selection controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
          >
            {selectedAccounts.size === filteredAccounts.length ? t('steps.selection.deselectAll') : t('steps.selection.selectAll')}
          </Button>
          <span className="text-sm text-muted-foreground">
            {t('steps.selection.selectedCount', { count: selectedAccounts.size })}
          </span>
        </div>
      </div>

      {/* Account list */}
      <div className="space-y-6">
        {Object.entries(groupedAccounts).map(([category, accounts]) => {
          if (accounts.length === 0) return null;

          return (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {t(`steps.selection.categories.${category}`, { count: accounts.length })}
                  <Badge variant="outline">{accounts.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2">
                  {accounts.map((account) => (
                    <div
                      key={account.code}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 cursor-pointer"
                      onClick={() => toggleAccount(account.code)}
                    >
                      <Checkbox
                        checked={selectedAccounts.has(account.code)}
                        onChange={() => toggleAccount(account.code)}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-sm text-muted-foreground">{account.code}</span>
                          <span className="font-medium">{account.name}</span>
                        </div>
                        {account.description && (
                          <p className="text-sm text-muted-foreground truncate">{account.description}</p>
                        )}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {account.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredAccounts.length === 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">{t('search.noResults')}</p>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentStep('welcome')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('buttons.previous')}
        </Button>

        <Button
          onClick={() => setCurrentStep('confirmation')}
          disabled={selectedAccounts.size === 0}
        >
          {t('buttons.next')}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  // Render confirmation step
  const renderConfirmationStep = () => {
    const accountsToCreate = allAccounts.filter(account =>
      selectedAccounts.has(account.code)
    );

    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">{t('steps.confirmation.title')}</h1>
          <p className="text-muted-foreground">{t('steps.confirmation.description')}</p>
        </div>

        {accountsToCreate.length === 0 ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {t('steps.confirmation.noAccountsSelected')}
            </AlertDescription>
          </Alert>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>{t('steps.confirmation.accountsToCreate')}</CardTitle>
              <CardDescription>
                {t('steps.selection.selectedCount', { count: accountsToCreate.length })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="max-h-96 overflow-y-auto space-y-2">
                {accountsToCreate.map((account) => (
                  <div
                    key={account.code}
                    className="flex items-center justify-between p-2 rounded-lg border"
                  >
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm text-muted-foreground">{account.code}</span>
                      <span className="font-medium">{account.name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {account.type}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentStep('selection')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('steps.confirmation.goBack')}
          </Button>

          <Button
            onClick={handleCreateAccounts}
            disabled={accountsToCreate.length === 0 || isCreating}
          >
            {isCreating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('messages.loading')}
              </>
            ) : (
              <>
                {t('steps.confirmation.createAccounts')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </div>
    );
  };

  // Render success step
  const renderSuccessStep = () => (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <CheckCircle2 className="h-16 w-16 text-green-500" />
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold">{t('steps.success.title')}</h1>
        <p className="text-muted-foreground text-lg">{t('steps.success.description')}</p>
        <p className="text-sm text-muted-foreground">
          {t('steps.success.accountsCreated', { count: selectedAccounts.size })}
        </p>
      </div>

      <div className="flex justify-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.push('/chart-of-accounts')}
        >
          {t('steps.success.viewAccounts')}
        </Button>
        <Button
          onClick={() => router.push('/dashboard')}
        >
          {t('steps.success.goToDashboard')}
        </Button>
      </div>
    </div>
  );

  // Main render
  return (
    <div className="container mx-auto py-10 max-w-4xl">
      <div className="space-y-8">
        {/* Progress indicator */}
        <div className="flex justify-center">
          <div className="flex items-center space-x-4">
            {(['welcome', 'selection', 'confirmation', 'success'] as SetupStep[]).map((step, index) => (
              <div key={step} className="flex items-center">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                  ${currentStep === step
                    ? 'bg-primary text-primary-foreground'
                    : index < (['welcome', 'selection', 'confirmation', 'success'] as SetupStep[]).indexOf(currentStep)
                    ? 'bg-green-500 text-white'
                    : 'bg-muted text-muted-foreground'
                  }
                `}>
                  {index < (['welcome', 'selection', 'confirmation', 'success'] as SetupStep[]).indexOf(currentStep) ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                {index < 3 && (
                  <div className={`
                    w-12 h-0.5 mx-2
                    ${index < (['welcome', 'selection', 'confirmation', 'success'] as SetupStep[]).indexOf(currentStep)
                      ? 'bg-green-500'
                      : 'bg-muted'
                    }
                  `} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step content */}
        <div className="min-h-[600px]">
          {currentStep === 'welcome' && renderWelcomeStep()}
          {currentStep === 'selection' && renderSelectionStep()}
          {currentStep === 'confirmation' && renderConfirmationStep()}
          {currentStep === 'success' && renderSuccessStep()}
        </div>

        {/* Skip option */}
        {currentStep !== 'success' && (
          <div className="text-center">
            <Button
              variant="ghost"
              onClick={() => router.push('/chart-of-accounts')}
              className="text-muted-foreground"
            >
              {t('buttons.skip')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}