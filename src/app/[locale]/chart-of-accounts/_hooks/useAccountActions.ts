import { useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { useBranchContext } from '@/contexts/BranchContext';
import type { ChartOfAccount } from './useChartOfAccounts';
import type { AccountType } from '@/lib/types';

// Map frontend account types to backend basic types
function mapAccountTypeToBackend(frontendType: AccountType): string {
  // Asset types
  if (frontendType.includes('Asset')) return 'Asset';
  // Liability types
  if (frontendType.includes('Liability')) return 'Liability';
  // Equity types
  if (frontendType.includes('Equity') || frontendType === 'CommonStock' || frontendType === 'RetainedEarnings') return 'Equity';
  // Revenue types
  if (frontendType.includes('Revenue')) return 'Revenue';
  // Expense types
  if (frontendType.includes('Expense')) return 'Expense';

  // Default to the basic types if they match exactly
  return frontendType;
}

interface UseAccountActionsProps {
  createAccount: any;
  updateAccount: any;
  deleteAccount: any;
  refetch: () => void;
  selectedAccount: ChartOfAccount | null;
  accountCode: string;
  accountName: string;
  accountType: string;
  accountDescription: string;
  accountIsActive: boolean;
  setSubmitError: (error: string) => void;
  setIsSubmitting: (submitting: boolean) => void;
  closeAddDialog: () => void;
  closeEditDialog: () => void;
  closeDeleteDialog: () => void;
}

export function useAccountActions({
  createAccount,
  updateAccount,
  deleteAccount,
  refetch,
  selectedAccount,
  accountCode,
  accountName,
  accountType,
  accountDescription,
  accountIsActive,
  setSubmitError,
  setIsSubmitting,
  closeAddDialog,
  closeEditDialog,
  closeDeleteDialog,
}: UseAccountActionsProps) {
  const common = useTranslations('common');
  const { selectedBranchId } = useBranchContext();

  const handleSaveAccount = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedBranchId) {
      setSubmitError('No branch selected');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      const accountData = {
        branchId: selectedBranchId,
        code: accountCode,
        name: accountName,
        type: mapAccountTypeToBackend(accountType),
        description: accountDescription || undefined,
        isActive: accountIsActive,
      };

      await createAccount(accountData).unwrap();

      toast.success(common('success'), {
        description: 'Account created successfully',
      });

      // Small delay to ensure toast is shown before closing
      setTimeout(() => {
        closeAddDialog();
        refetch();
      }, 100);
    } catch (error: any) {
      console.error('Error creating account:', error);
      setSubmitError(error?.data?.error || error?.message || 'Failed to create account');
    } finally {
      setIsSubmitting(false);
    }
  }, [
    selectedBranchId,
    accountCode,
    accountName,
    accountType,
    accountDescription,
    accountIsActive,
    createAccount,
    common,
    closeAddDialog,
    refetch,
    setSubmitError,
    setIsSubmitting,
  ]);

  const handleUpdateAccount = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedAccount) return;

    setIsSubmitting(true);
    setSubmitError('');

    try {
      const updateData = {
        code: accountCode,
        name: accountName,
        type: mapAccountTypeToBackend(accountType),
        description: accountDescription || undefined,
        isActive: accountIsActive,
      };

      await updateAccount({ id: selectedAccount.id, body: updateData }).unwrap();

      toast.success(common('success'), {
        description: 'Account updated successfully',
      });

      // Small delay to ensure toast is shown before closing
      setTimeout(() => {
        closeEditDialog();
        refetch();
      }, 100);
    } catch (error: any) {
      console.error('Error updating account:', error);
      setSubmitError(error?.data?.error || error?.message || 'Failed to update account');
    } finally {
      setIsSubmitting(false);
    }
  }, [
    selectedAccount,
    accountCode,
    accountName,
    accountType,
    accountDescription,
    accountIsActive,
    updateAccount,
    common,
    closeEditDialog,
    refetch,
    setSubmitError,
    setIsSubmitting,
  ]);

  const handleDeleteAccount = useCallback(async () => {
    if (!selectedAccount) return;

    setIsSubmitting(true);

    try {
      await deleteAccount(selectedAccount.id).unwrap();

      toast.success(common('success'), {
        description: 'Account deleted successfully',
      });

      // Small delay to ensure toast is shown before closing
      setTimeout(() => {
        closeDeleteDialog();
        refetch();
      }, 100);
    } catch (error: any) {
      console.error('Error deleting account:', error);
      toast.error(common('error'), {
        description: error?.data?.error || error?.message || 'Failed to delete account',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [
    selectedAccount,
    deleteAccount,
    common,
    closeDeleteDialog,
    refetch,
    setIsSubmitting,
  ]);

  return {
    handleSaveAccount,
    handleUpdateAccount,
    handleDeleteAccount,
  };
}
