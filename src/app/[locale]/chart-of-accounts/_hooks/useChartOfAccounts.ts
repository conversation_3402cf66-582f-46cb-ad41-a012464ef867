import { useState, useCallback, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import {
  useGetAccountsQuery,
  useCreateAccountMutation,
  useUpdateAccountMutation,
  useDeleteAccountMutation,
} from '@/redux/services/chartOfAccountsApi';
import { useBranchContext } from '@/contexts/BranchContext';
import { AccountType } from '@/types/account';
import { cleanupModals } from '@/lib/modal-utils';

// ✅ Import advanced pagination
import { useAdvancedPagination } from '@/hooks/useAdvancedPagination';

export interface ChartOfAccount {
  id: string;
  account_code?: string;
  code?: string;
  account_name?: string;
  name?: string;
  account_type?: string;
  type?: string;
  description?: string;
  is_active: boolean;
  balance?: number;
  created_at?: string;
  updated_at?: string;
}

export function useChartOfAccounts() {
  const common = useTranslations('common');
  const { selectedBranchId } = useBranchContext();
  const router = useRouter();
  const searchParams = useSearchParams();

  // ✅ Replace manual state with advanced pagination
  const { state, actions, queryParams } = useAdvancedPagination({
    initialSortBy: 'accountCode',
    initialSortOrder: 'asc',
    initialLimit: 10,
    persist: true,
    persistKey: 'chart-of-accounts-page'
  });

  // Additional state for chart-of-accounts specific features
  const [activeTab, setActiveTab] = useState<string>('all');
  const [goToPage, setGoToPage] = useState<string>('');
  const [isInitialized, setIsInitialized] = useState(false);

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBankLinkDialogOpen, setIsBankLinkDialogOpen] = useState(false);

  // Form states
  const [accountCode, setAccountCode] = useState<string>('');
  const [accountName, setAccountName] = useState<string>('');
  const [accountType, setAccountType] = useState<AccountType>('Asset');
  const [accountDescription, setAccountDescription] = useState<string>('');
  const [accountIsActive, setAccountIsActive] = useState<boolean>(true);
  const [selectedAccount, setSelectedAccount] = useState<ChartOfAccount | null>(null);
  const [submitError, setSubmitError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // ✅ API queries and mutations with enhanced query parameters
  const { data, error, isLoading, refetch, isFetching } = useGetAccountsQuery({
    ...queryParams,
    search: state.search,
    type: activeTab === 'all' ? undefined : activeTab,
    branchId: selectedBranchId,
  });

  const [createAccount] = useCreateAccountMutation();
  const [updateAccount] = useUpdateAccountMutation();
  const [deleteAccount] = useDeleteAccountMutation();

  // ✅ Initialize state from URL parameters only once
  useEffect(() => {
    if (!isInitialized) {
      const urlPage = searchParams.get('page');
      const urlLimit = searchParams.get('limit');
      const urlSearch = searchParams.get('search') || '';
      const urlType = searchParams.get('type') || 'all';
      const urlSortBy = searchParams.get('sortBy') || 'accountCode';
      const urlSortOrder = searchParams.get('sortOrder') || 'asc';

      actions.setPage(urlPage ? parseInt(urlPage) : 1);
      actions.setLimit(urlLimit ? parseInt(urlLimit) : 10);
      actions.handleSearch(urlSearch);
      setActiveTab(urlType);
      if (urlSortBy) {
        actions.handleSort(urlSortBy);
      }
      setIsInitialized(true);
    }
  }, [searchParams, isInitialized, actions]);

  // Function to update URL parameters
  const updateUrlParams = useCallback((updates: Record<string, string | number | null>) => {
    const current = new URLSearchParams(window.location.search);

    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === '' || value === 'all') {
        current.delete(key);
      } else {
        current.set(key, String(value));
      }
    });

    const search = current.toString();
    const query = search ? `?${search}` : '';

    // Use replace to avoid adding to browser history for every filter change
    // Note: Using window.location.pathname here is acceptable for URL parameter management
    router.replace(`${window.location.pathname}${query}`, { scroll: false });
  }, [router]);

  // ✅ Handlers using advanced pagination
  const handleSearch = useCallback((query: string) => {
    actions.handleSearch(query);
    if (isInitialized) {
      updateUrlParams({
        search: query,
        page: 1,
      });
    }
  }, [actions, updateUrlParams, isInitialized]);

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
    actions.setPage(1);
    if (isInitialized) {
      updateUrlParams({
        type: tab,
        page: 1,
      });
    }
  }, [actions, updateUrlParams, isInitialized]);

  const handleSort = useCallback((column: string) => {
    actions.handleSort(column);
    if (isInitialized) {
      updateUrlParams({
        sortBy: column,
        sortOrder: state.sortBy === column ? (state.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc',
        page: 1,
      });
    }
  }, [actions, state.sortBy, state.sortOrder, updateUrlParams, isInitialized]);

  const handlePageSizeChange = useCallback((newLimit: string) => {
    const limitNum = parseInt(newLimit);
    actions.setLimit(limitNum);
    if (isInitialized) {
      updateUrlParams({
        limit: limitNum,
        page: 1,
      });
    }
  }, [actions, updateUrlParams, isInitialized]);

  const handleGoToPage = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    const pageNum = parseInt(goToPage);
    if (pageNum >= 1 && pageNum <= (data?.totalPages || 1)) {
      actions.setPage(pageNum);
      setGoToPage('');
      if (isInitialized) {
        updateUrlParams({
          page: pageNum,
        });
      }
    }
  }, [goToPage, data?.totalPages, actions, updateUrlParams, isInitialized]);

  // ✅ Custom setPage function that also updates URL
  const handlePageChange = useCallback((newPage: number) => {
    actions.setPage(newPage);
    if (isInitialized) {
      updateUrlParams({
        page: newPage,
      });
    }
  }, [actions, updateUrlParams, isInitialized]);

  // Dialog handlers
  const openAddDialog = useCallback(() => {
    setAccountCode('');
    setAccountName('');
    setAccountType('Asset');
    setAccountDescription('');
    setAccountIsActive(true);
    setSubmitError('');
    setIsAddDialogOpen(true);
  }, []);

  // Modal cleanup function using the utility
  const handleModalCleanup = useCallback(() => {
    cleanupModals();
  }, []);

  const closeAddDialog = useCallback(() => {
    setIsAddDialogOpen(false);
    setSubmitError('');
    handleModalCleanup();
  }, [handleModalCleanup]);

  const openEditDialog = useCallback((account: ChartOfAccount) => {
    setSelectedAccount(account);
    setAccountCode((account as any).code || account.account_code || '');
    setAccountName((account as any).name || account.account_name || '');
    setAccountType(((account as any).type || account.account_type || 'Asset') as AccountType);
    setAccountDescription(account.description || '');
    setAccountIsActive(account.is_active);
    setSubmitError('');
    setIsEditDialogOpen(true);
  }, []);

  const closeEditDialog = useCallback(() => {
    setIsEditDialogOpen(false);
    setSelectedAccount(null);
    setSubmitError('');
    handleModalCleanup();
  }, [handleModalCleanup]);

  const openDeleteDialog = useCallback((account: ChartOfAccount) => {
    setSelectedAccount(account);
    setIsDeleteDialogOpen(true);
  }, []);

  const closeDeleteDialog = useCallback(() => {
    setIsDeleteDialogOpen(false);
    setSelectedAccount(null);
    handleModalCleanup();
  }, [handleModalCleanup]);

  const openBankLinkDialog = useCallback((account: ChartOfAccount) => {
    setSelectedAccount(account);
    setIsBankLinkDialogOpen(true);
  }, []);

  const closeBankLinkDialog = useCallback((refresh?: boolean) => {
    setIsBankLinkDialogOpen(false);
    setSelectedAccount(null);
    handleModalCleanup();
    if (refresh) {
      toast.success(common('success'), {
        description: 'Bank account linked successfully',
      });
      refetch();
    }
  }, [common, refetch, handleModalCleanup]);

  return {
    // Data
    data,
    error,
    isLoading,
    isFetching,

    // ✅ Advanced pagination state
    page: state.page,
    setPage: handlePageChange,
    limit: state.limit,
    searchQuery: state.search,
    activeTab,
    sortBy: state.sortBy,
    sortOrder: state.sortOrder,
    goToPage,
    setGoToPage,

    // Advanced pagination actions
    paginationState: state,
    paginationActions: actions,

    // Dialog states
    isAddDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    isBankLinkDialogOpen,

    // Form states
    accountCode,
    setAccountCode,
    accountName,
    setAccountName,
    accountType,
    setAccountType,
    accountDescription,
    setAccountDescription,
    accountIsActive,
    setAccountIsActive,
    selectedAccount,
    submitError,
    setSubmitError,
    isSubmitting,
    setIsSubmitting,

    // Handlers
    handleSearch,
    handleTabChange,
    handleSort,
    handlePageSizeChange,
    handleGoToPage,

    // Dialog handlers
    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openBankLinkDialog,
    closeBankLinkDialog,

    // API mutations
    createAccount,
    updateAccount,
    deleteAccount,
    refetch,
  };
}
