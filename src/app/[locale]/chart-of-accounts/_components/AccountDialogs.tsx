'use client';

import { useTranslations } from 'next-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog-fixed';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AccountType } from '@/types/account';
import type { ChartOfAccount } from '../_hooks/useChartOfAccounts';

interface AccountDialogsProps {
  // Add Dialog
  isAddDialogOpen: boolean;
  onCloseAddDialog: () => void;
  onSaveAccount: (e: React.FormEvent) => void;

  // Edit Dialog
  isEditDialogOpen: boolean;
  onCloseEditDialog: () => void;
  onUpdateAccount: (e: React.FormEvent) => void;

  // Delete Dialog
  isDeleteDialogOpen: boolean;
  onCloseDeleteDialog: () => void;
  onDeleteAccount: () => void;
  selectedAccount: ChartOfAccount | null;

  // Form state
  accountCode: string;
  setAccountCode: (value: string) => void;
  accountName: string;
  setAccountName: (value: string) => void;
  accountType: AccountType;
  setAccountType: (value: AccountType) => void;
  accountDescription: string;
  setAccountDescription: (value: string) => void;
  accountIsActive: boolean;
  setAccountIsActive: (value: boolean) => void;
  submitError: string;
  isSubmitting: boolean;
}

export function AccountDialogs({
  isAddDialogOpen,
  onCloseAddDialog,
  onSaveAccount,
  isEditDialogOpen,
  onCloseEditDialog,
  onUpdateAccount,
  isDeleteDialogOpen,
  onCloseDeleteDialog,
  onDeleteAccount,
  selectedAccount,
  accountCode,
  setAccountCode,
  accountName,
  setAccountName,
  accountType,
  setAccountType,
  accountDescription,
  setAccountDescription,
  accountIsActive,
  setAccountIsActive,
  submitError,
  isSubmitting,
}: AccountDialogsProps) {
  const common = useTranslations('common');

  // Enhanced close handlers that prevent event bubbling
  const handleCloseAddDialog = (open: boolean) => {
    if (!open && !isSubmitting) {
      onCloseAddDialog();
    }
  };

  const handleCloseEditDialog = (open: boolean) => {
    if (!open && !isSubmitting) {
      onCloseEditDialog();
    }
  };

  const handleCloseDeleteDialog = (open: boolean) => {
    if (!open) {
      onCloseDeleteDialog();
    }
  };

  return (
    <>
      {/* Add Account Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={handleCloseAddDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Account</DialogTitle>
            <DialogDescription>
              Create a new chart of accounts entry
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={onSaveAccount}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="add-account_code" className="text-right">
                  Code
                </Label>
                <Input
                  id="add-account_code"
                  value={accountCode}
                  onChange={(e) => setAccountCode(e.target.value)}
                  className="col-span-3"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="add-account_name" className="text-right">
                  Name
                </Label>
                <Input
                  id="add-account_name"
                  value={accountName}
                  onChange={(e) => setAccountName(e.target.value)}
                  className="col-span-3"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="add-account_type" className="text-right">
                  Type
                </Label>
                <Select
                  value={accountType}
                  onValueChange={(value) => setAccountType(value as AccountType)}
                  required
                  disabled={isSubmitting}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select account type" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Asset Types */}
                    <SelectItem value="Asset">Asset (General)</SelectItem>
                    <SelectItem value="CurrentAsset">Current Asset</SelectItem>
                    <SelectItem value="FixedAsset">Fixed Asset</SelectItem>
                    <SelectItem value="IntangibleAsset">Intangible Asset</SelectItem>
                    <SelectItem value="OtherAsset">Other Asset</SelectItem>

                    {/* Liability Types */}
                    <SelectItem value="Liability">Liability (General)</SelectItem>
                    <SelectItem value="CurrentLiability">Current Liability</SelectItem>
                    <SelectItem value="LongTermLiability">Long-term Liability</SelectItem>
                    <SelectItem value="OtherLiability">Other Liability</SelectItem>

                    {/* Equity Types */}
                    <SelectItem value="Equity">Equity (General)</SelectItem>
                    <SelectItem value="CommonStock">Common Stock</SelectItem>
                    <SelectItem value="RetainedEarnings">Retained Earnings</SelectItem>
                    <SelectItem value="OtherEquity">Other Equity</SelectItem>

                    {/* Revenue Types */}
                    <SelectItem value="Revenue">Revenue (General)</SelectItem>
                    <SelectItem value="OperatingRevenue">Operating Revenue</SelectItem>
                    <SelectItem value="NonOperatingRevenue">Non-operating Revenue</SelectItem>
                    <SelectItem value="OtherRevenue">Other Revenue</SelectItem>

                    {/* Expense Types */}
                    <SelectItem value="Expense">Expense (General)</SelectItem>
                    <SelectItem value="OperatingExpense">Operating Expense</SelectItem>
                    <SelectItem value="NonOperatingExpense">Non-operating Expense</SelectItem>
                    <SelectItem value="OtherExpense">Other Expense</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="add-account_description" className="text-right">
                  Description
                </Label>
                <Input
                  id="add-account_description"
                  value={accountDescription}
                  onChange={(e) => setAccountDescription(e.target.value)}
                  className="col-span-3"
                  disabled={isSubmitting}
                />
              </div>
              {submitError && <p className="col-span-4 text-red-600 text-sm">{`Error: ${submitError}`}</p>}
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline" onClick={onCloseAddDialog}>
                  {common('cancel')}
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : common('save')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Account Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={handleCloseEditDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Account</DialogTitle>
            <DialogDescription>
              Update the account information
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={onUpdateAccount}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_code" className="text-right">
                  Code
                </Label>
                <Input
                  id="edit-account_code"
                  value={accountCode}
                  onChange={(e) => setAccountCode(e.target.value)}
                  className="col-span-3"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_name" className="text-right">
                  Name
                </Label>
                <Input
                  id="edit-account_name"
                  value={accountName}
                  onChange={(e) => setAccountName(e.target.value)}
                  className="col-span-3"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_type" className="text-right">
                  Type
                </Label>
                <Select
                  value={accountType}
                  onValueChange={(value) => setAccountType(value as AccountType)}
                  required
                  disabled={isSubmitting}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select account type" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Same options as add dialog */}
                    <SelectItem value="Asset">Asset (General)</SelectItem>
                    <SelectItem value="CurrentAsset">Current Asset</SelectItem>
                    <SelectItem value="FixedAsset">Fixed Asset</SelectItem>
                    <SelectItem value="IntangibleAsset">Intangible Asset</SelectItem>
                    <SelectItem value="OtherAsset">Other Asset</SelectItem>
                    <SelectItem value="Liability">Liability (General)</SelectItem>
                    <SelectItem value="CurrentLiability">Current Liability</SelectItem>
                    <SelectItem value="LongTermLiability">Long-term Liability</SelectItem>
                    <SelectItem value="OtherLiability">Other Liability</SelectItem>
                    <SelectItem value="Equity">Equity (General)</SelectItem>
                    <SelectItem value="CommonStock">Common Stock</SelectItem>
                    <SelectItem value="RetainedEarnings">Retained Earnings</SelectItem>
                    <SelectItem value="OtherEquity">Other Equity</SelectItem>
                    <SelectItem value="Revenue">Revenue (General)</SelectItem>
                    <SelectItem value="OperatingRevenue">Operating Revenue</SelectItem>
                    <SelectItem value="NonOperatingRevenue">Non-operating Revenue</SelectItem>
                    <SelectItem value="OtherRevenue">Other Revenue</SelectItem>
                    <SelectItem value="Expense">Expense (General)</SelectItem>
                    <SelectItem value="OperatingExpense">Operating Expense</SelectItem>
                    <SelectItem value="NonOperatingExpense">Non-operating Expense</SelectItem>
                    <SelectItem value="OtherExpense">Other Expense</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_description" className="text-right">
                  Description
                </Label>
                <Input
                  id="edit-account_description"
                  value={accountDescription}
                  onChange={(e) => setAccountDescription(e.target.value)}
                  className="col-span-3"
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_status" className="text-right">
                  Status
                </Label>
                <Select
                  value={accountIsActive ? "active" : "inactive"}
                  onValueChange={(value) => setAccountIsActive(value === "active")}
                  disabled={isSubmitting}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {submitError && <p className="col-span-4 text-red-600 text-sm">{`Error: ${submitError}`}</p>}
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onCloseEditDialog}>
                {common('cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Updating...' : common('update')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Account Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={handleCloseDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Account</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedAccount && `Are you sure you want to delete account "${(selectedAccount as any).code || selectedAccount.account_code} - ${(selectedAccount as any).name || selectedAccount.account_name}"? This action cannot be undone.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button variant="outline" onClick={onCloseDeleteDialog}>
              {common('cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={onDeleteAccount}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Deleting...' : common('delete')}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
