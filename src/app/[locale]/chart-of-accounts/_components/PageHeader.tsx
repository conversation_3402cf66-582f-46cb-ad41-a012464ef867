'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, FileText, Wallet, Building2, Search } from 'lucide-react';
import { useOrganizationPermissions } from '@/hooks/useOrganizationPermissions';

interface PageHeaderProps {
  onAddAccount: () => void;
  searchQuery: string;
  onSearch: (query: string) => void;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function PageHeader({
  onAddAccount,
  searchQuery,
  onSearch,
  activeTab,
  onTabChange,
}: PageHeaderProps) {
  const common = useTranslations('common');
  const { canCreateAccount, permissionsLoading } = useOrganizationPermissions();

  // Helper functions to determine if subtabs should be shown
  const shouldShowAssetSubtabs = () => {
    return ['Asset', 'CurrentAsset', 'FixedAsset', 'IntangibleAsset', 'OtherAsset'].includes(activeTab);
  };

  const shouldShowLiabilitySubtabs = () => {
    return ['Liability', 'CurrentLiability', 'LongTermLiability', 'OtherLiability'].includes(activeTab);
  };

  const shouldShowEquitySubtabs = () => {
    return ['Equity', 'CommonStock', 'RetainedEarnings', 'OtherEquity'].includes(activeTab);
  };

  const shouldShowRevenueSubtabs = () => {
    return ['Revenue', 'OperatingRevenue', 'NonOperatingRevenue', 'OtherRevenue'].includes(activeTab);
  };

  const shouldShowExpenseSubtabs = () => {
    return ['Expense', 'OperatingExpense', 'NonOperatingExpense', 'OtherExpense'].includes(activeTab);
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Title and Add Button */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Chart of Accounts</h1>
        {!permissionsLoading && (
          canCreateAccount() ? (
            <Button onClick={onAddAccount}>
              <Plus className="mr-2 h-4 w-4" />
              Add New Account
            </Button>
          ) : (
            <div className="relative inline-block group">
              <Button disabled>
                <Plus className="mr-2 h-4 w-4" />
                Add New Account
              </Button>
              <div className="absolute bottom-full mb-2 right-0 bg-black text-white text-xs rounded py-1 px-2 whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                Staff permission required
              </div>
            </div>
          )
        )}
      </div>

      {/* Related Links */}
      <div className="flex flex-wrap gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href="/journal-entries">
            <FileText className="mr-2 h-4 w-4" />
            Journal Entries
          </Link>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href="/banking">
            <Wallet className="mr-2 h-4 w-4" />
            Banking
          </Link>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href="/assets">
            <Building2 className="mr-2 h-4 w-4" />
            Assets
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        {/* Search */}
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search accounts..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => onSearch(e.target.value)}
          />
        </div>

        {/* Account Type Tabs */}
        <Tabs value={activeTab} onValueChange={onTabChange} className="w-full sm:w-auto">
          <TabsList className="grid grid-cols-6">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="Asset">Assets</TabsTrigger>
            <TabsTrigger value="Liability">Liabilities</TabsTrigger>
            <TabsTrigger value="Equity">Equity</TabsTrigger>
            <TabsTrigger value="Revenue">Revenue</TabsTrigger>
            <TabsTrigger value="Expense">Expenses</TabsTrigger>
          </TabsList>

          {/* Asset Subtypes */}
          {shouldShowAssetSubtabs() && (
            <TabsList className="mt-2">
              <TabsTrigger value="CurrentAsset">Current</TabsTrigger>
              <TabsTrigger value="FixedAsset">Fixed</TabsTrigger>
              <TabsTrigger value="IntangibleAsset">Intangible</TabsTrigger>
              <TabsTrigger value="OtherAsset">Other</TabsTrigger>
            </TabsList>
          )}

          {/* Liability Subtypes */}
          {shouldShowLiabilitySubtabs() && (
            <TabsList className="mt-2">
              <TabsTrigger value="CurrentLiability">Current</TabsTrigger>
              <TabsTrigger value="LongTermLiability">Long-term</TabsTrigger>
              <TabsTrigger value="OtherLiability">Other</TabsTrigger>
            </TabsList>
          )}

          {/* Equity Subtypes */}
          {shouldShowEquitySubtabs() && (
            <TabsList className="mt-2">
              <TabsTrigger value="CommonStock">Common Stock</TabsTrigger>
              <TabsTrigger value="RetainedEarnings">Retained Earnings</TabsTrigger>
              <TabsTrigger value="OtherEquity">Other</TabsTrigger>
            </TabsList>
          )}

          {/* Revenue Subtypes */}
          {shouldShowRevenueSubtabs() && (
            <TabsList className="mt-2">
              <TabsTrigger value="OperatingRevenue">Operating</TabsTrigger>
              <TabsTrigger value="NonOperatingRevenue">Non-operating</TabsTrigger>
              <TabsTrigger value="OtherRevenue">Other</TabsTrigger>
            </TabsList>
          )}

          {/* Expense Subtypes */}
          {shouldShowExpenseSubtabs() && (
            <TabsList className="mt-2">
              <TabsTrigger value="OperatingExpense">Operating</TabsTrigger>
              <TabsTrigger value="NonOperatingExpense">Non-operating</TabsTrigger>
              <TabsTrigger value="OtherExpense">Other</TabsTrigger>
            </TabsList>
          )}
        </Tabs>
      </div>
    </div>
  );
}
