'use client';

import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useGetLinkedBankAccountQuery } from '@/redux/services/chartOfAccountsApi';
import { Link2, AlertCircle } from 'lucide-react';

interface BankAccountStatusProps {
  accountId: string;
  onCreateLink: () => void;
}

export function BankAccountStatus({ accountId, onCreateLink }: BankAccountStatusProps) {
  const common = useTranslations('common');

  const { data: bankAccount, isLoading, error } = useGetLinkedBankAccountQuery(accountId, {
    // Skip the query if no accountId is provided
    skip: !accountId
  });

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center space-x-2">
        <AlertCircle className="h-4 w-4 text-red-500" />
        <span className="text-sm text-red-500">Error</span>
      </div>
    );
  }

  if (bankAccount) {
    return (
      <div className="flex items-center space-x-2">
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Link2 className="mr-1 h-3 w-3" />
          Linked
        </Badge>
        <span className="text-xs text-gray-500 truncate max-w-[100px]">
          {bankAccount.bank_name || 'Bank Account'}
        </span>
      </div>
    );
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onCreateLink}
      className="h-7 px-2 text-xs"
    >
      <Link2 className="mr-1 h-3 w-3" />
      Link
    </Button>
  );
}
