'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, MapPin, Star, Navigation, Smartphone, Monitor } from 'lucide-react';
import { ContextBreadcrumb, ContextBreadcrumbCompact } from '@/components/layout/ContextBreadcrumb';
import { useDefaultOrganizationBranch } from '@/hooks/useDefaultOrganizationBranch';

export default function HeaderDemoPage() {
  const {
    getSelectedOrganization,
    getSelectedBranch,
    defaultOrganizationId,
    defaultBranchId,
    isUsingDefaults,
  } = useDefaultOrganizationBranch();

  const selectedOrganization = getSelectedOrganization();
  const selectedBranch = getSelectedBranch();

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Enhanced Navigation Header Demo</h1>
        <p className="text-muted-foreground mt-2">
          This page demonstrates the enhanced navigation header with default organization and branch functionality.
        </p>
      </div>

      {/* Current Context Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Navigation className="h-5 w-5" />
            Current Navigation Context
          </CardTitle>
          <CardDescription>
            Your current organization and branch context as shown in the header
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Organization */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Organization
              </h4>
              {selectedOrganization ? (
                <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                  <span className="font-medium">{selectedOrganization.name}</span>
                  {defaultOrganizationId === selectedOrganization.id && (
                    <Badge variant="secondary" className="text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      Default
                    </Badge>
                  )}
                </div>
              ) : (
                <div className="p-3 bg-muted/30 rounded-lg text-muted-foreground text-sm">
                  No organization selected
                </div>
              )}
            </div>

            {/* Branch */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Branch
              </h4>
              {selectedBranch ? (
                <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                  <span className="font-medium">{selectedBranch.name}</span>
                  {defaultBranchId === selectedBranch.id && (
                    <Badge variant="secondary" className="text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      Default
                    </Badge>
                  )}
                </div>
              ) : (
                <div className="p-3 bg-muted/30 rounded-lg text-muted-foreground text-sm">
                  No branch selected
                </div>
              )}
            </div>
          </div>

          {/* Status Indicators */}
          <div className="pt-4 border-t">
            <h4 className="font-medium text-sm mb-2">Status:</h4>
            <div className="flex flex-wrap gap-2">
              <Badge variant={isUsingDefaults ? "default" : "outline"}>
                {isUsingDefaults ? "Using Defaults" : "Manual Selection"}
              </Badge>
              <Badge variant={selectedOrganization ? "default" : "outline"}>
                {selectedOrganization ? "Organization Set" : "No Organization"}
              </Badge>
              <Badge variant={selectedBranch ? "default" : "outline"}>
                {selectedBranch ? "Branch Set" : "No Branch"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Breadcrumb Examples */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Breadcrumb Components
          </CardTitle>
          <CardDescription>
            Different breadcrumb styles showing organization/branch context
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Full Breadcrumb */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Full Breadcrumb (Desktop):</h4>
            <div className="p-4 bg-muted/30 rounded-lg">
              <ContextBreadcrumb 
                currentPage="Demo Page"
                customItems={[
                  {
                    label: 'Settings',
                    href: '/settings',
                    icon: <Building2 className="h-4 w-4" />
                  }
                ]}
              />
            </div>
          </div>

          {/* Compact Breadcrumb */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Compact Breadcrumb (Mobile):</h4>
            <div className="p-4 bg-muted/30 rounded-lg">
              <ContextBreadcrumbCompact currentPage="Demo Page" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Header Features */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Header Features</CardTitle>
          <CardDescription>
            Key features of the enhanced navigation header
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Desktop Features */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Monitor className="h-4 w-4" />
                Desktop Header Features
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Prominent organization and branch display with default indicators</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Dropdown menu for quick organization/branch switching</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>One-click default setting from the header dropdown</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Full context management dialog for detailed settings</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Tooltip showing current context and default status</span>
                </li>
              </ul>
            </div>

            {/* Mobile Features */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                Mobile Optimizations
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Compact display that fits in mobile header space</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Touch-friendly dropdown with larger tap targets</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Abbreviated organization/branch names with truncation</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Compact breadcrumb component for page navigation</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                  <span>Default status badges that don't clutter the interface</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Usage Instructions */}
          <div className="pt-4 border-t">
            <h4 className="font-medium mb-2">How to Use:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
              <li>Look at the header - you'll see your current organization and branch</li>
              <li>Click on the context display to open the dropdown menu</li>
              <li>Use the dropdown to quickly switch organizations or branches</li>
              <li>Click the star icons to set items as your defaults</li>
              <li>Use "Manage Context & Defaults" for full control</li>
              <li>Default selections will be automatically chosen on future logins</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
