import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { configureStore } from '@reduxjs/toolkit'
import { bankingApi, type BankAccountsQueryParams } from '@/redux/services/bankingApi'

// Mock data
const mockBankAccounts = [
  {
    id: 'account-1',
    restaurant_id: 'restaurant-1',
    accountName: 'Test Checking Account',
    bankName: 'Test Bank',
    accountType: 'Checking',
    accountNumber: '**********',
    routingNumber: '*********',
    balance: 10000,
    isActive: true,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: 'account-2',
    restaurant_id: 'restaurant-1',
    accountName: 'Test Savings Account',
    bankName: 'Test Bank',
    accountType: 'Savings',
    accountNumber: '**********',
    routingNumber: '*********',
    balance: 25000,
    isActive: true,
    created_at: '2023-02-01T00:00:00Z',
    updated_at: '2023-02-01T00:00:00Z',
  },
]

const mockBankAccountsResponse = {
  accounts: mockBankAccounts,
  pagination: {
    total: 2,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNextPage: false,
    hasPreviousPage: false,
  },
}

// Setup MSW server
const server = setupServer(
  // Get bank accounts with filters, sort, and pagination
  rest.get('/api/banking/accounts', (req, res, ctx) => {
    const url = new URL(req.url)
    const search = url.searchParams.get('search')
    const accountType = url.searchParams.get('accountType')
    const isActive = url.searchParams.get('isActive')
    const bankName = url.searchParams.get('bankName')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const sortBy = url.searchParams.get('sortBy') || 'accountName'
    const sortOrder = url.searchParams.get('sortOrder') || 'asc'

    let filteredAccounts = [...mockBankAccounts]

    // Apply search filter
    if (search) {
      filteredAccounts = filteredAccounts.filter(account =>
        account.accountName.toLowerCase().includes(search.toLowerCase()) ||
        account.bankName.toLowerCase().includes(search.toLowerCase())
      )
    }

    // Apply account type filter
    if (accountType) {
      filteredAccounts = filteredAccounts.filter(account => account.accountType === accountType)
    }

    // Apply active status filter
    if (isActive !== null) {
      const activeStatus = isActive === 'true'
      filteredAccounts = filteredAccounts.filter(account => account.isActive === activeStatus)
    }

    // Apply bank name filter
    if (bankName) {
      filteredAccounts = filteredAccounts.filter(account =>
        account.bankName.toLowerCase().includes(bankName.toLowerCase())
      )
    }

    // Apply sorting
    filteredAccounts.sort((a, b) => {
      let aValue: any = a[sortBy as keyof typeof a]
      let bValue: any = b[sortBy as keyof typeof b]

      if (sortBy === 'balance') {
        aValue = Number(aValue)
        bValue = Number(bValue)
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedAccounts = filteredAccounts.slice(startIndex, endIndex)

    const response = {
      accounts: paginatedAccounts,
      pagination: {
        total: filteredAccounts.length,
        page,
        limit,
        totalPages: Math.ceil(filteredAccounts.length / limit),
        hasNextPage: endIndex < filteredAccounts.length,
        hasPreviousPage: page > 1,
      },
    }

    return res(ctx.json(response))
  }),

  // Get single bank account
  rest.get('/api/banking/accounts/:id', (req, res, ctx) => {
    const { id } = req.params
    const account = mockBankAccounts.find(a => a.id === id)
    
    if (!account) {
      return res(ctx.status(404), ctx.json({ error: 'Bank account not found' }))
    }

    return res(ctx.json(account))
  }),

  // Create bank account
  rest.post('/api/banking/accounts', (req, res, ctx) => {
    return res(ctx.json({
      id: 'new-account-id',
      ...req.body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }))
  }),

  // Update bank account
  rest.put('/api/banking/accounts/:id', (req, res, ctx) => {
    const { id } = req.params
    const account = mockBankAccounts.find(a => a.id === id)
    
    if (!account) {
      return res(ctx.status(404), ctx.json({ error: 'Bank account not found' }))
    }

    return res(ctx.json({
      ...account,
      ...req.body,
      updated_at: new Date().toISOString(),
    }))
  }),

  // Delete bank account
  rest.delete('/api/banking/accounts/:id', (req, res, ctx) => {
    const { id } = req.params
    const account = mockBankAccounts.find(a => a.id === id)
    
    if (!account) {
      return res(ctx.status(404), ctx.json({ error: 'Bank account not found' }))
    }

    return res(ctx.status(204))
  }),

  // Batch update bank accounts
  rest.post('/api/banking/accounts/batch-update', (req, res, ctx) => {
    return res(ctx.json({ updated_count: 2 }))
  }),

  // Batch delete bank accounts
  rest.post('/api/banking/accounts/batch-delete', (req, res, ctx) => {
    return res(ctx.json({ deleted_count: 2 }))
  }),

  // Upload bank statement
  rest.post('/api/banking/accounts/:id/statements', (req, res, ctx) => {
    return res(ctx.json({
      success: true,
      message: 'Statement uploaded successfully',
    }))
  }),

  // Check COA linked
  rest.get('/api/banking/check-coa-linked', (req, res, ctx) => {
    return res(ctx.json({
      isLinked: false,
      bankAccount: null,
    }))
  }),
)

// Create test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      [bankingApi.reducerPath]: bankingApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(bankingApi.middleware),
  })
}

describe('Banking API', () => {
  beforeEach(() => {
    server.listen()
  })

  afterEach(() => {
    server.resetHandlers()
  })

  afterEach(() => {
    server.close()
  })

  describe('getBankAccounts', () => {
    it('fetches bank accounts without parameters', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccounts.initiate()
      )

      expect(result.data).toEqual(mockBankAccountsResponse)
      expect(result.data?.accounts).toHaveLength(2)
      expect(result.data?.pagination.total).toBe(2)
    })

    it('fetches bank accounts with search parameter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccounts.initiate({ search: 'Checking' })
      )

      expect(result.data?.accounts).toHaveLength(1)
      expect(result.data?.accounts[0].accountName).toBe('Test Checking Account')
    })

    it('fetches bank accounts with account type filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccounts.initiate({ accountType: 'Savings' })
      )

      expect(result.data?.accounts).toHaveLength(1)
      expect(result.data?.accounts[0].accountType).toBe('Savings')
    })

    it('fetches bank accounts with active status filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccounts.initiate({ isActive: true })
      )

      expect(result.data?.accounts).toHaveLength(2)
      expect(result.data?.accounts.every(account => account.isActive)).toBe(true)
    })

    it('fetches bank accounts with bank name filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccounts.initiate({ bankName: 'Test Bank' })
      )

      expect(result.data?.accounts).toHaveLength(2)
      expect(result.data?.accounts.every(account => account.bankName === 'Test Bank')).toBe(true)
    })

    it('fetches bank accounts with pagination', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccounts.initiate({ page: 1, limit: 1 })
      )

      expect(result.data?.accounts).toHaveLength(1)
      expect(result.data?.pagination.page).toBe(1)
      expect(result.data?.pagination.limit).toBe(1)
      expect(result.data?.pagination.totalPages).toBe(2)
    })

    it('fetches bank accounts with sorting', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccounts.initiate({
          sortBy: 'balance',
          sortOrder: 'desc'
        })
      )

      expect(result.data?.accounts).toHaveLength(2)
      expect(result.data?.accounts[0].balance).toBe(25000)
      expect(result.data?.accounts[1].balance).toBe(10000)
    })
  })

  describe('getBankAccountById', () => {
    it('fetches a single bank account by ID', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccountById.initiate('account-1')
      )

      expect(result.data).toEqual(mockBankAccounts[0])
    })

    it('returns error for non-existent bank account', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.getBankAccountById.initiate('non-existent')
      )

      expect(result.error).toBeDefined()
    })
  })

  describe('createBankAccount', () => {
    it('creates a new bank account', async () => {
      const store = createTestStore()
      const newAccount = {
        accountName: 'New Account',
        bankName: 'New Bank',
        accountType: 'Checking',
        accountNumber: '*********1',
        routingNumber: '*********',
        balance: 5000,
        isActive: true,
      }
      
      const result = await store.dispatch(
        bankingApi.endpoints.createBankAccount.initiate(newAccount)
      )

      expect(result.data).toMatchObject(newAccount)
      expect(result.data?.id).toBe('new-account-id')
    })
  })

  describe('updateBankAccount', () => {
    it('updates an existing bank account', async () => {
      const store = createTestStore()
      const updateData = { accountName: 'Updated Account Name' }
      
      const result = await store.dispatch(
        bankingApi.endpoints.updateBankAccount.initiate({
          id: 'account-1',
          body: updateData
        })
      )

      expect(result.data?.accountName).toBe('Updated Account Name')
    })
  })

  describe('deleteBankAccount', () => {
    it('deletes a bank account', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.deleteBankAccount.initiate('account-1')
      )

      expect(result.data).toBeUndefined() // 204 No Content
    })
  })

  describe('batchUpdateBankAccounts', () => {
    it('updates multiple bank accounts', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.batchUpdateBankAccounts.initiate({
          account_ids: ['account-1', 'account-2'],
          isActive: false
        })
      )

      expect(result.data?.updated_count).toBe(2)
    })
  })

  describe('batchDeleteBankAccounts', () => {
    it('deletes multiple bank accounts', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.batchDeleteBankAccounts.initiate({
          ids: ['account-1', 'account-2']
        })
      )

      expect(result.data?.deleted_count).toBe(2)
    })
  })

  describe('uploadBankStatement', () => {
    it('uploads a bank statement', async () => {
      const store = createTestStore()
      const formData = new FormData()
      formData.append('file', new File(['test'], 'statement.csv'))
      
      const result = await store.dispatch(
        bankingApi.endpoints.uploadBankStatement.initiate({
          accountId: 'account-1',
          formData
        })
      )

      expect(result.data?.success).toBe(true)
      expect(result.data?.message).toBe('Statement uploaded successfully')
    })
  })

  describe('checkCoaLinked', () => {
    it('checks if COA is linked to bank account', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        bankingApi.endpoints.checkCoaLinked.initiate('coa-1')
      )

      expect(result.data?.isLinked).toBe(false)
      expect(result.data?.bankAccount).toBe(null)
    })
  })
})
