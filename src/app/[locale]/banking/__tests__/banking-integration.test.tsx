import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'

describe('Banking Page Integration Tests', () => {
  let browser: Browser
  let page: Page
  const baseUrl = process.env.TEST_BASE_URL || 'http://localhost:3001'

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: process.env.CI === 'true',
      slowMo: process.env.CI === 'true' ? 0 : 50,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    })
  })

  afterAll(async () => {
    if (browser) {
      await browser.close()
    }
  })

  beforeEach(async () => {
    page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })
    
    // Mock console to reduce noise in tests
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.error('Page error:', msg.text())
      }
    })
  })

  afterEach(async () => {
    if (page) {
      await page.close()
    }
  })

  describe('Banking List Page', () => {
    it('should load the banking page successfully', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      
      // Wait for the page to load
      await page.waitForSelector('[data-testid="bank-account-list-client"]', { timeout: 10000 })
      
      // Check if the page title is present
      const title = await page.$eval('h1', el => el.textContent)
      expect(title).toContain('Bank')
    })

    it('should display filters section', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Check for filters
      const searchInput = await page.$('input[placeholder*="search"]')
      expect(searchInput).toBeTruthy()
      
      // Check for account type filter
      const accountTypeFilter = await page.$('[data-testid="select"]')
      expect(accountTypeFilter).toBeTruthy()
    })

    it('should handle search functionality', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Find search input
      const searchInput = await page.$('input[placeholder*="search"]')
      if (searchInput) {
        await searchInput.type('Test Account')
        
        // Wait for search to be processed
        await page.waitForTimeout(500)
        
        // Verify search input has value
        const searchValue = await page.$eval('input[placeholder*="search"]', el => (el as HTMLInputElement).value)
        expect(searchValue).toBe('Test Account')
      }
    })

    it('should toggle between grid and list views', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for view toggle button
      const viewToggleButton = await page.$('button:has-text("Grid"), button:has-text("List")')
      if (viewToggleButton) {
        await viewToggleButton.click()
        await page.waitForTimeout(300)
        
        // Verify the view has changed
        const buttonText = await viewToggleButton.textContent()
        expect(buttonText).toBeTruthy()
      }
    })

    it('should display bank account cards or table rows', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Wait for accounts to load (either cards or table)
      try {
        await page.waitForSelector('[data-testid="account-card"], table tbody tr', { timeout: 5000 })
        
        // Check if we have either account cards or table rows
        const accountCards = await page.$$('[data-testid="account-card"]')
        const tableRows = await page.$$('table tbody tr')
        
        expect(accountCards.length > 0 || tableRows.length > 0).toBe(true)
      } catch (error) {
        // If no accounts are found, check for empty state
        const emptyMessage = await page.$('text="No accounts found"')
        expect(emptyMessage).toBeTruthy()
      }
    })

    it('should handle pagination when multiple pages exist', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for pagination controls
      const paginationNext = await page.$('button:has-text("Next"), button:has-text("next")')
      const paginationPrev = await page.$('button:has-text("Previous"), button:has-text("previous")')
      
      // If pagination exists, test it
      if (paginationNext || paginationPrev) {
        if (paginationNext && !(await paginationNext.isDisabled())) {
          await paginationNext.click()
          await page.waitForTimeout(500)
        }
      }
      
      // Test passes if no errors occur
      expect(true).toBe(true)
    })

    it('should open add account dialog', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for add account button
      const addButton = await page.$('button:has-text("Add"), button:has-text("add")')
      if (addButton) {
        await addButton.click()
        
        // Wait for dialog to appear
        await page.waitForSelector('[data-testid="dialog"], .dialog', { timeout: 2000 })
        
        const dialog = await page.$('[data-testid="dialog"], .dialog')
        expect(dialog).toBeTruthy()
      }
    })

    it('should handle account type filtering', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for account type filter
      const accountTypeFilter = await page.$('select, [data-testid="select"]')
      if (accountTypeFilter) {
        await accountTypeFilter.click()
        await page.waitForTimeout(300)
        
        // Look for account type options
        const checkingOption = await page.$('option[value="Checking"], [value="Checking"]')
        if (checkingOption) {
          await checkingOption.click()
          await page.waitForTimeout(500)
        }
      }
      
      // Test passes if no errors occur
      expect(true).toBe(true)
    })

    it('should handle sorting by clicking column headers', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for sortable column headers
      const nameHeader = await page.$('th:has-text("Name"), th:has-text("name"), th:has-text("Account")')
      if (nameHeader) {
        await nameHeader.click()
        await page.waitForTimeout(300)
        
        // Verify sorting indicator appears
        const headerText = await nameHeader.textContent()
        expect(headerText).toBeTruthy()
      }
    })

    it('should handle status filtering', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for status filter
      const statusFilter = await page.$('select:has(option[value="true"]), [data-testid="select"]:has([value="true"])')
      if (statusFilter) {
        await statusFilter.click()
        await page.waitForTimeout(300)
        
        // Look for active option
        const activeOption = await page.$('option[value="true"], [value="true"]')
        if (activeOption) {
          await activeOption.click()
          await page.waitForTimeout(500)
        }
      }
      
      // Test passes if no errors occur
      expect(true).toBe(true)
    })

    it('should clear filters when clear button is clicked', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Set some filters first
      const searchInput = await page.$('input[placeholder*="search"]')
      if (searchInput) {
        await searchInput.type('test')
      }
      
      // Click clear button
      const clearButton = await page.$('button:has-text("Clear"), button:has-text("clear")')
      if (clearButton) {
        await clearButton.click()
        await page.waitForTimeout(300)
        
        // Verify search input is cleared
        const searchValue = await page.$eval('input[placeholder*="search"]', 
          el => (el as HTMLInputElement).value)
        expect(searchValue).toBe('')
      }
    })

    it('should navigate to account details when clicking view action', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for view action links
      const viewLink = await page.$('a:has-text("View"), a:has-text("view")')
      if (viewLink) {
        const href = await viewLink.getAttribute('href')
        expect(href).toContain('/banking/accounts/')
      }
    })

    it('should handle page size changes', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for page size selector
      const pageSizeSelect = await page.$('select, [data-testid="select"]')
      if (pageSizeSelect) {
        // Try to change page size
        await pageSizeSelect.click()
        await page.waitForTimeout(300)
        
        // Look for page size options
        const option25 = await page.$('option[value="25"], [value="25"]')
        if (option25) {
          await option25.click()
          await page.waitForTimeout(500)
        }
      }
      
      // Test passes if no errors occur
      expect(true).toBe(true)
    })

    it('should display loading state initially', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      
      // Check for loading indicators immediately after navigation
      try {
        await page.waitForSelector('[data-testid="skeleton"], .loading, text="Loading"', { timeout: 1000 })
        const loadingElement = await page.$('[data-testid="skeleton"], .loading, text="Loading"')
        expect(loadingElement).toBeTruthy()
      } catch (error) {
        // If loading state is too fast to catch, that's also acceptable
        expect(true).toBe(true)
      }
    })

    it('should handle error states gracefully', async () => {
      // Test with a potentially invalid URL or simulate network error
      await page.goto(`${baseUrl}/en/banking`)
      
      // Wait for page to load
      await page.waitForSelector('[data-testid="bank-account-list-client"]', { timeout: 10000 })
      
      // Look for error messages or retry buttons
      const errorMessage = await page.$('text="Error", text="Failed", text="Retry"')
      const retryButton = await page.$('button:has-text("Retry"), button:has-text("retry")')
      
      // If error state exists, test retry functionality
      if (retryButton) {
        await retryButton.click()
        await page.waitForTimeout(1000)
      }
      
      // Test passes if page doesn't crash
      expect(true).toBe(true)
    })

    it('should be responsive on mobile viewport', async () => {
      // Set mobile viewport
      await page.setViewport({ width: 375, height: 667 })
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Check if page renders without horizontal scroll
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
      const viewportWidth = await page.evaluate(() => window.innerWidth)
      
      expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 20) // Allow small margin
    })

    it('should maintain state when navigating back', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Set a search filter
      const searchInput = await page.$('input[placeholder*="search"]')
      if (searchInput) {
        await searchInput.type('test search')
        await page.waitForTimeout(500)
      }
      
      // Navigate to another page and back
      await page.goto(`${baseUrl}/en/dashboard`)
      await page.waitForTimeout(500)
      await page.goBack()
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Check if search state is maintained (this depends on implementation)
      // For now, just verify page loads correctly
      const title = await page.$eval('h1', el => el.textContent)
      expect(title).toContain('Bank')
    })

    it('should handle batch operations', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for batch action controls
      const batchActionBar = await page.$('[data-testid="batch-action-bar"]')
      if (batchActionBar) {
        // Try to find and click a batch action button
        const statusButton = await page.$('button:has-text("Status"), button:has-text("Update Status")')
        if (statusButton) {
          await statusButton.click()
          
          // Wait for dialog to appear
          await page.waitForSelector('[data-testid="dialog"]', { timeout: 2000 })
          
          const dialog = await page.$('[data-testid="dialog"]')
          expect(dialog).toBeTruthy()
        }
      }
    })

    it('should handle statement upload functionality', async () => {
      await page.goto(`${baseUrl}/en/banking`)
      await page.waitForSelector('[data-testid="bank-account-list-client"]')
      
      // Look for upload statement action
      const uploadButton = await page.$('button:has-text("Upload"), button:has-text("Statement")')
      if (uploadButton) {
        await uploadButton.click()
        
        // Wait for upload dialog to appear
        await page.waitForSelector('[data-testid="upload-dialog"], .upload-form', { timeout: 2000 })
        
        const uploadDialog = await page.$('[data-testid="upload-dialog"], .upload-form')
        expect(uploadDialog).toBeTruthy()
      }
    })
  })
})
