'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useI18n } from '@/hooks/useI18n';
import {
  useGetBankAccountsQuery,
  useBatchUpdateBankAccountsMutation,
  useBatchDeleteBankAccountsMutation,
  type BankAccountsQueryParams
} from '@/redux/services/bankingApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  ArrowLeft,
  ChevronDown,
  Download,
  Edit,
  Eye,
  MoreHorizontal,
  Plus,
  Search,
  Trash,
  Upload,
  Grid,
  List,
  ChevronLeft,
  ChevronRight,
  Building2,
  CreditCard
} from 'lucide-react';
import {
  BatchSelectionProvider,
  BatchActionBar,
  BatchSelectionCheckbox,
  BatchSelectAll,
  useBatchSelection
} from '@/components/batch-selection';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { BankAccountFormDialog } from './BankAccountFormDialog';
import { StatementUploadForm } from './StatementUploadForm';

// Inner component that uses the batch selection hook
function BankAccountListContent() {
  // Get translations
  const t = useTranslations('banking');
  const { formatCurrency, formatDate } = useI18n();

  // State for filters and pagination
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [accountTypeFilter, setAccountTypeFilter] = useState<string>('');
  const [isActiveFilter, setIsActiveFilter] = useState<string>('');
  const [bankNameFilter, setBankNameFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortBy, setSortBy] = useState<string>('accountName');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // State for dialogs
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<any>(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [accountForUpload, setAccountForUpload] = useState<any>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<boolean | ''>('');

  // Modal cleanup function
  const cleanupModals = () => {
    // Force cleanup of any lingering modal overlays
    setTimeout(() => {
      const overlays = document.querySelectorAll('[data-radix-dialog-overlay], [data-radix-alert-dialog-overlay]');
      overlays.forEach(overlay => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      });

      // Re-enable body interactions
      document.body.style.pointerEvents = '';
      document.body.style.overflow = '';
      document.body.classList.remove('overflow-hidden');
    }, 100);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupModals();
    };
  }, []);

  // Build query parameters
  const queryParams: BankAccountsQueryParams = {
    search: searchQuery || undefined,
    accountType: accountTypeFilter || undefined,
    isActive: isActiveFilter ? isActiveFilter === 'true' : undefined,
    bankName: bankNameFilter || undefined,
    page: currentPage,
    limit: pageSize,
    sortBy,
    sortOrder,
  };

  // RTK Query hooks
  const { data: accountsResponse, isLoading: isLoadingAccounts, error } = useGetBankAccountsQuery(queryParams);
  const [batchUpdateAccounts] = useBatchUpdateBankAccountsMutation();
  const [batchDeleteAccounts] = useBatchDeleteBankAccountsMutation();

  const { toast } = useToast();

  // Get batch selection functions - now this is inside the provider
  const { isSelected, getSelectedIds } = useBatchSelection();

  // Extract accounts and pagination from response
  const accounts = accountsResponse?.accounts || [];
  const pagination = accountsResponse?.pagination;

  // Get account type badge class
  const getAccountTypeBadgeClass = (accountType: string) => {
    switch (accountType?.toLowerCase()) {
      case 'checking':
        return 'bg-blue-100 text-blue-800';
      case 'savings':
        return 'bg-green-100 text-green-800';
      case 'credit':
        return 'bg-red-100 text-red-800';
      case 'investment':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle batch actions
  const handleBatchAction = async (action: string, ids: string[]) => {
    try {
      switch (action) {
        case 'status':
          setIsStatusDialogOpen(true);
          break;

        case 'delete':
          const deleteResult = await batchDeleteAccounts({ ids }).unwrap();
          toast({
            title: t('notifications.success'),
            description: t('notifications.batchDeleteSuccess', { count: deleteResult.deleted_count }),
            variant: 'default',
          });
          break;
      }
    } catch (error) {
      console.error(`Failed to perform batch action ${action}:`, error);
      toast({
        title: t('notifications.error'),
        description: t('notifications.failedAction', { action }),
        variant: 'destructive',
      });
    }
  };

  // Handle status update
  const handleStatusUpdate = async (ids: string[]) => {
    if (selectedStatus === '') return;

    try {
      const result = await batchUpdateAccounts({
        account_ids: ids,
        isActive: selectedStatus as boolean
      }).unwrap();

      toast({
        title: t('notifications.success'),
        description: t('notifications.batchUpdateSuccess', { count: result.updated_count }),
        variant: 'default',
      });

      closeStatusDialog();
    } catch (error) {
      console.error('Failed to update account status:', error);
      toast({
        title: t('notifications.error'),
        description: t('notifications.batchUpdateError'),
        variant: 'destructive',
      });
    }
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  // Clear filters
  const clearFilters = () => {
    setSearchQuery('');
    setAccountTypeFilter('');
    setIsActiveFilter('');
    setBankNameFilter('');
    setCurrentPage(1);
  };

  // Handle add account
  const handleAddAccount = () => {
    cleanupModals(); // Clean up any lingering modals first
    setSelectedAccount(null);
    setIsFormDialogOpen(true);
  };

  // Handle edit account
  const handleEditAccount = (account: any) => {
    cleanupModals(); // Clean up any lingering modals first
    setSelectedAccount(account);
    setIsFormDialogOpen(true);
  };

  // Handle upload statement
  const handleUploadStatement = (account: any) => {
    cleanupModals(); // Clean up any lingering modals first
    setAccountForUpload(account);
    setIsUploadDialogOpen(true);
  };

  // Enhanced close handlers with cleanup
  const closeFormDialog = () => {
    setIsFormDialogOpen(false);
    setSelectedAccount(null);
    cleanupModals();
  };

  const closeUploadDialog = () => {
    setIsUploadDialogOpen(false);
    setAccountForUpload(null);
    cleanupModals();
  };

  const closeStatusDialog = () => {
    setIsStatusDialogOpen(false);
    setSelectedStatus('');
    cleanupModals();
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <h1 className="text-3xl font-bold">{t('bankAccounts.title')}</h1>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            {viewMode === 'grid' ? <List className="mr-2 h-4 w-4" /> : <Grid className="mr-2 h-4 w-4" />}
            {viewMode === 'grid' ? t('actions.listView') : t('actions.gridView')}
          </Button>
          <Button onClick={handleAddAccount}>
            <Plus className="mr-2 h-4 w-4" />
            {t('bankAccounts.addAccount')}
          </Button>
        </div>
      </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{t('filters.title')}</CardTitle>
            <CardDescription>{t('filters.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="search"
                  placeholder={t('filters.search')}
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Select value={accountTypeFilter} onValueChange={setAccountTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allAccountTypes')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{t('filters.allAccountTypes')}</SelectItem>
                  <SelectItem value="Checking">Checking</SelectItem>
                  <SelectItem value="Savings">Savings</SelectItem>
                  <SelectItem value="Credit">Credit</SelectItem>
                  <SelectItem value="Investment">Investment</SelectItem>
                </SelectContent>
              </Select>

              <Select value={isActiveFilter} onValueChange={setIsActiveFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allStatuses')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{t('filters.allStatuses')}</SelectItem>
                  <SelectItem value="true">{t('status.active')}</SelectItem>
                  <SelectItem value="false">{t('status.inactive')}</SelectItem>
                </SelectContent>
              </Select>

              <Input
                type="text"
                placeholder={t('filters.bankName')}
                value={bankNameFilter}
                onChange={(e) => setBankNameFilter(e.target.value)}
              />
            </div>

            <div className="flex justify-between items-center">
              <Button variant="outline" onClick={clearFilters}>
                {t('filters.clear')}
              </Button>
              <div className="flex items-center space-x-2">
                <Label htmlFor="pageSize">{t('pagination.pageSize')}:</Label>
                <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bank Accounts Table/Grid */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>{t('table.title')}</CardTitle>
                <CardDescription>
                  {pagination && t('table.showing', {
                    from: (pagination.page - 1) * pagination.limit + 1,
                    to: Math.min(pagination.page * pagination.limit, pagination.total),
                    total: pagination.total
                  })}
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Label>{t('table.sortBy')}:</Label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="accountName">{t('table.accountName')}</SelectItem>
                    <SelectItem value="bankName">{t('table.bankName')}</SelectItem>
                    <SelectItem value="accountType">{t('table.accountType')}</SelectItem>
                    <SelectItem value="balance">{t('table.balance')}</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                >
                  {sortOrder === 'asc' ? '↑' : '↓'}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoadingAccounts ? (
              <div className="space-y-2">
                {Array.from({ length: pageSize }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500">{t('errors.loadingFailed')}</p>
                <Button variant="outline" onClick={() => window.location.reload()}>
                  {t('actions.retry')}
                </Button>
              </div>
            ) : viewMode === 'list' ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <BatchSelectAll ids={accounts.map(account => account.id)} />
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('accountName')}
                    >
                      {t('table.accountName')} {sortBy === 'accountName' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('bankName')}
                    >
                      {t('table.bankName')} {sortBy === 'bankName' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('accountType')}
                    >
                      {t('table.accountType')} {sortBy === 'accountType' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </TableHead>
                    <TableHead>{t('table.accountNumber')}</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('balance')}
                    >
                      {t('table.balance')} {sortBy === 'balance' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </TableHead>
                    <TableHead>{t('table.status')}</TableHead>
                    <TableHead className="text-right">{t('table.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {accounts.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-4">
                        {t('table.noAccountsFound')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    accounts.map(account => (
                      <TableRow key={account.id}>
                        <TableCell>
                          <BatchSelectionCheckbox id={account.id} />
                        </TableCell>
                        <TableCell className="font-medium">{account.accountName}</TableCell>
                        <TableCell>{account.bankName}</TableCell>
                        <TableCell>
                          <Badge className={getAccountTypeBadgeClass(account.accountType)}>
                            {account.accountType}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {account.accountNumber ? `****${account.accountNumber.slice(-4)}` : '-'}
                        </TableCell>
                        <TableCell>{formatCurrency(account.balance || 0)}</TableCell>
                        <TableCell>
                          <Badge variant={account.isActive ? 'default' : 'secondary'}>
                            {account.isActive ? t('status.active') : t('status.inactive')}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>{t('table.actions')}</DropdownMenuLabel>
                              <DropdownMenuItem asChild>
                                <Link href={`/banking/accounts/${account.id}`}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  {t('actions.view')}
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditAccount(account)}>
                                <Edit className="mr-2 h-4 w-4" />
                                {t('actions.edit')}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleUploadStatement(account)}>
                                <Upload className="mr-2 h-4 w-4" />
                                {t('actions.uploadStatement')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            ) : (
              // Grid view
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {accounts.map(account => (
                  <Card key={account.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center space-x-2">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            {account.accountType === 'Credit' ? (
                              <CreditCard className="h-5 w-5 text-blue-600" />
                            ) : (
                              <Building2 className="h-5 w-5 text-blue-600" />
                            )}
                          </div>
                          <div>
                            <CardTitle className="text-lg">{account.accountName}</CardTitle>
                            <CardDescription>{account.bankName}</CardDescription>
                          </div>
                        </div>
                        <Badge variant={account.isActive ? 'default' : 'secondary'}>
                          {account.isActive ? t('status.active') : t('status.inactive')}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-2 mb-4">
                        <div>
                          <p className="text-sm text-muted-foreground">{t('accountCard.accountType')}</p>
                          <Badge className={getAccountTypeBadgeClass(account.accountType)}>
                            {account.accountType}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{t('accountCard.balance')}</p>
                          <p className="font-medium">{formatCurrency(account.balance || 0)}</p>
                        </div>
                        <div className="col-span-2">
                          <p className="text-sm text-muted-foreground">{t('accountCard.accountNumber')}</p>
                          <p className="font-mono text-sm">
                            {account.accountNumber ? `****${account.accountNumber.slice(-4)}` : '-'}
                          </p>
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <BatchSelectionCheckbox id={account.id} />
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/banking/accounts/${account.id}`}>
                            <Eye className="h-4 w-4 mr-1" />
                            {t('accountCard.actions.view')}
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleEditAccount(account)}>
                          <Edit className="h-4 w-4 mr-1" />
                          {t('accountCard.actions.edit')}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-muted-foreground">
              {t('pagination.showing', {
                from: (pagination.page - 1) * pagination.limit + 1,
                to: Math.min(pagination.page * pagination.limit, pagination.total),
                total: pagination.total
              })}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!pagination.hasPreviousPage}
              >
                <ChevronLeft className="h-4 w-4" />
                {t('pagination.previous')}
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={page === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!pagination.hasNextPage}
              >
                {t('pagination.next')}
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Batch Action Bar */}
        <BatchActionBar
          entityName={t('batchActions.entityName')}
          actions={[
            { id: 'status', label: t('actions.updateStatus') },
            { id: 'delete', label: t('actions.deleteAccounts') },
          ]}
          onAction={handleBatchAction}
        />

        {/* Status Update Dialog */}
        <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('dialog.batchUpdate.title')}</DialogTitle>
              <DialogDescription>
                {t('dialog.batchUpdate.description')}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="status">{t('table.status')}</Label>
                <Select value={selectedStatus.toString()} onValueChange={(value) => setSelectedStatus(value === 'true')}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder={t('dialogs.updateStatus.selectStatus')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">{t('status.active')}</SelectItem>
                    <SelectItem value="false">{t('status.inactive')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={closeStatusDialog}>
                {t('dialog.buttons.cancel')}
              </Button>
              <Button onClick={() => handleStatusUpdate(getSelectedIds())}>
                {t('dialog.buttons.update')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Bank Account Form Dialog */}
        {isFormDialogOpen && (
          <BankAccountFormDialog
            isOpen={isFormDialogOpen}
            onClose={closeFormDialog}
            account={selectedAccount}
          />
        )}

        {/* Statement Upload Dialog */}
        {isUploadDialogOpen && accountForUpload && (
          <StatementUploadForm
            isOpen={isUploadDialogOpen}
            onClose={closeUploadDialog}
            onSuccess={closeUploadDialog}
            bankAccount={accountForUpload}
          />
        )}
      </div>
    );
}

// Main component that wraps the content with BatchSelectionProvider
export default function BankAccountListClient() {
  return (
    <BatchSelectionProvider>
      <BankAccountListContent />
    </BatchSelectionProvider>
  );
}