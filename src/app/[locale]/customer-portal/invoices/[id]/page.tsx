'use client';

import { useState, useEffect, use, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  ArrowLeft,
  Download,
  CreditCard,
  FileText,
  Calendar,
  DollarSign,
  User,
  Building,
  Mail,
  Phone
} from 'lucide-react';
import { Link } from '@/i18n/navigation';
import { format } from 'date-fns';

interface CustomerPortalUser {
  id: string;
  name: string;
  email: string;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  customerId: string;
  issueDate: string;
  dueDate: string;
  totalAmount: number;
  status: string;
  notes?: string;
  terms?: string;
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    address?: string;
  };
  items?: InvoiceItem[];
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface InvoiceDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function InvoiceDetailPage({ params }: InvoiceDetailPageProps) {
  const { id } = use(params);
  const [user, setUser] = useState<CustomerPortalUser | null>(null);
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations('customerPortal.invoiceDetail');
  const locale = useLocale();

  // Check if user is logged in and fetch invoice
  useEffect(() => {
    const storedUser = sessionStorage.getItem('customerPortalUser');

    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setUser(parsedUser);

      // Fetch invoice
      fetchInvoice(id, parsedUser.id);
    } else {
      // Redirect to login if not logged in
      router.push('/customer-portal');
    }
  }, [id, router]);

  // Fetch invoice details
  const fetchInvoice = useCallback(async (invoiceId: string, customerId: string) => {
    try {
      // Use customer portal API route for specific invoice
      const response = await fetch(`/api/customer-portal/invoices/${customerId}/${invoiceId}`);

      if (response.ok) {
        const invoice = await response.json();
        setInvoice(invoice);
      } else if (response.status === 403) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to view this invoice.',
          variant: 'destructive',
        });
        router.push('/customer-portal/dashboard');
      } else if (response.status === 404) {
        toast({
          title: 'Invoice Not Found',
          description: 'The requested invoice could not be found.',
          variant: 'destructive',
        });
        router.push('/customer-portal/dashboard');
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        toast({
          title: 'Error Loading Invoice',
          description: errorData.message || 'Failed to load invoice details. Please try again.',
          variant: 'destructive',
        });
        router.push('/customer-portal/dashboard');
      }
    } catch (error) {
      console.error('Error fetching invoice:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while loading the invoice.',
        variant: 'destructive',
      });
      router.push('/customer-portal/dashboard');
    } finally {
      setIsLoading(false);
    }
  }, [router, toast]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(locale, { style: 'currency', currency: 'USD' }).format(amount);
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'PartiallyPaid':
        return 'bg-yellow-100 text-yellow-800';
      case 'Sent':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle download invoice
  const handleDownloadInvoice = async () => {
    if (!invoice) return;

    try {
      // Call Go backend PDF service
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8050'}/pdf/invoices/${invoice.id}`);

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      // Get the PDF blob
      const blob = await response.blob();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice-${invoice.invoice_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Download Started',
        description: 'Your invoice PDF download will begin shortly.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate PDF. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (!user || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <Skeleton className="h-[600px] w-full max-w-4xl mx-auto" />
        </div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-2xl font-bold mb-4">Invoice Not Found</h1>
          <p className="mb-6">The requested invoice could not be found.</p>
          <Button asChild>
            <Link href="/customer-portal/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-6 flex items-center justify-between">
            <Button variant="outline" asChild>
              <Link href="/customer-portal/dashboard">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleDownloadInvoice}>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
              {invoice.status !== 'Paid' && (
                <Button asChild>
                  <Link href={`/customer-portal/invoices/${invoice.id}/pay`}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Pay Now
                  </Link>
                </Button>
              )}
            </div>
          </div>

          {/* Invoice Card */}
          <Card className="shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl">Invoice Details</CardTitle>
                  <CardDescription>
                    Invoice #{invoice.invoiceNumber}
                  </CardDescription>
                </div>
                <div className="text-right">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusBadgeClass(invoice.status)}`}>
                    {t(`status.${invoice.status.toLowerCase()}`)}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Invoice Summary */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Invoice Details</h3>

                    <div className="space-y-2">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 text-muted-foreground mr-2" />
                        <span className="text-sm text-muted-foreground">Invoice Number:</span>
                        <span className="ml-2 font-medium">{invoice.invoiceNumber}</span>
                      </div>

                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
                        <span className="text-sm text-muted-foreground">Issue Date:</span>
                        <span className="ml-2">{format(new Date(invoice.issueDate), 'MMM d, yyyy')}</span>
                      </div>

                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
                        <span className="text-sm text-muted-foreground">Due Date:</span>
                        <span className="ml-2">{format(new Date(invoice.dueDate), 'MMM d, yyyy')}</span>
                      </div>

                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 text-muted-foreground mr-2" />
                        <span className="text-sm text-muted-foreground">Total Amount:</span>
                        <span className="ml-2 font-semibold text-lg">{formatCurrency(invoice.totalAmount)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Customer Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Customer Information</h3>

                    <div className="space-y-2">
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-muted-foreground mr-2" />
                        <span className="text-sm text-muted-foreground">Name:</span>
                        <span className="ml-2">{invoice.customer?.name || user.name}</span>
                      </div>

                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-muted-foreground mr-2" />
                        <span className="text-sm text-muted-foreground">Email:</span>
                        <span className="ml-2">{invoice.customer?.email || user.email}</span>
                      </div>

                      {invoice.customer?.phone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 text-muted-foreground mr-2" />
                          <span className="text-sm text-muted-foreground">Phone:</span>
                          <span className="ml-2">{invoice.customer.phone}</span>
                        </div>
                      )}

                      {invoice.customer?.address && (
                        <div className="flex items-start">
                          <Building className="h-4 w-4 text-muted-foreground mr-2 mt-0.5" />
                          <span className="text-sm text-muted-foreground">Address:</span>
                          <span className="ml-2">{invoice.customer.address}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Invoice Items */}
                {invoice.items && invoice.items.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Invoice Items</h3>

                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2">Description</th>
                            <th className="text-right py-2">Quantity</th>
                            <th className="text-right py-2">Unit Price</th>
                            <th className="text-right py-2">Total</th>
                          </tr>
                        </thead>
                        <tbody>
                          {invoice.items.map((item, index) => (
                            <tr key={item.id || index} className="border-b">
                              <td className="py-2">{item.description}</td>
                              <td className="text-right py-2">{item.quantity}</td>
                              <td className="text-right py-2">{formatCurrency(item.unitPrice)}</td>
                              <td className="text-right py-2 font-medium">{formatCurrency(item.total)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                <Separator />

                {/* Total */}
                <div className="flex justify-end">
                  <div className="w-64">
                    <div className="flex justify-between items-center py-2 text-lg font-semibold">
                      <span>Total:</span>
                      <span>{formatCurrency(invoice.totalAmount)}</span>
                    </div>
                  </div>
                </div>

                {/* Notes and Terms */}
                {(invoice.notes || invoice.terms) && (
                  <>
                    <Separator />
                    <div className="space-y-4">
                      {invoice.notes && (
                        <div>
                          <h4 className="font-medium mb-2">Notes</h4>
                          <p className="text-sm text-muted-foreground">{invoice.notes}</p>
                        </div>
                      )}

                      {invoice.terms && (
                        <div>
                          <h4 className="font-medium mb-2">Terms & Conditions</h4>
                          <p className="text-sm text-muted-foreground">{invoice.terms}</p>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="mt-6 flex justify-center space-x-4">
            <Button variant="outline" onClick={handleDownloadInvoice}>
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>

            {invoice.status !== 'Paid' && (
              <Button asChild size="lg">
                <Link href={`/customer-portal/invoices/${invoice.id}/pay`}>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Pay Invoice ({formatCurrency(invoice.totalAmount)})
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
