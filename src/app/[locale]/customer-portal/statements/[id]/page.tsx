'use client';

import { useState, useEffect, use, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  ArrowLeft,
  Download,
  FileText,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { Link } from '@/i18n/navigation';
import { format } from 'date-fns';

interface CustomerPortalUser {
  id: string;
  name: string;
  email: string;
}

interface CustomerStatement {
  id: string;
  merchantId: string;
  customerId: string;
  startDate: string;
  endDate: string;
  openingBalance: number;
  closingBalance: number;
  totalInvoiced: number;
  totalPaid: number;
  totalCredits: number;
  generatedAt: string;
  customer: {
    id: string;
    name: string;
    email?: string;
  };
  items: StatementItem[];
}

interface StatementItem {
  id: string;
  statementId: string;
  date: string;
  description: string;
  type: string;
  reference?: string;
  amount: number;
  balance: number;
}

interface StatementDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function StatementDetailPage({ params }: StatementDetailPageProps) {
  const { id } = use(params);
  const [user, setUser] = useState<CustomerPortalUser | null>(null);
  const [statement, setStatement] = useState<CustomerStatement | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const router = useRouter();
  const { toast } = useToast();
  const locale = useLocale();

  // Fetch statement details
  const fetchStatement = useCallback(async (statementId: string, customerId: string) => {
    try {
      // Use customer portal API route for better security and access control
      const response = await fetch(`/api/customer-portal/statements/${customerId}/${statementId}`);

      if (response.ok) {
        const statement = await response.json();
        setStatement(statement);
      } else if (response.status === 403) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to view this statement.',
          variant: 'destructive',
        });
        router.push('/customer-portal/dashboard');
      } else if (response.status === 404) {
        toast({
          title: 'Statement Not Found',
          description: 'The requested statement could not be found.',
          variant: 'destructive',
        });
        router.push('/customer-portal/dashboard');
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        toast({
          title: 'Error Loading Statement',
          description: errorData.message || 'Failed to load statement details. Please try again.',
          variant: 'destructive',
        });
        router.push('/customer-portal/dashboard');
      }
    } catch (error) {
      console.error('Error fetching statement:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while loading the statement.',
        variant: 'destructive',
      });
      router.push('/customer-portal/dashboard');
    } finally {
      setIsLoading(false);
    }
  }, [router, toast]);

  // Check if user is logged in and fetch statement
  useEffect(() => {
    const storedUser = sessionStorage.getItem('customerPortalUser');

    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setUser(parsedUser);

      // Fetch statement
      fetchStatement(id, parsedUser.id);
    } else {
      // Redirect to login if not logged in
      router.push('/customer-portal');
    }
  }, [id, router, fetchStatement]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(locale, { style: 'currency', currency: 'USD' }).format(amount);
  };

  // Get transaction type icon and color
  const getTransactionTypeInfo = (type: string) => {
    switch (type) {
      case 'invoice':
        return { icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      case 'payment':
        return { icon: TrendingDown, color: 'text-green-600', bgColor: 'bg-green-100' };
      case 'credit':
        return { icon: TrendingUp, color: 'text-purple-600', bgColor: 'bg-purple-100' };
      default:
        return { icon: FileText, color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  // Handle download statement
  const handleDownloadStatement = async () => {
    if (!statement) return;

    try {
      // Call Go backend PDF service
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8050'}/pdf/statements/${statement.id}`);

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      // Get the PDF blob
      const blob = await response.blob();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `statement-${statement.id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Download Started',
        description: 'Your statement PDF download will begin shortly.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate PDF. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (!user || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <Skeleton className="h-[600px] w-full max-w-4xl mx-auto" />
        </div>
      </div>
    );
  }

  if (!statement) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-2xl font-bold mb-4">Statement Not Found</h1>
          <p className="mb-6">The requested statement could not be found.</p>
          <Button asChild>
            <Link href="/customer-portal/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-6 flex items-center justify-between">
            <Button variant="outline" asChild>
              <Link href="/customer-portal/dashboard">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>

            <Button variant="outline" onClick={handleDownloadStatement}>
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
          </div>

          {/* Statement Card */}
          <Card className="shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl">Customer Statement</CardTitle>
                  <CardDescription>
                    Statement #{statement.id.slice(-8)}
                  </CardDescription>
                </div>
                <div className="text-right">
                  <p className="text-sm text-muted-foreground">Period</p>
                  <p className="font-medium">
                    {format(new Date(statement.startDate), 'MMM d')} - {format(new Date(statement.endDate), 'MMM d, yyyy')}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Statement Summary */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Opening Balance</p>
                    <p className="text-lg font-semibold">{formatCurrency(statement.openingBalance)}</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Total Invoiced</p>
                    <p className="text-lg font-semibold">{formatCurrency(statement.totalInvoiced || 0)}</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Total Paid</p>
                    <p className="text-lg font-semibold">{formatCurrency(statement.totalPaid || 0)}</p>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Closing Balance</p>
                    <p className="text-lg font-semibold">{formatCurrency(statement.closingBalance)}</p>
                  </div>
                </div>

                <Separator />

                {/* Transactions */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg">Transaction History</h3>

                  <div className="space-y-2">
                    {statement.items && statement.items.length > 0 ? (
                      statement.items.map((item: StatementItem) => {
                        const typeInfo = getTransactionTypeInfo(item.type);
                        const Icon = typeInfo.icon;

                        return (
                          <div key={item.id} className="flex items-center justify-between p-4 bg-white border rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 rounded-full ${typeInfo.bgColor}`}>
                                <Icon className={`h-4 w-4 ${typeInfo.color}`} />
                              </div>
                              <div>
                                <p className="font-medium">{item.description}</p>
                                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                  <span>{format(new Date(item.date), 'MMM d, yyyy')}</span>
                                  {item.reference && <span>Ref: {item.reference}</span>}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className={`font-medium ${item.amount > 0 ? 'text-red-600' : item.amount < 0 ? 'text-green-600' : 'text-gray-600'}`}>
                                {item.amount !== 0 && (item.amount > 0 ? '+' : '')}{item.amount !== 0 ? formatCurrency(Math.abs(item.amount)) : '—'}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                Balance: {formatCurrency(item.balance)}
                              </p>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>No transactions found for this statement period.</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
