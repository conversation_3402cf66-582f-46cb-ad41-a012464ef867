'use client';

import { use, useState } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useGetBillByIdQuery } from '@/redux/services/billsApi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Edit, Trash2, DollarSign, Calendar, FileText, Building2 } from 'lucide-react';
import { useFormatters } from '@/hooks/useFormatters';
import { format } from 'date-fns';
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton';
import { BillDeleteDialog } from '../_components/BillDeleteDialog';

interface BillViewPageProps {
  params: Promise<{ id: string }>;
}

export default function BillViewPage({ params }: BillViewPageProps) {
  const { id } = use(params);
  const t = useTranslations('bills');
  const router = useRouter();
  const { formatCurrency } = useFormatters();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const {
    data: bill,
    isLoading,
    error,
    refetch
  } = useGetBillByIdQuery(id);

  if (isLoading) {
    return (
      <div className="container mx-auto py-10">
        <div className="space-y-6">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-10" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-20" />
            </div>
          </div>

          {/* Content Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardContent>
              </Card>
            </div>
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-24" />
                </CardHeader>
                <CardContent className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !bill) {
    return (
      <div className="container mx-auto py-10">
        <div className="text-center space-y-4">
          <div className="p-4 bg-destructive/10 rounded-lg inline-block">
            <FileText className="h-12 w-12 text-destructive mx-auto" />
          </div>
          <h1 className="text-2xl font-bold">{t('errors.notFound')}</h1>
          <p className="text-muted-foreground">{t('errors.billNotFound')}</p>
          <div className="flex justify-center space-x-2">
            <Button variant="outline" onClick={() => router.push('/bills')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('actions.backToBills')}
            </Button>
            <Button onClick={() => refetch()}>
              {t('actions.retry')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'partially_paid':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'void':
        return 'bg-gray-100 text-gray-600 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="icon" onClick={() => router.push('/bills')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="p-2 bg-primary/10 rounded-lg">
              <FileText className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {t('view.title')} {bill.billNumber || `#${bill.id.slice(-8)}`}
              </h1>
              <p className="text-muted-foreground">
                {t('view.subtitle')}
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={`/bills/${bill.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                {t('actions.editBill')}
              </Link>
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              {t('actions.deleteBill')}
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Bill Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Bill Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>{t('view.billInformation')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t('table.billNumber')}
                    </label>
                    <p className="text-lg font-semibold">
                      {bill.billNumber || `#${bill.id.slice(-8)}`}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t('table.status')}
                    </label>
                    <div className="mt-1">
                      <Badge className={getStatusColor(bill.status)}>
                        {t(`status.${bill.status}`)}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t('table.billDate')}
                    </label>
                    <p className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{format(new Date(bill.billDate), 'PPP')}</span>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t('table.dueDate')}
                    </label>
                    <p className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {bill.dueDate ? format(new Date(bill.dueDate), 'PPP') : t('view.noDueDate')}
                      </span>
                    </p>
                  </div>
                </div>

                {bill.notes && (
                  <>
                    <Separator />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('dialog.form.notes')}
                      </label>
                      <p className="mt-1 text-sm">{bill.notes}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Bill Items */}
            <Card>
              <CardHeader>
                <CardTitle>{t('view.billItems')}</CardTitle>
                <CardDescription>
                  {t('view.itemsDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {bill.items && bill.items.length > 0 ? (
                  <div className="space-y-4">
                    {bill.items.map((item, index) => (
                      <div key={item.id || index} className="flex justify-between items-center p-4 border rounded-lg">
                        <div className="flex-1">
                          <p className="font-medium">{item.description}</p>
                          <p className="text-sm text-muted-foreground">
                            {t('view.quantity')}: {item.quantity} × {formatCurrency(item.unitPrice)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{formatCurrency(item.totalPrice)}</p>
                          {item.taxAmount && item.taxAmount > 0 && (
                            <p className="text-sm text-muted-foreground">
                              {t('view.tax')}: {formatCurrency(item.taxAmount)}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-8">
                    {t('view.noItems')}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Summary & Vendor */}
          <div className="space-y-6">
            {/* Vendor Information */}
            {bill.vendor && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Building2 className="h-5 w-5" />
                    <span>{t('view.vendorInformation')}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="font-semibold">{bill.vendor.name}</p>
                    {bill.vendor.email && (
                      <p className="text-sm text-muted-foreground">{bill.vendor.email}</p>
                    )}
                    {bill.vendor.phone && (
                      <p className="text-sm text-muted-foreground">{bill.vendor.phone}</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Amount Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5" />
                  <span>{t('view.amountSummary')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('view.subtotal')}</span>
                  <span>{formatCurrency(bill.subTotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('view.tax')}</span>
                  <span>{formatCurrency(bill.taxAmount)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-semibold">
                  <span>{t('view.total')}</span>
                  <span>{formatCurrency(bill.totalAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('view.paidAmount')}</span>
                  <span>{formatCurrency(bill.paidAmount)}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold">
                  <span>{t('view.balanceAmount')}</span>
                  <span className={bill.balanceAmount > 0 ? 'text-red-600' : 'text-green-600'}>
                    {formatCurrency(bill.balanceAmount)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Delete Dialog */}
      <BillDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        bill={bill ? {
          id: bill.id,
          billNumber: bill.billNumber,
          status: bill.status,
          totalAmount: bill.totalAmount
        } : null}
        onDeleteSuccess={() => router.push('/en/bills')}
      />
    </div>
  );
}
