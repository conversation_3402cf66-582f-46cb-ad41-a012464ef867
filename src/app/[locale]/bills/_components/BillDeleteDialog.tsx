'use client';

import { useTranslations } from 'next-intl';
import { useDeleteBillMutation } from '@/redux/services/billsApi';
import { useFormatters } from '@/hooks/useFormatters';
import { DeleteDialog } from '@/components/ui/delete-dialog';

interface BillDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bill: {
    id: string;
    billNumber?: string;
    status: string;
    totalAmount: number;
  } | null;
  onDeleteSuccess?: () => void;
}

export function BillDeleteDialog({ isOpen, onClose, bill, onDeleteSuccess }: BillDeleteDialogProps) {
  const t = useTranslations('bills');
  const [deleteBill] = useDeleteBillMutation();
  const { formatCurrency } = useFormatters();

  const handleDelete = async () => {
    if (!bill) return;
    await deleteBill(bill.id).unwrap();
  };

  const canDelete = bill?.status?.toLowerCase() === 'draft';
  const billDisplayNumber = bill?.billNumber || `#${bill?.id?.slice(-8)}`;

  const itemDetails = bill ? (
    <>
      <p className="font-medium">
        You are about to delete bill {billDisplayNumber}:
      </p>
      <p className="text-sm text-muted-foreground">
        Status: <span className="font-medium">{bill.status.charAt(0).toUpperCase() + bill.status.slice(1)}</span>
      </p>
      <p className="text-sm text-muted-foreground">
        Total Amount: <span className="font-medium">{formatCurrency(bill.totalAmount)}</span>
      </p>
    </>
  ) : null;

  return (
    <DeleteDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={handleDelete}
      title="Delete Bill"
      description="Are you sure you want to delete this bill? This action cannot be undone."
      itemDetails={itemDetails}
      canDelete={canDelete}
      warningMessage={!canDelete ? "Only draft bills can be deleted." : undefined}
      confirmButtonText="Delete"
      cancelButtonText="Cancel"
      loadingText="Deleting..."
      successMessage={t('notifications.deleteSuccess')}
      errorMessage={t('notifications.deleteError')}
      onDeleteSuccess={onDeleteSuccess}
    />
  );
}
