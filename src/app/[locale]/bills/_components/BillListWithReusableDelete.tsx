'use client';

import { useTranslations } from 'next-intl';
import { useDeleteBillMutation } from '@/redux/services/billsApi';
import { useFormatters } from '@/hooks/useFormatters';
import { useDeleteDialog } from '@/hooks/useDeleteDialog';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';

// Example of how to use the reusable delete dialog in a bill list component
export function BillListWithReusableDelete() {
  const t = useTranslations('bills');
  const [deleteBill] = useDeleteBillMutation();
  const { formatCurrency } = useFormatters();

  // Initialize the reusable delete dialog
  const deleteDialog = useDeleteDialog({
    title: 'Delete Bill',
    description: 'Are you sure you want to delete this bill? This action cannot be undone.',
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel',
    loadingText: 'Deleting...',
    successMessage: t('notifications.deleteSuccess'),
    errorMessage: t('notifications.deleteError'),
  });

  // Handle delete action for a specific bill
  const handleDeleteBill = (bill: any) => {
    const canDelete = bill.status === 'draft';
    
    const itemDetails = (
      <>
        <p className="font-medium">
          You are about to delete bill {bill.billNumber || `#${bill.id.slice(-8)}`}:
        </p>
        <p className="text-sm text-muted-foreground">
          Status: <span className="font-medium">{t(`status.${bill.status}`)}</span>
        </p>
        <p className="text-sm text-muted-foreground">
          Total Amount: <span className="font-medium">{formatCurrency(bill.totalAmount)}</span>
        </p>
      </>
    );

    deleteDialog.openDialog({
      item: bill,
      onConfirm: async () => {
        await deleteBill(bill.id).unwrap();
      },
      onDeleteSuccess: () => {
        // Refresh the list or navigate
        console.log('Bill deleted successfully');
      },
      canDelete,
      warningMessage: !canDelete ? 'Only draft bills can be deleted.' : undefined,
      itemDetails,
    });
  };

  // Example bill data
  const exampleBill = {
    id: '123',
    billNumber: 'BILL-001',
    status: 'draft',
    totalAmount: 1500.00,
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Bill List with Reusable Delete</h2>
      
      {/* Example bill item */}
      <div className="border rounded-lg p-4 mb-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-medium">{exampleBill.billNumber}</h3>
            <p className="text-sm text-muted-foreground">
              Status: {exampleBill.status} | Amount: {formatCurrency(exampleBill.totalAmount)}
            </p>
          </div>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteBill(exampleBill)}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Render the delete dialog */}
      <deleteDialog.DeleteDialogComponent />
    </div>
  );
}

// Example of how to use the reusable delete dialog for vendors
export function VendorListWithReusableDelete() {
  const t = useTranslations('vendors');
  const [deleteVendor] = useDeleteVendorMutation();

  const deleteDialog = useDeleteDialog({
    title: 'Delete Vendor',
    description: 'Are you sure you want to delete this vendor? This action cannot be undone and will affect all related bills and transactions.',
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel',
    loadingText: 'Deleting...',
    successMessage: 'Vendor deleted successfully',
    errorMessage: 'Failed to delete vendor',
  });

  const handleDeleteVendor = (vendor: any) => {
    const itemDetails = (
      <>
        <p className="font-medium">
          You are about to delete vendor: {vendor.name}
        </p>
        {vendor.email && (
          <p className="text-sm text-muted-foreground">
            Email: <span className="font-medium">{vendor.email}</span>
          </p>
        )}
        {vendor.phone && (
          <p className="text-sm text-muted-foreground">
            Phone: <span className="font-medium">{vendor.phone}</span>
          </p>
        )}
      </>
    );

    deleteDialog.openDialog({
      item: vendor,
      onConfirm: async () => {
        await deleteVendor(vendor.id).unwrap();
      },
      onDeleteSuccess: () => {
        console.log('Vendor deleted successfully');
      },
      canDelete: true,
      itemDetails,
    });
  };

  // Example vendor data
  const exampleVendor = {
    id: '456',
    name: 'Acme Corp',
    email: '<EMAIL>',
    phone: '******-0123',
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Vendor List with Reusable Delete</h2>
      
      {/* Example vendor item */}
      <div className="border rounded-lg p-4 mb-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-medium">{exampleVendor.name}</h3>
            <p className="text-sm text-muted-foreground">
              {exampleVendor.email} | {exampleVendor.phone}
            </p>
          </div>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteVendor(exampleVendor)}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Render the delete dialog */}
      <deleteDialog.DeleteDialogComponent />
    </div>
  );
}
