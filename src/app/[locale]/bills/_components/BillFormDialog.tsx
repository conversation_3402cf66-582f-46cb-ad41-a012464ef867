'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useCreateBillMutation, useUpdateBillMutation } from '@/redux/services/billsApi';
import { useGetVendorsQuery } from '@/redux/services/vendorsApi';
import { useBranchContext } from '@/contexts/BranchContext';
import { BillStatus } from '@/lib/types';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { useToast } from '@/components/ui/use-toast';

interface BillFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bill?: any;
}

export function BillFormDialog({ isOpen, onClose, bill }: BillFormDialogProps) {
  const t = useTranslations('bills');
  const { toast } = useToast();
  const { selectedBranchId } = useBranchContext();

  // Form state
  const [vendorId, setVendorId] = useState('');
  const [billNumber, setBillNumber] = useState('');
  const [billDate, setBillDate] = useState<Date | undefined>(undefined);
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [totalAmount, setTotalAmount] = useState('');
  const [taxAmount, setTaxAmount] = useState('0');
  const [description, setDescription] = useState('');
  const [error, setError] = useState<string | null>(null);

  // RTK Query hooks
  const { data: vendorsResponse } = useGetVendorsQuery();
  const [createBill, { isLoading: isCreating }] = useCreateBillMutation();
  const [updateBill, { isLoading: isUpdating }] = useUpdateBillMutation();

  const vendors = vendorsResponse?.data || [];
  const isEditing = !!bill;
  const isLoading = isCreating || isUpdating;

  // Initialize form with bill data if editing
  useEffect(() => {
    if (bill) {
      setVendorId(bill.vendor_id || '');
      setBillNumber(bill.billNumber || '');
      setBillDate(bill.billDate ? new Date(bill.billDate) : undefined);
      setDueDate(bill.dueDate ? new Date(bill.dueDate) : undefined);
      setTotalAmount(bill.totalAmount?.toString() || '');
      setTaxAmount(bill.taxAmount?.toString() || '0');
      setDescription(bill.description || '');
    } else {
      resetForm();
    }
  }, [bill]);

  // Reset form
  const resetForm = () => {
    setVendorId('');
    setBillNumber('');
    setBillDate(undefined);
    setDueDate(undefined);
    setTotalAmount('');
    setTaxAmount('0');
    setDescription('');
    setError(null);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validation
    if (!vendorId || !billNumber || !billDate || !dueDate || !totalAmount) {
      setError(t('validation.allFieldsRequired'));
      return;
    }

    const amount = parseFloat(totalAmount);
    if (isNaN(amount) || amount <= 0) {
      setError(t('validation.invalidAmount'));
      return;
    }

    const tax = parseFloat(taxAmount);
    if (isNaN(tax) || tax < 0) {
      setError(t('validation.invalidTaxAmount'));
      return;
    }

    // Check if we have a branch selected
    if (!selectedBranchId) {
      setError('Please select a branch first');
      return;
    }

    try {
      // For now, create a simple item from the total amount
      // TODO: Implement proper line items functionality
      const billData = {
        branchId: selectedBranchId,
        vendorId: vendorId,
        issueDate: billDate.toISOString(),
        dueDate: dueDate?.toISOString(),
        notes: description || null,
        currency: "USD",
        items: [
          {
            description: description || "Bill item",
            quantity: 1,
            unitPrice: amount,
            taxRateId: null
          }
        ]
      };

      if (isEditing) {
        // For editing, we need to use the UpdateBillRequest structure
        const updateData = {
          vendorId: vendorId,
          issueDate: billDate.toISOString(),
          dueDate: dueDate?.toISOString(),
          notes: description || null,
          currency: "USD",
          items: [
            {
              description: description || "Bill item",
              quantity: 1,
              unitPrice: amount,
              taxRateId: null
            }
          ]
        };

        await updateBill({
          id: bill.id,
          body: updateData
        }).unwrap();

        toast({
          title: t('notifications.success'),
          description: t('notifications.updateSuccess'),
          variant: 'default',
        });
      } else {
        await createBill(billData).unwrap();

        toast({
          title: t('notifications.success'),
          description: t('notifications.createSuccess'),
          variant: 'default',
        });
      }

      onClose();
      resetForm();
    } catch (error: any) {
      console.error('Failed to save bill:', error);
      setError(error.message || t('notifications.error'));
    }
  };

  // Handle close
  const handleClose = () => {
    onClose();
    resetForm();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? t('dialog.edit.title') : t('dialog.add.title')}
          </DialogTitle>
          <DialogDescription>
            {isEditing ? t('dialog.edit.description') : t('dialog.add.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            {/* Vendor Select */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="vendor" className="text-right">
                {t('dialog.form.vendor')}*
              </Label>
              <Select value={vendorId} onValueChange={setVendorId} required>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder={t('dialog.form.selectVendor')} />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Bill Number */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="billNumber" className="text-right">
                {t('dialog.form.billNumber')}*
              </Label>
              <Input
                id="billNumber"
                value={billNumber}
                onChange={(e) => setBillNumber(e.target.value)}
                className="col-span-3"
                required
              />
            </div>

            {/* Bill Date */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="billDate" className="text-right">
                {t('dialog.form.billDate')}*
              </Label>
              <div className="col-span-3">
                <DatePicker
                  date={billDate}
                  setDate={setBillDate}
                  placeholder={t('dialog.form.pickDate')}
                />
              </div>
            </div>

            {/* Due Date */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="dueDate" className="text-right">
                {t('dialog.form.dueDate')}*
              </Label>
              <div className="col-span-3">
                <DatePicker
                  date={dueDate}
                  setDate={setDueDate}
                  placeholder={t('dialog.form.pickDate')}
                />
              </div>
            </div>

            {/* Total Amount */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="totalAmount" className="text-right">
                {t('dialog.form.totalAmount')}*
              </Label>
              <Input
                id="totalAmount"
                type="number"
                step="0.01"
                min="0.01"
                value={totalAmount}
                onChange={(e) => setTotalAmount(e.target.value)}
                className="col-span-3"
                required
              />
            </div>

            {/* Tax Amount */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="taxAmount" className="text-right">
                {t('dialog.form.taxAmount')}
              </Label>
              <Input
                id="taxAmount"
                type="number"
                step="0.01"
                min="0"
                value={taxAmount}
                onChange={(e) => setTaxAmount(e.target.value)}
                className="col-span-3"
              />
            </div>

            {/* Description */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                {t('dialog.form.description')}
              </Label>
              <Input
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                placeholder={t('dialog.form.notesPlaceholder')}
              />
            </div>

            {error && (
              <div className="col-span-4 text-red-500 text-sm">
                {error}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              {t('dialog.buttons.cancel')}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading
                ? (isEditing ? t('dialog.buttons.saving') : t('dialog.buttons.creating'))
                : (isEditing ? t('dialog.buttons.save') : t('dialog.buttons.create'))
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
