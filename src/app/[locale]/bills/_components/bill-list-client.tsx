'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useI18n } from '@/hooks/useI18n';
import {
  useGetBillsQuery,
  useBatchUpdateBillsMutation,
  useBatchDeleteBillsMutation,
  type BillsQueryParams
} from '@/redux/services/billsApi';
import { useGetVendorsQuery } from '@/redux/services/vendorsApi';
import { BillStatus } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  ArrowLeft,
  ChevronDown,
  Download,
  Edit,
  Eye,
  MoreHorizontal,
  Plus,
  Search,
  Trash,
  Calendar,
  DollarSign,
  Grid,
  List,
  ChevronLeft,
  ChevronRight,
  Filter,
  FileText
} from 'lucide-react';
import {
  BatchSelectionProvider,
  BatchActionBar,
  BatchSelectionCheckbox,
  BatchSelectAll,
  useBatchSelection
} from '@/components/batch-selection';
import { DatePicker } from '@/components/ui/date-picker';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { BillFormDialog } from './BillFormDialog';
import { BillDeleteDialog } from './BillDeleteDialog';

// ✅ Import advanced pagination components
import { useAdvancedPagination } from '@/hooks/useAdvancedPagination';
import { PaginatedTable } from '@/components/common/PaginatedTable';
import { useBillColumns } from '@/config/tableColumns';

export default function BillListClient() {
  // Get translations
  const t = useTranslations('bills');
  const { formatCurrency, formatDate, router } = useI18n();

  // ✅ Replace manual state with advanced pagination
  const { state, actions, queryParams } = useAdvancedPagination({
    initialSortBy: 'billDate',
    initialSortOrder: 'desc',
    initialLimit: 10,
    persist: true,
    persistKey: 'bills-page'
  });

  // Additional state for bill-specific features
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [vendorFilter, setVendorFilter] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [amountMin, setAmountMin] = useState<string>('');
  const [amountMax, setAmountMax] = useState<string>('');
  const [overdueOnly, setOverdueOnly] = useState<boolean>(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // State for dialogs
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [selectedBill, setSelectedBill] = useState<any>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<BillStatus | ''>('');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [billToDelete, setBillToDelete] = useState<any>(null);

  // ✅ Build enhanced query parameters
  const enhancedQueryParams: BillsQueryParams = {
    ...queryParams,
    search: state.search || undefined,
    status: statusFilter || undefined,
    vendor_id: vendorFilter || undefined,
    start_date: startDate?.toISOString().split('T')[0],
    end_date: endDate?.toISOString().split('T')[0],
    amount_min: amountMin ? parseFloat(amountMin) : undefined,
    amount_max: amountMax ? parseFloat(amountMax) : undefined,
    overdue_only: overdueOnly || undefined,
  };

  // RTK Query hooks
  const { data: billsResponse, isLoading: isLoadingBills, error, isFetching } = useGetBillsQuery(enhancedQueryParams);
  const { data: vendorsResponse } = useGetVendorsQuery();
  const [batchUpdateBills] = useBatchUpdateBillsMutation();
  const [batchDeleteBills] = useBatchDeleteBillsMutation();

  const { toast } = useToast();

  // Get batch selection functions
  const { isSelected, getSelectedIds } = useBatchSelection();

  // Extract bills and pagination from response
  const bills = billsResponse?.bills || [];
  const pagination = billsResponse?.pagination;
  const vendors = vendorsResponse?.data || [];

  // Get vendor name from ID - moved before useBillColumns
  const getVendorName = (vendorId?: string): string => {
    if (!vendorId) return 'Unknown';
    const vendor = vendors.find(v => v.id === vendorId);
    return vendor ? vendor.name : vendorId;
  };

  // ✅ Bill action handlers for columns
  const handleView = (bill: any) => {
    router.push(`/bills/${bill.id}`);
  };

  const handleEdit = (bill: any) => {
    setSelectedBill(bill);
    setIsFormDialogOpen(true);
  };

  const handleDelete = (bill: any) => {
    setBillToDelete(bill);
    setIsDeleteDialogOpen(true);
  };

  const handlePay = (bill: any) => {
    router.push(`/bills/${bill.id}/pay`);
  };

  // ✅ Get column definitions
  const columns = useBillColumns(
    handleEdit,
    handleDelete,
    handleView,
    handlePay,
    getVendorName,
    true, // canEdit
    true  // canDelete
  );

  // Get status badge class
  const getStatusBadgeClass = (status: BillStatus) => {
    switch (status) {
      case BillStatus.Draft:
        return 'bg-gray-100 text-gray-800';
      case BillStatus.Pending:
        return 'bg-yellow-100 text-yellow-800';
      case BillStatus.PartiallyPaid:
        return 'bg-blue-100 text-blue-800';
      case BillStatus.Paid:
        return 'bg-green-100 text-green-800';
      case BillStatus.Overdue:
        return 'bg-red-100 text-red-800';
      case BillStatus.Void:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };



  // Handle batch actions
  const handleBatchAction = async (action: string, ids: string[]) => {
    try {
      switch (action) {
        case 'status':
          setIsStatusDialogOpen(true);
          break;

        case 'delete':
          const deleteResult = await batchDeleteBills({ ids }).unwrap();
          toast({
            title: t('notifications.success'),
            description: t('notifications.batchDeleteSuccess', { count: deleteResult.deleted_count }),
            variant: 'default',
          });
          break;
      }
    } catch (error) {
      console.error(`Failed to perform batch action ${action}:`, error);
      toast({
        title: t('notifications.error'),
        description: t('notifications.failedAction', { action }),
        variant: 'destructive',
      });
    }
  };

  // Handle status update
  const handleStatusUpdate = async (ids: string[]) => {
    if (selectedStatus === '') return;

    try {
      const result = await batchUpdateBills({
        bill_ids: ids,
        status: selectedStatus as BillStatus
      }).unwrap();

      toast({
        title: t('notifications.success'),
        description: t('notifications.batchUpdateSuccess', { count: result.updated_count }),
        variant: 'default',
      });

      setIsStatusDialogOpen(false);
      setSelectedStatus('');
    } catch (error) {
      console.error('Failed to update bill status:', error);
      toast({
        title: t('notifications.error'),
        description: t('notifications.batchUpdateError'),
        variant: 'destructive',
      });
    }
  };

  // Note: Pagination and sorting are now handled by useAdvancedPagination

  // ✅ Clear filters using advanced pagination
  const clearFilters = () => {
    actions.handleSearch('');
    setStatusFilter('');
    setVendorFilter('');
    setStartDate(undefined);
    setEndDate(undefined);
    setAmountMin('');
    setAmountMax('');
    setOverdueOnly(false);
    actions.setPage(1);
  };

  // Handle add bill
  const handleAddBill = () => {
    setSelectedBill(null);
    setIsFormDialogOpen(true);
  };

  // Handle edit bill
  const handleEditBill = (bill: any) => {
    setSelectedBill(bill);
    setIsFormDialogOpen(true);
  };

  return (
      <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <h1 className="text-3xl font-bold">{t('title')}</h1>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
              {viewMode === 'grid' ? <List className="mr-2 h-4 w-4" /> : <Grid className="mr-2 h-4 w-4" />}
              {viewMode === 'grid' ? t('actions.listView') : t('actions.gridView')}
            </Button>
            <Button onClick={handleAddBill}>
              <Plus className="mr-2 h-4 w-4" />
              {t('actions.addNewBill')}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{t('filters.title')}</CardTitle>
            <CardDescription>{t('filters.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="search"
                  placeholder={t('filters.search')}
                  className="pl-8"
                  value={state.search}
                  onChange={(e) => actions.handleSearch(e.target.value)}
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allStatuses')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{t('filters.allStatuses')}</SelectItem>
                  {Object.values(BillStatus).map(status => (
                    <SelectItem key={status} value={status}>
                      {t(`status.${status.toLowerCase()}`)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={vendorFilter} onValueChange={setVendorFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allVendors')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{t('filters.allVendors')}</SelectItem>
                  {vendors.map(vendor => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="overdue-only"
                  checked={overdueOnly}
                  onChange={(e) => setOverdueOnly(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="overdue-only">{t('filters.overdueOnly')}</Label>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div className="flex space-x-2">
                <DatePicker
                  date={startDate}
                  setDate={setStartDate}
                  placeholder={t('filters.startDate')}
                />
                <DatePicker
                  date={endDate}
                  setDate={setEndDate}
                  placeholder={t('filters.endDate')}
                />
              </div>

              <div className="flex space-x-2">
                <Input
                  type="number"
                  placeholder={t('filters.minAmount')}
                  value={amountMin}
                  onChange={(e) => setAmountMin(e.target.value)}
                />
                <Input
                  type="number"
                  placeholder={t('filters.maxAmount')}
                  value={amountMax}
                  onChange={(e) => setAmountMax(e.target.value)}
                />
              </div>
            </div>

            <div className="flex justify-between items-center">
              <Button variant="outline" onClick={clearFilters}>
                {t('filters.clear')}
              </Button>
              <div className="flex items-center space-x-2">
                <Label htmlFor="pageSize">{t('pagination.pageSize')}:</Label>
                <Select value={state.limit.toString()} onValueChange={(value) => actions.setLimit(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bills Table/Grid */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>{t('table.title')}</CardTitle>
                <CardDescription>
                  {pagination && t('table.showing', {
                    from: (pagination.page - 1) * pagination.limit + 1,
                    to: Math.min(pagination.page * pagination.limit, pagination.total),
                    total: pagination.total
                  })}
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Label>{t('table.sortBy')}:</Label>
                <Select value={state.sortBy} onValueChange={(value) => actions.handleSort(value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="billNumber">{t('table.billNumber')}</SelectItem>
                    <SelectItem value="billDate">{t('table.billDate')}</SelectItem>
                    <SelectItem value="dueDate">{t('table.dueDate')}</SelectItem>
                    <SelectItem value="totalAmount">{t('table.totalAmount')}</SelectItem>
                    <SelectItem value="status">{t('table.status')}</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => actions.handleSort(state.sortBy)}
                >
                  {state.sortOrder === 'asc' ? '↑' : '↓'}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoadingBills ? (
              <div className="space-y-2">
                {Array.from({ length: state.limit }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500">{t('errors.loadingFailed')}</p>
                <Button variant="outline" onClick={() => window.location.reload()}>
                  {t('actions.retry')}
                </Button>
              </div>
            ) : viewMode === 'list' ? (
              // ✅ Replace entire table with PaginatedTable
              <PaginatedTable
                data={bills}
                columns={columns}
                paginationInfo={pagination}
                isLoading={isLoadingBills}
                error={error}
                isFetching={isFetching}

                // Search functionality
                searchable
                searchValue={state.search}
                onSearchChange={actions.handleSearch}
                searchPlaceholder="Search bills..."

                // Sorting
                sortBy={state.sortBy}
                sortOrder={state.sortOrder}
                onSortChange={actions.handleSort}

                // Pagination
                currentPage={state.page}
                pageSize={state.limit}
                onPageChange={actions.setPage}
                onPageSizeChange={actions.setLimit}

                // Display
                title="Bills"
                emptyMessage="No bills found"
              />
            ) : (
              // Grid view
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {bills.map(bill => (
                  <Card key={bill.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center space-x-2">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <FileText className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{bill.billNumber}</CardTitle>
                            <CardDescription>{getVendorName(bill.vendor_id)}</CardDescription>
                          </div>
                        </div>
                        <Badge className={getStatusBadgeClass(bill.status)}>
                          {t(`status.${bill.status.toLowerCase()}`)}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-2 mb-4">
                        <div>
                          <p className="text-sm text-muted-foreground">{t('billCard.billDate')}</p>
                          <p className="font-medium">{formatDate(new Date(bill.billDate))}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{t('billCard.dueDate')}</p>
                          <p className="font-medium">{formatDate(new Date(bill.dueDate))}</p>
                        </div>
                        <div className="col-span-2">
                          <p className="text-sm text-muted-foreground">{t('billCard.totalAmount')}</p>
                          <p className="font-medium text-lg">{formatCurrency(bill.totalAmount)}</p>
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <BatchSelectionCheckbox id={bill.id} />
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/bills/${bill.id}`}>
                            <Eye className="h-4 w-4 mr-1" />
                            {t('billCard.actions.view')}
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleEditBill(bill)}>
                          <Edit className="h-4 w-4 mr-1" />
                          {t('billCard.actions.edit')}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-muted-foreground">
              {t('pagination.showing', {
                from: (pagination.page - 1) * pagination.limit + 1,
                to: Math.min(pagination.page * pagination.limit, pagination.total),
                total: pagination.total
              })}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => actions.setPage(state.page - 1)}
                disabled={!pagination.hasPreviousPage}
              >
                <ChevronLeft className="h-4 w-4" />
                {t('pagination.previous')}
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={page === state.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => actions.setPage(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => actions.setPage(state.page + 1)}
                disabled={!pagination.hasNextPage}
              >
                {t('pagination.next')}
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Batch Action Bar */}
        <BatchActionBar
          entityName={t('batchActions.entityName')}
          actions={[
            { id: 'status', label: t('actions.updateStatus') },
            { id: 'delete', label: t('actions.deleteBills') },
          ]}
          onAction={handleBatchAction}
        />

        {/* Status Update Dialog */}
        <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('dialog.batchUpdate.title')}</DialogTitle>
              <DialogDescription>
                {t('dialog.batchUpdate.description')}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="status">{t('table.status')}</Label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder={t('dialogs.updateStatus.selectStatus')} />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(BillStatus).map(status => (
                      <SelectItem key={status} value={status}>
                        {t(`status.${status.toLowerCase()}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>
                {t('dialog.buttons.cancel')}
              </Button>
              <Button onClick={() => handleStatusUpdate(getSelectedIds())}>
                {t('dialog.buttons.update')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Bill Form Dialog */}
        {isFormDialogOpen && (
          <BillFormDialog
            isOpen={isFormDialogOpen}
            onClose={() => setIsFormDialogOpen(false)}
            bill={selectedBill}
          />
        )}

        {/* Bill Delete Dialog */}
        <BillDeleteDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => {
            setIsDeleteDialogOpen(false);
            setBillToDelete(null);
          }}
          bill={billToDelete}
        />
      </div>
  );
}