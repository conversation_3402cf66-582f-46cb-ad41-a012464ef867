import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { configureStore } from '@reduxjs/toolkit'
import { billsApi, type BillsQueryParams } from '@/redux/services/billsApi'
import { BillStatus } from '@/lib/types'

// Mock data
const mockBills = [
  {
    id: 'bill-1',
    restaurant_id: 'restaurant-1',
    vendor_id: 'vendor-1',
    billNumber: 'BILL-001',
    billDate: '2024-01-15',
    dueDate: '2024-02-15',
    totalAmount: 1500,
    taxAmount: 150,
    amountDue: 1500,
    status: BillStatus.Pending,
    description: 'Office supplies',
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: 'bill-2',
    restaurant_id: 'restaurant-1',
    vendor_id: 'vendor-2',
    billNumber: 'BILL-002',
    billDate: '2024-01-20',
    dueDate: '2024-02-20',
    totalAmount: 2750,
    taxAmount: 275,
    amountDue: 2750,
    status: BillStatus.Draft,
    description: 'Equipment rental',
    created_at: '2024-01-20T00:00:00Z',
    updated_at: '2024-01-20T00:00:00Z',
  },
]

const mockBillsResponse = {
  bills: mockBills,
  pagination: {
    total: 2,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNextPage: false,
    hasPreviousPage: false,
  },
}

const mockBillPayments = [
  {
    id: 'payment-1',
    bill_id: 'bill-1',
    amount: 750,
    paymentDate: '2024-01-25',
    paymentMethod: 'Bank Transfer',
    reference: 'TXN-001',
    created_at: '2024-01-25T00:00:00Z',
    updated_at: '2024-01-25T00:00:00Z',
  },
]

const mockBillPaymentsResponse = {
  payments: mockBillPayments,
  pagination: {
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNextPage: false,
    hasPreviousPage: false,
  },
}

// Setup MSW server
const server = setupServer(
  // Get bills with filters, sort, and pagination
  rest.get('/api/bills', (req, res, ctx) => {
    const url = new URL(req.url)
    const search = url.searchParams.get('search')
    const status = url.searchParams.get('status')
    const vendorId = url.searchParams.get('vendor_id')
    const startDate = url.searchParams.get('start_date')
    const endDate = url.searchParams.get('end_date')
    const amountMin = url.searchParams.get('amount_min')
    const amountMax = url.searchParams.get('amount_max')
    const overdueOnly = url.searchParams.get('overdue_only')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const sortBy = url.searchParams.get('sortBy') || 'billDate'
    const sortOrder = url.searchParams.get('sortOrder') || 'desc'

    let filteredBills = [...mockBills]

    // Apply search filter
    if (search) {
      filteredBills = filteredBills.filter(bill =>
        bill.billNumber.toLowerCase().includes(search.toLowerCase()) ||
        bill.description?.toLowerCase().includes(search.toLowerCase())
      )
    }

    // Apply status filter
    if (status) {
      filteredBills = filteredBills.filter(bill => bill.status === status)
    }

    // Apply vendor filter
    if (vendorId) {
      filteredBills = filteredBills.filter(bill => bill.vendor_id === vendorId)
    }

    // Apply date range filters
    if (startDate) {
      filteredBills = filteredBills.filter(bill => bill.billDate >= startDate)
    }
    if (endDate) {
      filteredBills = filteredBills.filter(bill => bill.billDate <= endDate)
    }

    // Apply amount range filters
    if (amountMin) {
      filteredBills = filteredBills.filter(bill => bill.totalAmount >= parseFloat(amountMin))
    }
    if (amountMax) {
      filteredBills = filteredBills.filter(bill => bill.totalAmount <= parseFloat(amountMax))
    }

    // Apply overdue filter
    if (overdueOnly === 'true') {
      const today = new Date().toISOString().split('T')[0]
      filteredBills = filteredBills.filter(bill => 
        bill.dueDate < today && bill.status !== BillStatus.Paid
      )
    }

    // Apply sorting
    filteredBills.sort((a, b) => {
      let aValue: any = a[sortBy as keyof typeof a]
      let bValue: any = b[sortBy as keyof typeof b]

      if (sortBy === 'totalAmount' || sortBy === 'amountDue') {
        aValue = Number(aValue)
        bValue = Number(bValue)
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedBills = filteredBills.slice(startIndex, endIndex)

    const response = {
      bills: paginatedBills,
      pagination: {
        total: filteredBills.length,
        page,
        limit,
        totalPages: Math.ceil(filteredBills.length / limit),
        hasNextPage: endIndex < filteredBills.length,
        hasPreviousPage: page > 1,
      },
    }

    return res(ctx.json(response))
  }),

  // Get single bill
  rest.get('/api/bills/:id', (req, res, ctx) => {
    const { id } = req.params
    const bill = mockBills.find(b => b.id === id)
    
    if (!bill) {
      return res(ctx.status(404), ctx.json({ error: 'Bill not found' }))
    }

    return res(ctx.json(bill))
  }),

  // Create bill
  rest.post('/api/bills', (req, res, ctx) => {
    return res(ctx.json({
      id: 'new-bill-id',
      ...req.body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }))
  }),

  // Update bill
  rest.put('/api/bills/:id', (req, res, ctx) => {
    const { id } = req.params
    const bill = mockBills.find(b => b.id === id)
    
    if (!bill) {
      return res(ctx.status(404), ctx.json({ error: 'Bill not found' }))
    }

    return res(ctx.json({
      ...bill,
      ...req.body,
      updated_at: new Date().toISOString(),
    }))
  }),

  // Delete bill
  rest.delete('/api/bills/:id', (req, res, ctx) => {
    const { id } = req.params
    const bill = mockBills.find(b => b.id === id)
    
    if (!bill) {
      return res(ctx.status(404), ctx.json({ error: 'Bill not found' }))
    }

    return res(ctx.status(204))
  }),

  // Batch update bills
  rest.post('/api/bills/batch-update', (req, res, ctx) => {
    return res(ctx.json({ updated_count: 2 }))
  }),

  // Batch delete bills
  rest.post('/api/bills/batch-delete', (req, res, ctx) => {
    return res(ctx.json({ deleted_count: 2 }))
  }),

  // Get bill payments
  rest.get('/api/bills/payments', (req, res, ctx) => {
    const url = new URL(req.url)
    const billId = url.searchParams.get('bill_id')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')

    let filteredPayments = [...mockBillPayments]

    if (billId) {
      filteredPayments = filteredPayments.filter(payment => payment.bill_id === billId)
    }

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPayments = filteredPayments.slice(startIndex, endIndex)

    const response = {
      payments: paginatedPayments,
      pagination: {
        total: filteredPayments.length,
        page,
        limit,
        totalPages: Math.ceil(filteredPayments.length / limit),
        hasNextPage: endIndex < filteredPayments.length,
        hasPreviousPage: page > 1,
      },
    }

    return res(ctx.json(response))
  }),

  // Create bill payment
  rest.post('/api/bills/payments', (req, res, ctx) => {
    return res(ctx.json({
      id: 'new-payment-id',
      ...req.body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }))
  }),
)

// Create test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      [billsApi.reducerPath]: billsApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(billsApi.middleware),
  })
}

describe('Bills API', () => {
  beforeEach(() => {
    server.listen()
  })

  afterEach(() => {
    server.resetHandlers()
  })

  afterEach(() => {
    server.close()
  })

  describe('getBills', () => {
    it('fetches bills without parameters', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate()
      )

      expect(result.data).toEqual(mockBillsResponse)
      expect(result.data?.bills).toHaveLength(2)
      expect(result.data?.pagination.total).toBe(2)
    })

    it('fetches bills with search parameter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate({ search: 'BILL-001' })
      )

      expect(result.data?.bills).toHaveLength(1)
      expect(result.data?.bills[0].billNumber).toBe('BILL-001')
    })

    it('fetches bills with status filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate({ status: BillStatus.Pending })
      )

      expect(result.data?.bills).toHaveLength(1)
      expect(result.data?.bills[0].status).toBe(BillStatus.Pending)
    })

    it('fetches bills with vendor filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate({ vendor_id: 'vendor-1' })
      )

      expect(result.data?.bills).toHaveLength(1)
      expect(result.data?.bills[0].vendor_id).toBe('vendor-1')
    })

    it('fetches bills with date range filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate({ 
          start_date: '2024-01-01',
          end_date: '2024-01-31'
        })
      )

      expect(result.data?.bills).toHaveLength(2)
    })

    it('fetches bills with amount range filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate({ 
          amount_min: 2000,
          amount_max: 3000
        })
      )

      expect(result.data?.bills).toHaveLength(1)
      expect(result.data?.bills[0].totalAmount).toBe(2750)
    })

    it('fetches bills with pagination', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate({ page: 1, limit: 1 })
      )

      expect(result.data?.bills).toHaveLength(1)
      expect(result.data?.pagination.page).toBe(1)
      expect(result.data?.pagination.limit).toBe(1)
      expect(result.data?.pagination.totalPages).toBe(2)
    })

    it('fetches bills with sorting', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBills.initiate({
          sortBy: 'totalAmount',
          sortOrder: 'desc'
        })
      )

      expect(result.data?.bills).toHaveLength(2)
      expect(result.data?.bills[0].totalAmount).toBe(2750)
      expect(result.data?.bills[1].totalAmount).toBe(1500)
    })
  })

  describe('getBillById', () => {
    it('fetches a single bill by ID', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBillById.initiate('bill-1')
      )

      expect(result.data).toEqual(mockBills[0])
    })

    it('returns error for non-existent bill', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBillById.initiate('non-existent')
      )

      expect(result.error).toBeDefined()
    })
  })

  describe('createBill', () => {
    it('creates a new bill', async () => {
      const store = createTestStore()
      const newBill = {
        vendor_id: 'vendor-1',
        bill_number: 'BILL-003',
        bill_date: '2024-02-01',
        due_date: '2024-03-01',
        total_amount: 1000,
        tax_amount: 100,
        description: 'New bill',
      }
      
      const result = await store.dispatch(
        billsApi.endpoints.createBill.initiate(newBill)
      )

      expect(result.data).toMatchObject(newBill)
      expect(result.data?.id).toBe('new-bill-id')
    })
  })

  describe('updateBill', () => {
    it('updates an existing bill', async () => {
      const store = createTestStore()
      const updateData = { description: 'Updated description' }
      
      const result = await store.dispatch(
        billsApi.endpoints.updateBill.initiate({
          id: 'bill-1',
          body: updateData
        })
      )

      expect(result.data?.description).toBe('Updated description')
    })
  })

  describe('deleteBill', () => {
    it('deletes a bill', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.deleteBill.initiate('bill-1')
      )

      expect(result.data).toBeUndefined() // 204 No Content
    })
  })

  describe('batchUpdateBills', () => {
    it('updates multiple bills', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.batchUpdateBills.initiate({
          bill_ids: ['bill-1', 'bill-2'],
          status: BillStatus.Paid
        })
      )

      expect(result.data?.updated_count).toBe(2)
    })
  })

  describe('batchDeleteBills', () => {
    it('deletes multiple bills', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.batchDeleteBills.initiate({
          ids: ['bill-1', 'bill-2']
        })
      )

      expect(result.data?.deleted_count).toBe(2)
    })
  })

  describe('getBillPayments', () => {
    it('fetches bill payments without parameters', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBillPayments.initiate()
      )

      expect(result.data).toEqual(mockBillPaymentsResponse)
      expect(result.data?.payments).toHaveLength(1)
    })

    it('fetches bill payments with bill ID filter', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBillPayments.initiate({ bill_id: 'bill-1' })
      )

      expect(result.data?.payments).toHaveLength(1)
      expect(result.data?.payments[0].bill_id).toBe('bill-1')
    })

    it('fetches bill payments with pagination', async () => {
      const store = createTestStore()
      
      const result = await store.dispatch(
        billsApi.endpoints.getBillPayments.initiate({ page: 1, limit: 5 })
      )

      expect(result.data?.pagination.page).toBe(1)
      expect(result.data?.pagination.limit).toBe(5)
    })
  })

  describe('createBillPayment', () => {
    it('creates a new bill payment', async () => {
      const store = createTestStore()
      const newPayment = {
        bill_id: 'bill-1',
        amount: 500,
        payment_date: '2024-02-01',
        payment_method: 'Credit Card',
        reference: 'TXN-002',
      }
      
      const result = await store.dispatch(
        billsApi.endpoints.createBillPayment.initiate(newPayment)
      )

      expect(result.data).toMatchObject(newPayment)
      expect(result.data?.id).toBe('new-payment-id')
    })
  })
})
