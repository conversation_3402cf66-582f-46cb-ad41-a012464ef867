import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { payrollApi } from '@/redux/services/payrollApi'
import { PayrollRunStatus } from '@/lib/types'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

// Create a test store with only payroll API
const createTestStore = () => {
  return configureStore({
    reducer: {
      [payrollApi.reducerPath]: payrollApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(payrollApi.middleware),
  })
}

// Test wrapper component
const createWrapper = () => {
  const store = createTestStore()
  return ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>{children}</Provider>
  )
}

describe('Payroll RTK Query Integration', () => {
  beforeEach(() => {
    mockFetch.mockClear()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should fetch payroll runs successfully', async () => {
    const mockPayrollRuns = {
      runs: [
        {
          payroll_run_id: '1',
          pay_period_start_date: '2024-01-01',
          pay_period_end_date: '2024-01-15',
          payment_date: '2024-01-20',
          gross_wages: 10000,
          employee_taxes: 2000,
          net_pay: 8000,
          status: PayrollRunStatus.Draft,
        },
      ],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockPayrollRuns,
    })

    const wrapper = createWrapper()
    const { result } = renderHook(
      () => payrollApi.useGetPayrollRunsQuery({ page: 1, limit: 10 }),
      { wrapper }
    )

    expect(result.current.isLoading).toBe(true)

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toEqual(mockPayrollRuns)
    expect(result.current.error).toBeUndefined()
  })

  it('should handle API errors gracefully', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    const wrapper = createWrapper()
    const { result } = renderHook(
      () => payrollApi.useGetPayrollRunsQuery({ page: 1, limit: 10 }),
      { wrapper }
    )

    expect(result.current.isLoading).toBe(true)

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.error).toBeDefined()
    expect(result.current.data).toBeUndefined()
  })

  it('should update payroll run status', async () => {
    const mockUpdatedRun = {
      payroll_run_id: '1',
      status: PayrollRunStatus.Processed,
      pay_period_start_date: '2024-01-01',
      pay_period_end_date: '2024-01-15',
      payment_date: '2024-01-20',
      gross_wages: 10000,
      employee_taxes: 2000,
      net_pay: 8000,
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockUpdatedRun,
    })

    const wrapper = createWrapper()
    const { result } = renderHook(
      () => payrollApi.useUpdatePayrollRunMutation(),
      { wrapper }
    )

    const [updatePayrollRun] = result.current

    await updatePayrollRun({
      id: '1',
      body: { status: PayrollRunStatus.Processed },
    })

    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/payroll/runs/1'),
      expect.objectContaining({
        method: 'PUT',
        body: JSON.stringify({ status: PayrollRunStatus.Processed }),
      })
    )
  })

  it('should delete payroll run', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    })

    const wrapper = createWrapper()
    const { result } = renderHook(
      () => payrollApi.useDeletePayrollRunMutation(),
      { wrapper }
    )

    const [deletePayrollRun] = result.current

    await deletePayrollRun('1')

    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/payroll/runs/1'),
      expect.objectContaining({
        method: 'DELETE',
      })
    )
  })

  it('should handle pagination correctly', async () => {
    const mockPaginatedData = {
      runs: [],
      pagination: {
        total: 50,
        page: 2,
        limit: 10,
        totalPages: 5,
        hasNextPage: true,
        hasPreviousPage: true,
      },
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockPaginatedData,
    })

    const wrapper = createWrapper()
    const { result } = renderHook(
      () => payrollApi.useGetPayrollRunsQuery({ page: 2, limit: 10 }),
      { wrapper }
    )

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data?.pagination.page).toBe(2)
    expect(result.current.data?.pagination.hasNextPage).toBe(true)
    expect(result.current.data?.pagination.hasPreviousPage).toBe(true)
  })

  it('should handle filtering parameters', async () => {
    const mockFilteredData = {
      runs: [],
      pagination: {
        total: 5,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockFilteredData,
    })

    const wrapper = createWrapper()
    const { result } = renderHook(
      () => payrollApi.useGetPayrollRunsQuery({
        page: 1,
        limit: 10,
        status: PayrollRunStatus.Draft,
        date: '2024-01-01',
      }),
      { wrapper }
    )

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining('status=Draft'),
      expect.any(Object)
    )
    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining('date=2024-01-01'),
      expect.any(Object)
    )
  })
})
