'use client';

import { useTranslations } from 'next-intl';
import { useDeleteVendorMutation } from '@/redux/services/vendorsApi';
import { DeleteDialog } from '@/components/ui/delete-dialog';

interface VendorDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  vendor: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  } | null;
  onDeleteSuccess?: () => void;
}

export function VendorDeleteDialog({ isOpen, onClose, vendor, onDeleteSuccess }: VendorDeleteDialogProps) {
  const t = useTranslations('vendors');
  const [deleteVendor] = useDeleteVendorMutation();

  const handleDelete = async () => {
    if (!vendor) return;
    await deleteVendor(vendor.id).unwrap();
  };

  const itemDetails = vendor ? (
    <>
      <p className="font-medium">
        You are about to delete vendor: {vendor.name}
      </p>
      {vendor.email && (
        <p className="text-sm text-muted-foreground">
          Email: <span className="font-medium">{vendor.email}</span>
        </p>
      )}
      {vendor.phone && (
        <p className="text-sm text-muted-foreground">
          Phone: <span className="font-medium">{vendor.phone}</span>
        </p>
      )}
    </>
  ) : null;

  return (
    <DeleteDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={handleDelete}
      title="Delete Vendor"
      description="Are you sure you want to delete this vendor? This action cannot be undone and will affect all related bills and transactions."
      itemDetails={itemDetails}
      canDelete={true}
      confirmButtonText="Delete"
      cancelButtonText="Cancel"
      loadingText="Deleting..."
      successMessage="Vendor deleted successfully"
      errorMessage="Failed to delete vendor"
      onDeleteSuccess={onDeleteSuccess}
    />
  );
}
