'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ExpenseForm } from '@/components/expenses/ExpenseForm';
import { Expense } from '@/redux/services/expensesApi';

interface ExpenseFormDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  expense?: Expense;
  branchId: string;
  onSuccess?: () => void;
}

export function ExpenseFormDialog({
  isOpen,
  onOpenChange,
  expense,
  branchId,
  onSuccess,
}: ExpenseFormDialogProps) {
  const handleSuccess = () => {
    onSuccess?.();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {expense ? 'Edit Expense' : 'Create New Expense'}
          </DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <ExpenseForm
            expense={expense}
            branchId={branchId}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
