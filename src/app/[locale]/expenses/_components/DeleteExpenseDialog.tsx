'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useTranslations } from 'next-intl';
import { FormattedCurrency, DateTime } from '@/components/common';
import { Expense } from '@/redux/services/expensesApi';

interface DeleteExpenseDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading: boolean;
  selectedExpense: Expense | null;
  onCancel: () => void;
}

export function DeleteExpenseDialog({
  isOpen,
  onOpenChange,
  onConfirm,
  isLoading,
  selectedExpense,
  onCancel,
}: DeleteExpenseDialogProps) {
  const t = useTranslations('expenses');

  // Check if expense can be deleted (only pending expenses)
  const canDelete = selectedExpense?.status === 'PENDING';

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      case 'PAID': return 'bg-blue-100 text-blue-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('dialog.delete.title')}</DialogTitle>
          <DialogDescription>
            {canDelete
              ? t('dialog.delete.description')
              : 'Only pending expenses can be deleted. This expense cannot be deleted because it has been processed.'
            }
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          {selectedExpense && (
            <div className="space-y-3">
              <p><strong>{t('dialog.details.date')}</strong> <DateTime date={selectedExpense.date} format="short" /></p>
              <p><strong>{t('dialog.details.description')}</strong> {selectedExpense.description}</p>
              <p><strong>{t('dialog.details.amount')}</strong> <FormattedCurrency amount={selectedExpense.amount} /></p>
              <div className="flex items-center gap-2">
                <strong>Status:</strong>
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(selectedExpense.status)}`}>
                  {selectedExpense.status.charAt(0) + selectedExpense.status.slice(1).toLowerCase()}
                </span>
              </div>
              {!canDelete && (
                <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <p className="text-sm text-amber-800">
                    ⚠️ This expense cannot be deleted because it is in "{selectedExpense.status.toLowerCase()}" status.
                    Only expenses with "pending" status can be deleted.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            {t('dialog.buttons.cancel')}
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={onConfirm}
            disabled={isLoading || !canDelete}
          >
            {isLoading ? t('dialog.buttons.deleting') : t('dialog.buttons.delete')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
