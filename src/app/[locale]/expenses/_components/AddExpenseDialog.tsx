'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { useTranslations } from 'next-intl';

interface AddExpenseDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  formError: string | null;
  
  // Form state
  expenseDate: Date | undefined;
  setExpenseDate: (date: Date | undefined) => void;
  vendorId: string;
  setVendorId: (value: string) => void;
  category: string;
  setCategory: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  amount: string;
  setAmount: (value: string) => void;
  paymentMethod: string;
  setPaymentMethod: (value: string) => void;
  receiptUrl: string;
  setReceiptUrl: (value: string) => void;
  
  // Data
  vendors: any[];
  expenseCategories: string[];
  paymentMethods: string[];
  
  // Actions
  onCancel: () => void;
}

export function AddExpenseDialog({
  isOpen,
  onOpenChange,
  onSubmit,
  isLoading,
  formError,
  expenseDate,
  setExpenseDate,
  vendorId,
  setVendorId,
  category,
  setCategory,
  description,
  setDescription,
  amount,
  setAmount,
  paymentMethod,
  setPaymentMethod,
  receiptUrl,
  setReceiptUrl,
  vendors,
  expenseCategories,
  paymentMethods,
  onCancel,
}: AddExpenseDialogProps) {
  const t = useTranslations('expenses');

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t('dialog.add.title')}</DialogTitle>
          <DialogDescription>
            {t('dialog.add.description')}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="date" className="text-right">
                {t('dialog.form.date')}
              </Label>
              <div className="col-span-3">
                <DatePicker
                  date={expenseDate}
                  setDate={setExpenseDate}
                  className="w-full"
                  id="date"
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="vendor" className="text-right">
                {t('dialog.form.vendor')}
              </Label>
              <div className="col-span-3">
                <Select value={vendorId} onValueChange={setVendorId}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('dialog.form.selectVendor')} />
                  </SelectTrigger>
                  <SelectContent>
                    {vendors && vendors.length > 0 ? (
                      vendors.map((vendor) => (
                        <SelectItem key={vendor.id} value={vendor.id}>
                          {vendor.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="loading" disabled>
                        {t('dialog.form.loadingVendors')}
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="category" className="text-right">
                {t('dialog.form.category')}
              </Label>
              <div className="col-span-3">
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('dialog.form.selectCategory')} />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseCategories.map((cat) => (
                      <SelectItem key={cat} value={cat}>
                        {cat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                {t('dialog.form.description')}
              </Label>
              <Input
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                {t('dialog.form.amount')}
              </Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="paymentMethod" className="text-right">
                {t('dialog.form.paymentMethod')}
              </Label>
              <div className="col-span-3">
                <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('dialog.form.selectPaymentMethod')} />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method} value={method}>
                        {method}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="receiptUrl" className="text-right">
                {t('dialog.form.receiptUrl')}
              </Label>
              <Input
                id="receiptUrl"
                value={receiptUrl}
                onChange={(e) => setReceiptUrl(e.target.value)}
                className="col-span-3"
                placeholder={t('dialog.form.receiptUrlPlaceholder')}
              />
            </div>

            {formError && <p className="text-red-600 text-sm col-span-4">
              {formError === 'All fields except Receipt URL are required.'
                ? t('dialog.form.validationError')
                : formError === 'Amount must be a positive number.'
                ? t('dialog.form.amountError')
                : formError}
            </p>}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              {t('dialog.buttons.cancel')}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t('dialog.buttons.saving') : t('dialog.buttons.save')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
