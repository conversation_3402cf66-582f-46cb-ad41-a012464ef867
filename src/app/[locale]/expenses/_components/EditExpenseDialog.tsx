'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { useTranslations } from 'next-intl';

interface EditExpenseDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  formError: string | null;
  
  // Form state
  editExpenseDate: Date | undefined;
  setEditExpenseDate: (date: Date | undefined) => void;
  editVendorId: string;
  setEditVendorId: (value: string) => void;
  editCategory: string;
  setEditCategory: (value: string) => void;
  editDescription: string;
  setEditDescription: (value: string) => void;
  editAmount: string;
  setEditAmount: (value: string) => void;
  editPaymentMethod: string;
  setEditPaymentMethod: (value: string) => void;
  editReceiptUrl: string;
  setEditReceiptUrl: (value: string) => void;
  editStatus: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID';
  setEditStatus: (value: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID') => void;
  
  // Data
  vendors: any[];
  expenseCategories: string[];
  paymentMethods: string[];
  
  // Actions
  onCancel: () => void;
}

export function EditExpenseDialog({
  isOpen,
  onOpenChange,
  onSubmit,
  isLoading,
  formError,
  editExpenseDate,
  setEditExpenseDate,
  editVendorId,
  setEditVendorId,
  editCategory,
  setEditCategory,
  editDescription,
  setEditDescription,
  editAmount,
  setEditAmount,
  editPaymentMethod,
  setEditPaymentMethod,
  editReceiptUrl,
  setEditReceiptUrl,
  editStatus,
  setEditStatus,
  vendors,
  expenseCategories,
  paymentMethods,
  onCancel,
}: EditExpenseDialogProps) {
  const t = useTranslations('expenses');

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t('dialog.edit.title')}</DialogTitle>
          <DialogDescription>
            {t('dialog.edit.description')}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-date" className="text-right">
                {t('dialog.form.date')}
              </Label>
              <div className="col-span-3">
                <DatePicker
                  date={editExpenseDate}
                  setDate={setEditExpenseDate}
                  className="w-full"
                  id="edit-date"
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-vendor" className="text-right">
                {t('dialog.form.vendor')}
              </Label>
              <div className="col-span-3">
                <Select
                  value={editVendorId}
                  onValueChange={setEditVendorId}
                  defaultValue={editVendorId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('dialog.form.selectVendor')} />
                  </SelectTrigger>
                  <SelectContent>
                    {vendors && vendors.length > 0 ? (
                      vendors.map((vendor) => (
                        <SelectItem key={vendor.id} value={vendor.id}>
                          {vendor.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="loading" disabled>
                        {t('dialog.form.loadingVendors')}
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-category" className="text-right">
                {t('dialog.form.category')}
              </Label>
              <div className="col-span-3">
                <Select
                  value={editCategory}
                  onValueChange={setEditCategory}
                  defaultValue={editCategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('dialog.form.selectCategory')} />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseCategories.map((cat) => (
                      <SelectItem key={cat} value={cat}>
                        {cat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right">
                {t('dialog.form.description')}
              </Label>
              <Input
                id="edit-description"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-amount" className="text-right">
                {t('dialog.form.amount')}
              </Label>
              <Input
                id="edit-amount"
                type="number"
                step="0.01"
                min="0.01"
                value={editAmount}
                onChange={(e) => setEditAmount(e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-paymentMethod" className="text-right">
                Payment Method*
              </Label>
              <div className="col-span-3">
                <Select
                  value={editPaymentMethod}
                  onValueChange={setEditPaymentMethod}
                  defaultValue={editPaymentMethod}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method} value={method}>
                        {method}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-status" className="text-right">
                Status*
              </Label>
              <div className="col-span-3">
                <Select
                  value={editStatus}
                  onValueChange={setEditStatus}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a status">
                      {editStatus ? editStatus.charAt(0) + editStatus.slice(1).toLowerCase() : "Select a status"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="APPROVED">Approved</SelectItem>
                    <SelectItem value="REJECTED">Rejected</SelectItem>
                    <SelectItem value="PAID">Paid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-receiptUrl" className="text-right">
                Receipt URL
              </Label>
              <Input
                id="edit-receiptUrl"
                value={editReceiptUrl}
                onChange={(e) => setEditReceiptUrl(e.target.value)}
                className="col-span-3"
                placeholder="https://example.com/receipt.pdf"
              />
            </div>

            {formError && <p className="text-red-600 text-sm col-span-4">{formError}</p>}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              {t('dialog.buttons.cancel')}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t('dialog.buttons.updating') : t('dialog.buttons.update')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
