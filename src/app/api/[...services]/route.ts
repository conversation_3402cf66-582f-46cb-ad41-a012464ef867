/**
 * Generic API proxy route for all backend services
 * Handles all HTTP methods and forwards requests to the backend API
 * Uses the [...services] catch-all pattern to proxy any API route
 *
 * Note: Auth routes (/api/auth/*) are handled separately and won't reach this proxy
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

// List of endpoints that should be handled by Next.js instead of proxied
const NEXTJS_ONLY_ENDPOINTS = [
  'auth/',
  'webhooks/',
  'setup-stripe',
  'subscriptions/',
  'upload', // File uploads might need special handling
  'test-session', // Development/testing routes
];

/**
 * Check if a service path should be handled by Next.js instead of proxied
 */
function shouldHandleInNextJS(servicePath: string): boolean {
  return NEXTJS_ONLY_ENDPOINTS.some(endpoint => servicePath.startsWith(endpoint));
}

/**
 * Generic handler for all HTTP methods
 */
async function handleRequest(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const resolvedParams = await params;
    const servicePath = resolvedParams.services.join('/');
    const { searchParams } = new URL(request.url);
    const method = request.method;

    // Debug endpoint to check session
    if (servicePath === 'debug-auth') {
      return NextResponse.json({
        hasSession: !!session,
        sessionUser: session?.user,
        accessToken: (session as any)?.accessToken ? 'present' : 'missing',
        accessTokenLength: (session as any)?.accessToken?.length || 0,
        sessionKeys: session ? Object.keys(session) : [],
        refreshToken: (session as any)?.refreshToken ? 'present' : 'missing'
      });
    }



    // Skip auth routes - they should be handled by their specific route handlers
    if (shouldHandleInNextJS(servicePath)) {
      return NextResponse.json(
        { error: 'Endpoint should be handled by Next.js route handlers' },
        { status: 400 }
      );
    }

    // Build the backend URL
    let backendUrl = `/${servicePath}`;

    // Add query parameters if they exist
    if (searchParams.toString()) {
      backendUrl += `?${searchParams.toString()}`;
    }

    console.log('Generic API proxy:', {
      method,
      servicePath,
      backendUrl,
      queryParams: Object.fromEntries(searchParams),
      hasSession: !!session,
      hasAccessToken: !!(session as any)?.accessToken
    });

    // Check authentication - if no session or access token, return 401
    if (!session || !(session as any)?.accessToken) {
      console.log('Authentication failed:', {
        hasSession: !!session,
        hasAccessToken: !!(session as any)?.accessToken,
        servicePath,
        sessionKeys: session ? Object.keys(session) : [],
        userEmail: session?.user?.email,
        accessTokenLength: (session as any)?.accessToken?.length || 0
      });

      // Return a more specific error message
      const errorMessage = !session
        ? 'No active session. Please log in.'
        : 'Invalid session. Please log in again.';

      return NextResponse.json(
        {
          error: errorMessage,
          code: 'AUTHENTICATION_REQUIRED',
          redirectTo: '/login'
        },
        { status: 401 }
      );
    }

    // Prepare request options
    const requestOptions: any = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    // Add body for methods that support it
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const body = await request.text();
        if (body) {
          requestOptions.body = body;
        }
      } catch (error) {
        console.warn('Failed to read request body:', error);
      }
    }

    // Forward request to backend using serverFetchClient which handles auth
    const response = await serverFetchClient(backendUrl, request, requestOptions);

    // Handle the response
    if (method === 'DELETE' && response.ok) {
      // For DELETE requests, return success message
      return NextResponse.json({ success: true });
    }

    // For other methods, return the data
    const data = await handleApiResponse(response);

    // Determine status code based on method
    let statusCode = 200;
    if (method === 'POST') {
      statusCode = 201;
    }

    return NextResponse.json(data, { status: statusCode });
  } catch (error) {
    console.error(`Error in ${request.method} /api/[...services]:`, error);

    // Determine error status - ensure it's a valid HTTP status code
    let status = 500;
    if (error instanceof Error && 'status' in error) {
      const errorStatus = (error as any).status;
      // Ensure status is a valid HTTP status code (200-599)
      if (errorStatus >= 200 && errorStatus <= 599) {
        status = errorStatus;
      } else if (errorStatus === 0) {
        // Network errors typically have status 0, map to 503 Service Unavailable
        status = 503;
      }
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : `Failed to ${request.method.toLowerCase()} data`
      },
      { status }
    );
  }
}

// Export all HTTP method handlers
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  });
}
