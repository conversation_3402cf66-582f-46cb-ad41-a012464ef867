/**
 * Handle API response and extract data
 * Provides consistent error handling for backend responses
 */
export async function handleApiResponse(response: Response): Promise<any> {
  try {
    // Handle 204 No Content responses
    if (response.status === 204) {
      return null;
    }

    // Try to parse JSON response
    const contentType = response.headers.get('content-type');
    let data: any;

    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      // For non-JSON responses, return as text
      data = await response.text();
    }

    // If response is not ok, throw an error with the data
    if (!response.ok) {
      const error = new Error(
        data?.error || 
        data?.message || 
        `HTTP ${response.status}: ${response.statusText}`
      ) as any;
      
      error.status = response.status;
      error.data = data;
      throw error;
    }

    return data;
  } catch (error) {
    // If it's already our custom error, re-throw it
    if (error instanceof Error && 'status' in error) {
      throw error;
    }

    // For other errors (like JSON parsing errors), create a new error
    const customError = new Error(
      error instanceof Error ? error.message : 'Failed to process response'
    ) as any;
    
    customError.status = response.status || 500;
    customError.data = null;
    throw customError;
  }
}

/**
 * Create a standardized error response for API routes
 */
export function createApiError(
  message: string,
  status: number = 500,
  data?: any
): Error {
  const error = new Error(message) as any;
  error.status = status;
  error.data = data;
  return error;
}

/**
 * Check if an error is a network/connection error
 */
export function isNetworkError(error: any): boolean {
  return (
    error instanceof TypeError ||
    error.message?.includes('fetch') ||
    error.message?.includes('network') ||
    error.code === 'ECONNREFUSED' ||
    error.code === 'ENOTFOUND'
  );
}

/**
 * Format error for consistent API responses
 */
export function formatApiError(error: any): {
  error: string;
  status: number;
  details?: any;
} {
  if (error && typeof error === 'object' && 'status' in error) {
    return {
      error: error.message || 'An error occurred',
      status: error.status || 500,
      details: error.data,
    };
  }

  if (isNetworkError(error)) {
    return {
      error: 'Service unavailable',
      status: 503,
      details: 'Unable to connect to backend service',
    };
  }

  return {
    error: error instanceof Error ? error.message : 'Unknown error',
    status: 500,
  };
}
