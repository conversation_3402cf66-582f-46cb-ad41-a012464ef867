import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';

const GO_BACKEND_URL = process.env.GO_BACKEND_URL || 'http://localhost:8050';

/**
 * Server-side fetch client that handles authentication
 * Uses NextAuth session to get the access token for backend requests
 */
export async function serverFetchClient(
  path: string,
  request: NextRequest,
  options: RequestInit = {}
): Promise<Response> {
  try {
    // Get the session to extract the access token
    const session = await getServerSession(authOptions);
    
    // Build the full URL
    const url = `${GO_BACKEND_URL}/api${path.startsWith('/') ? path : `/${path}`}`;
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'ADC-Account-Web',
      ...(options.headers as Record<string, string> || {}),
    };

    // Add authorization header if session has access token
    if ((session as any)?.accessToken) {
      const token = (session as any).accessToken;
      headers['Authorization'] = `Bearer ${token}`;
      console.log('Sending token to backend:', {
        url,
        tokenLength: token.length,
        tokenStart: token.substring(0, 30) + '...',
        hasSession: !!session,
        userEmail: session?.user?.email
      });
    } else {
      // Fallback to Authorization header from the original request
      const authHeader = request.headers.get('authorization');
      if (authHeader) {
        headers['Authorization'] = authHeader;
        console.log('Using request auth header');
      } else {
        console.log('No authorization token available:', {
          url,
          hasSession: !!session,
          sessionKeys: session ? Object.keys(session) : [],
          userEmail: session?.user?.email
        });
      }
    }

    // Forward important headers from the original request
    const forwardHeaders = [
      'x-forwarded-for',
      'x-real-ip',
      'x-forwarded-proto',
      'x-forwarded-host',
      'accept-language',
    ];

    forwardHeaders.forEach(headerName => {
      const value = request.headers.get(headerName);
      if (value) {
        headers[headerName] = value;
      }
    });

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers,
    });

    return response;
  } catch (error) {
    console.error('Server fetch client error:', error);
    throw error;
  }
}

/**
 * Helper function to get authorization header from NextAuth session
 */
export async function getAuthHeader(request?: NextRequest): Promise<string | null> {
  try {
    // Try to get session from NextAuth
    const session = await getServerSession(authOptions);
    if ((session as any)?.accessToken) {
      return `Bearer ${(session as any).accessToken}`;
    }

    // Fallback to Authorization header from request if provided
    if (request) {
      const authHeader = request.headers.get('authorization');
      if (authHeader) {
        return authHeader;
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting auth header:', error);
    return null;
  }
}
