import { AccountType } from './types';

export interface AccountTemplate {
  code: string;
  name: string;
  type: AccountType;
  description?: string;
  contra?: boolean;
}

export interface AccountCategory {
  name: string;
  type: AccountType;
  accounts: Record<string, AccountTemplate>;
}

export interface ChartOfAccountsTemplate {
  chart_of_accounts: Record<string, AccountCategory>;
}

export const defaultChartOfAccountsTemplate: ChartOfAccountsTemplate = {
  chart_of_accounts: {
    "1000_ASSETS": {
      name: "Assets",
      type: "Asset",
      accounts: {
        "1100": { code: "1100", name: "Current Assets", type: "CurrentAsset" },
        "1110": { code: "1110", name: "Cash and Cash Equivalents", type: "CurrentAsset" },
        "1111": { code: "1111", name: "Petty Cash", type: "CurrentAsset" },
        "1112": { code: "1112", name: "Checking Account", type: "CurrentAsset" },
        "1113": { code: "1113", name: "Savings Account", type: "CurrentAsset" },
        "1114": { code: "1114", name: "Money Market Account", type: "CurrentAsset" },
        "1120": { code: "1120", name: "Accounts Receivable", type: "CurrentAsset" },
        "1121": { code: "1121", name: "Trade Receivables", type: "CurrentAsset" },
        "1122": { code: "1122", name: "Other Receivables", type: "CurrentAsset" },
        "1125": { code: "1125", name: "Allowance for Doubtful Accounts", type: "CurrentAsset", contra: true },
        "1130": { code: "1130", name: "Inventory", type: "CurrentAsset" },
        "1131": { code: "1131", name: "Raw Materials", type: "CurrentAsset" },
        "1132": { code: "1132", name: "Work in Process", type: "CurrentAsset" },
        "1133": { code: "1133", name: "Finished Goods", type: "CurrentAsset" },
        "1140": { code: "1140", name: "Prepaid Expenses", type: "CurrentAsset" },
        "1141": { code: "1141", name: "Prepaid Insurance", type: "CurrentAsset" },
        "1142": { code: "1142", name: "Prepaid Rent", type: "CurrentAsset" },
        "1143": { code: "1143", name: "Prepaid Supplies", type: "CurrentAsset" },
        "1150": { code: "1150", name: "Short-term Investments", type: "CurrentAsset" },

        "1200": { code: "1200", name: "Non-Current Assets", type: "FixedAsset" },
        "1210": { code: "1210", name: "Property, Plant & Equipment", type: "FixedAsset" },
        "1211": { code: "1211", name: "Land", type: "FixedAsset" },
        "1212": { code: "1212", name: "Buildings", type: "FixedAsset" },
        "1213": { code: "1213", name: "Equipment", type: "FixedAsset" },
        "1214": { code: "1214", name: "Furniture & Fixtures", type: "FixedAsset" },
        "1215": { code: "1215", name: "Vehicles", type: "FixedAsset" },
        "1216": { code: "1216", name: "Computer Equipment", type: "FixedAsset" },
        "1220": { code: "1220", name: "Accumulated Depreciation", type: "FixedAsset", contra: true },
        "1221": { code: "1221", name: "Accumulated Depreciation - Buildings", type: "FixedAsset", contra: true },
        "1222": { code: "1222", name: "Accumulated Depreciation - Equipment", type: "FixedAsset", contra: true },
        "1223": { code: "1223", name: "Accumulated Depreciation - Furniture", type: "FixedAsset", contra: true },
        "1224": { code: "1224", name: "Accumulated Depreciation - Vehicles", type: "FixedAsset", contra: true },
        "1230": { code: "1230", name: "Intangible Assets", type: "IntangibleAsset" },
        "1231": { code: "1231", name: "Goodwill", type: "IntangibleAsset" },
        "1232": { code: "1232", name: "Patents", type: "IntangibleAsset" },
        "1233": { code: "1233", name: "Trademarks", type: "IntangibleAsset" },
        "1234": { code: "1234", name: "Software", type: "IntangibleAsset" },
        "1240": { code: "1240", name: "Long-term Investments", type: "OtherAsset" }
      }
    },

    "2000_LIABILITIES": {
      name: "Liabilities",
      type: "Liability",
      accounts: {
        "2100": { code: "2100", name: "Current Liabilities", type: "CurrentLiability" },
        "2110": { code: "2110", name: "Accounts Payable", type: "CurrentLiability" },
        "2111": { code: "2111", name: "Trade Payables", type: "CurrentLiability" },
        "2112": { code: "2112", name: "Other Payables", type: "CurrentLiability" },
        "2120": { code: "2120", name: "Accrued Expenses", type: "CurrentLiability" },
        "2121": { code: "2121", name: "Accrued Salaries", type: "CurrentLiability" },
        "2122": { code: "2122", name: "Accrued Interest", type: "CurrentLiability" },
        "2123": { code: "2123", name: "Accrued Taxes", type: "CurrentLiability" },
        "2130": { code: "2130", name: "Short-term Debt", type: "CurrentLiability" },
        "2131": { code: "2131", name: "Notes Payable", type: "CurrentLiability" },
        "2132": { code: "2132", name: "Line of Credit", type: "CurrentLiability" },
        "2140": { code: "2140", name: "Current Portion of Long-term Debt", type: "CurrentLiability" },
        "2150": { code: "2150", name: "Unearned Revenue", type: "CurrentLiability" },
        "2160": { code: "2160", name: "Payroll Liabilities", type: "CurrentLiability" },
        "2161": { code: "2161", name: "Federal Income Tax Withheld", type: "CurrentLiability" },
        "2162": { code: "2162", name: "State Income Tax Withheld", type: "CurrentLiability" },
        "2163": { code: "2163", name: "FICA Tax Payable", type: "CurrentLiability" },
        "2164": { code: "2164", name: "Unemployment Tax Payable", type: "CurrentLiability" },

        "2200": { code: "2200", name: "Non-Current Liabilities", type: "LongTermLiability" },
        "2210": { code: "2210", name: "Long-term Debt", type: "LongTermLiability" },
        "2211": { code: "2211", name: "Bank Loans", type: "LongTermLiability" },
        "2212": { code: "2212", name: "Mortgages Payable", type: "LongTermLiability" },
        "2213": { code: "2213", name: "Bonds Payable", type: "LongTermLiability" },
        "2220": { code: "2220", name: "Deferred Tax Liability", type: "LongTermLiability" },
        "2230": { code: "2230", name: "Other Long-term Liabilities", type: "OtherLiability" }
      }
    },

    "3000_EQUITY": {
      name: "Equity",
      type: "Equity",
      accounts: {
        "3100": { code: "3100", name: "Owner's Equity", type: "Equity" },
        "3110": { code: "3110", name: "Common Stock", type: "CommonStock" },
        "3120": { code: "3120", name: "Preferred Stock", type: "Equity" },
        "3130": { code: "3130", name: "Additional Paid-in Capital", type: "Equity" },
        "3140": { code: "3140", name: "Retained Earnings", type: "RetainedEarnings" },
        "3150": { code: "3150", name: "Treasury Stock", type: "Equity", contra: true },
        "3160": { code: "3160", name: "Owner's Draw", type: "Equity", contra: true },
        "3170": { code: "3170", name: "Accumulated Other Comprehensive Income", type: "OtherEquity" }
      }
    },

    "4000_REVENUE": {
      name: "Revenue",
      type: "Revenue",
      accounts: {
        "4100": { code: "4100", name: "Operating Revenue", type: "OperatingRevenue" },
        "4110": { code: "4110", name: "Sales Revenue", type: "OperatingRevenue" },
        "4111": { code: "4111", name: "Product Sales", type: "OperatingRevenue" },
        "4112": { code: "4112", name: "Service Revenue", type: "OperatingRevenue" },
        "4113": { code: "4113", name: "Consulting Revenue", type: "OperatingRevenue" },
        "4120": { code: "4120", name: "Sales Returns and Allowances", type: "OperatingRevenue", contra: true },
        "4130": { code: "4130", name: "Sales Discounts", type: "OperatingRevenue", contra: true },

        "4200": { code: "4200", name: "Other Revenue", type: "NonOperatingRevenue" },
        "4210": { code: "4210", name: "Interest Income", type: "NonOperatingRevenue" },
        "4220": { code: "4220", name: "Dividend Income", type: "NonOperatingRevenue" },
        "4230": { code: "4230", name: "Rental Income", type: "NonOperatingRevenue" },
        "4240": { code: "4240", name: "Gain on Sale of Assets", type: "NonOperatingRevenue" },
        "4250": { code: "4250", name: "Miscellaneous Income", type: "OtherRevenue" }
      }
    },

    "5000_EXPENSES": {
      name: "Expenses",
      type: "Expense",
      accounts: {
        "5100": { code: "5100", name: "Cost of Goods Sold", type: "OperatingExpense" },
        "5110": { code: "5110", name: "Materials", type: "OperatingExpense" },
        "5120": { code: "5120", name: "Direct Labor", type: "OperatingExpense" },
        "5130": { code: "5130", name: "Manufacturing Overhead", type: "OperatingExpense" },
        "5140": { code: "5140", name: "Freight In", type: "OperatingExpense" },

        "5200": { code: "5200", name: "Operating Expenses", type: "OperatingExpense" },
        "5210": { code: "5210", name: "Salaries and Wages", type: "OperatingExpense" },
        "5211": { code: "5211", name: "Executive Salaries", type: "OperatingExpense" },
        "5212": { code: "5212", name: "Office Salaries", type: "OperatingExpense" },
        "5213": { code: "5213", name: "Sales Salaries", type: "OperatingExpense" },
        "5220": { code: "5220", name: "Employee Benefits", type: "OperatingExpense" },
        "5221": { code: "5221", name: "Health Insurance", type: "OperatingExpense" },
        "5222": { code: "5222", name: "Retirement Plan", type: "OperatingExpense" },
        "5223": { code: "5223", name: "Payroll Taxes", type: "OperatingExpense" },
        "5230": { code: "5230", name: "Rent Expense", type: "OperatingExpense" },
        "5240": { code: "5240", name: "Utilities", type: "OperatingExpense" },
        "5241": { code: "5241", name: "Electricity", type: "OperatingExpense" },
        "5242": { code: "5242", name: "Water", type: "OperatingExpense" },
        "5243": { code: "5243", name: "Gas", type: "OperatingExpense" },
        "5244": { code: "5244", name: "Internet/Phone", type: "OperatingExpense" },
        "5250": { code: "5250", name: "Insurance", type: "OperatingExpense" },
        "5251": { code: "5251", name: "General Liability", type: "OperatingExpense" },
        "5252": { code: "5252", name: "Property Insurance", type: "OperatingExpense" },
        "5260": { code: "5260", name: "Depreciation Expense", type: "OperatingExpense" },
        "5270": { code: "5270", name: "Office Supplies", type: "OperatingExpense" },
        "5280": { code: "5280", name: "Marketing and Advertising", type: "OperatingExpense" },
        "5290": { code: "5290", name: "Professional Services", type: "OperatingExpense" },
        "5291": { code: "5291", name: "Legal Fees", type: "OperatingExpense" },
        "5292": { code: "5292", name: "Accounting Fees", type: "OperatingExpense" },
        "5293": { code: "5293", name: "Consulting Fees", type: "OperatingExpense" },
        "5300": { code: "5300", name: "Travel and Entertainment", type: "OperatingExpense" },
        "5310": { code: "5310", name: "Repairs and Maintenance", type: "OperatingExpense" },
        "5320": { code: "5320", name: "Bad Debt Expense", type: "OperatingExpense" },
        "5330": { code: "5330", name: "Bank Charges", type: "OperatingExpense" },
        "5340": { code: "5340", name: "Training and Development", type: "OperatingExpense" },

        "5400": { code: "5400", name: "Other Expenses", type: "NonOperatingExpense" },
        "5410": { code: "5410", name: "Interest Expense", type: "NonOperatingExpense" },
        "5420": { code: "5420", name: "Tax Expense", type: "NonOperatingExpense" },
        "5430": { code: "5430", name: "Loss on Sale of Assets", type: "NonOperatingExpense" },
        "5440": { code: "5440", name: "Miscellaneous Expense", type: "OtherExpense" }
      }
    }
  }
};

// Helper function to convert template to account creation format
export function convertTemplateToAccounts(template: ChartOfAccountsTemplate): Array<{
  code: string;
  name: string;
  type: AccountType;
  description?: string;
  isActive: boolean;
}> {
  const accounts: Array<{
    code: string;
    name: string;
    type: AccountType;
    description?: string;
    isActive: boolean;
  }> = [];

  Object.values(template.chart_of_accounts).forEach(category => {
    Object.values(category.accounts).forEach(account => {
      accounts.push({
        code: account.code,
        name: account.name,
        type: account.type,
        description: account.description,
        isActive: true
      });
    });
  });

  return accounts.sort((a, b) => a.code.localeCompare(b.code));
}
