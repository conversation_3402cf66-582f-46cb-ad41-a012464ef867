/**
 * Modal cleanup utilities to handle lingering modal overlays and restore UI interactions
 */

/**
 * Comprehensive modal cleanup function that removes lingering overlays and restores UI interactions
 */
export function cleanupModals(): void {
  // Force cleanup of any lingering modal overlays
  setTimeout(() => {
    // Remove Radix UI dialog overlays
    const overlays = document.querySelectorAll([
      '[data-radix-dialog-overlay]',
      '[data-radix-alert-dialog-overlay]',
      '[data-radix-sheet-overlay]',
      '[data-radix-popover-content]',
      '.fixed.inset-0.z-50', // Generic overlay selector
    ].join(', '));

    overlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    });

    // Remove any lingering content that might be blocking interactions
    const contents = document.querySelectorAll([
      '[data-radix-dialog-content]',
      '[data-radix-alert-dialog-content]',
      '[data-radix-sheet-content]',
    ].join(', '));

    contents.forEach(content => {
      // Only remove if not currently open
      if (content.parentNode && !content.closest('[data-state="open"]')) {
        content.parentNode.removeChild(content);
      }
    });

    // Re-enable body interactions
    document.body.style.pointerEvents = '';
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Remove overflow classes
    document.body.classList.remove('overflow-hidden', 'overflow-y-hidden');
    document.documentElement.classList.remove('overflow-hidden', 'overflow-y-hidden');

    // Remove any data attributes that might be blocking interactions
    document.body.removeAttribute('data-scroll-locked');
    document.documentElement.removeAttribute('data-scroll-locked');
    document.body.removeAttribute('data-radix-scroll-area-viewport');

    // Reset any transform styles that might interfere
    document.body.style.transform = '';
    document.documentElement.style.transform = '';

    console.log('Modal cleanup completed');
  }, 100);
}

/**
 * Enhanced modal cleanup with additional safety checks
 */
export function forceCleanupModals(): void {
  // Immediate cleanup
  cleanupModals();

  // Additional cleanup after a longer delay to catch any stubborn elements
  setTimeout(() => {
    // Force remove any elements with high z-index that might be blocking
    const highZIndexElements = document.querySelectorAll('*');
    highZIndexElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      const zIndex = parseInt(computedStyle.zIndex);

      // Check for elements with very high z-index that might be modal overlays
      if (zIndex >= 50 && zIndex <= 100) {
        const rect = element.getBoundingClientRect();
        // If it covers most of the screen, it's likely a modal overlay
        if (rect.width >= window.innerWidth * 0.8 && rect.height >= window.innerHeight * 0.8) {
          // Check if it's not a legitimate modal that should be open
          if (!element.closest('[data-state="open"]') &&
              !element.querySelector('[data-state="open"]')) {
            console.warn('Removing potential blocking overlay:', element);
            element.remove();
          }
        }
      }
    });

    // Final body style reset
    document.body.style.cssText = document.body.style.cssText
      .replace(/pointer-events:\s*[^;]+;?/g, '')
      .replace(/overflow:\s*[^;]+;?/g, '')
      .replace(/padding-right:\s*[^;]+;?/g, '');

    console.log('Force modal cleanup completed');
  }, 500);
}

/**
 * Hook for modal cleanup that can be used in React components
 */
export function useModalCleanup() {
  const cleanup = () => {
    cleanupModals();
  };

  const forceCleanup = () => {
    forceCleanupModals();
  };

  return { cleanup, forceCleanup };
}

/**
 * Event listener for escape key to force cleanup if modals get stuck
 */
export function setupGlobalModalCleanup(): () => void {
  const handleEscape = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && event.ctrlKey && event.shiftKey) {
      console.log('Emergency modal cleanup triggered (Ctrl+Shift+Escape)');
      forceCleanupModals();
    }
  };

  // Also add a click handler for emergency cleanup
  const handleEmergencyClick = (event: MouseEvent) => {
    // Triple click on body with Ctrl key for emergency cleanup
    if (event.ctrlKey && event.detail === 3 && event.target === document.body) {
      console.log('Emergency modal cleanup triggered (Ctrl+Triple-click)');
      forceCleanupModals();
    }
  };

  document.addEventListener('keydown', handleEscape);
  document.addEventListener('click', handleEmergencyClick);

  // Return cleanup function
  return () => {
    document.removeEventListener('keydown', handleEscape);
    document.removeEventListener('click', handleEmergencyClick);
  };
}

/**
 * Debug function to check for stuck modals
 */
export function debugModalState(): void {
  console.group('Modal Debug Information');

  const overlays = document.querySelectorAll([
    '[data-radix-dialog-overlay]',
    '[data-radix-alert-dialog-overlay]',
    '[data-radix-sheet-overlay]',
  ].join(', '));

  const contents = document.querySelectorAll([
    '[data-radix-dialog-content]',
    '[data-radix-alert-dialog-content]',
    '[data-radix-sheet-content]',
  ].join(', '));

  console.log('Found overlays:', overlays.length);
  console.log('Found contents:', contents.length);
  console.log('Body pointer-events:', document.body.style.pointerEvents);
  console.log('Body overflow:', document.body.style.overflow);
  console.log('Body classes:', document.body.className);

  overlays.forEach((overlay, index) => {
    console.log(`Overlay ${index}:`, overlay);
  });

  contents.forEach((content, index) => {
    console.log(`Content ${index}:`, content);
  });

  console.groupEnd();
}
