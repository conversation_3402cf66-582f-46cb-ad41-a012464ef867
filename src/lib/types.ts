// Main account types
export type AccountType =
  // Asset subtypes
  | 'Asset' | 'CurrentAsset' | 'FixedAsset' | 'IntangibleAsset' | 'OtherAsset'
  // Liability subtypes
  | 'Liability' | 'CurrentLiability' | 'LongTermLiability' | 'OtherLiability'
  // Equity subtypes
  | 'Equity' | 'CommonStock' | 'RetainedEarnings' | 'OtherEquity'
  // Revenue subtypes
  | 'Revenue' | 'OperatingRevenue' | 'NonOperatingRevenue' | 'OtherRevenue'
  // Expense subtypes
  | 'Expense' | 'OperatingExpense' | 'NonOperatingExpense' | 'OtherExpense';

// TODO: Refactor usage and remove this type (potentially replace with Prisma.ChartOfAccountGetPayload)
export interface Account {
  id: string;
  restaurant_id: string; // Assuming a multi-tenant setup later
  account_code: string;
  account_name: string;
  account_type: AccountType;
  description?: string; // Optional description
  is_active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface Vendor {
  id: string;
  restaurant_id?: string; // Assuming a multi-tenant setup later
  merchantId?: string; // Alternative to restaurant_id
  name: string;
  contact_person?: string; // Optional
  contactPerson?: string; // Alternative to contact_person
  email?: string;
  phone?: string; // Optional
  address?: string; // Optional
  created_at?: string; // ISO date string
  createdAt?: string; // Alternative to created_at
  updated_at?: string; // ISO date string
  updatedAt?: string; // Alternative to updated_at
}

export enum BillStatus {
  Draft = 'Draft',
  Open = 'Open',
  Paid = 'Paid',
  Void = 'Void',
}

export enum InvoiceStatus {
  Draft = 'Draft',
  Sent = 'Sent',
  PartiallyPaid = 'PartiallyPaid',
  Paid = 'Paid',
  Overdue = 'Overdue',
  Void = 'Void',
}

export enum RecurringFrequency {
  Weekly = 'Weekly',
  Monthly = 'Monthly',
  Quarterly = 'Quarterly',
  Biannually = 'Biannually',
  Annually = 'Annually',
  Custom = 'Custom',
  // Add these for backward compatibility with our API
  BiWeekly = 'Weekly', // Map BiWeekly to Weekly
  Yearly = 'Annually', // Map Yearly to Annually
}

export enum RecurringStatus {
  Active = 'Active',
  Paused = 'Paused',
  Completed = 'Completed',
}

export interface Bill {
  id: string; // UUID
  restaurant_id?: string; // Foreign key to Restaurant (assuming multi-tenant)
  merchantId?: string; // Alternative to restaurant_id
  vendor_id?: string; // Foreign key to Vendor
  vendorId?: string; // Alternative to vendor_id
  bill_number?: string; // Unique per vendor, usually
  billNumber?: string; // Alternative to bill_number
  bill_date?: string; // ISO date string
  billDate?: string; // Alternative to bill_date
  due_date?: string; // ISO date string
  dueDate?: string; // Alternative to due_date
  subtotal?: number | string; // Subtotal amount before tax
  taxAmount?: number | string; // Alternative to tax_amount
  tax_amount?: number | string; // Tax amount
  total_amount?: number | string; // Total amount of the bill
  totalAmount?: number | string; // Alternative to total_amount
  amount_due?: number | string; // Remaining amount due (can be less than total if partially paid)
  amountDue?: number | string; // Alternative to amount_due
  status: BillStatus;
  notes?: string; // Optional internal notes
  terms?: string; // Optional payment terms
  associated_journal_entry_id?: string; // Optional link to the JE created when bill is approved/paid
  associatedJournalEntryId?: string; // Alternative to associated_journal_entry_id
  expenseAccountId?: string | null; // For expense account association
  created_at?: string; // ISO date string
  createdAt?: string; // Alternative to created_at
  updated_at?: string; // ISO date string
  updatedAt?: string; // Alternative to updated_at

  // Include vendor data that might be nested in the API response
  vendor?: {
    name: string;
    email?: string | null;
  };
}

export interface BillPayment {
  id: string; // UUID
  bill_id: string; // Foreign key to Bill
  payment_date: string; // ISO date string
  amount_paid: number;
  payment_method: string; // e.g., 'Check', 'Credit Card', 'ACH'
  reference_number?: string; // Optional check number, transaction ID, etc.
  associated_journal_entry_id?: string; // Optional link to the JE created for the payment
  created_at: string; // ISO date string
}

export interface SalesOrder {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  external_order_id: string; // ID from the external system (POS, online ordering)
  order_details: any; // JSON blob containing line items, customer info, etc.
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  order_date: string; // ISO date string when the order was placed
  associated_journal_entry_id?: string; // Optional link to the JE created for this sale
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface Invoice {
  id: string; // UUID
  merchantId: string; // Foreign key to Merchant (multi-tenant)
  customerId: string; // Foreign key to Customer
  invoiceNumber: string; // Unique per merchant, usually sequential
  issueDate: string; // ISO date string
  dueDate: string; // ISO date string
  subtotal: string | number; // Sum of line items before tax
  taxAmount: string | number; // Total tax amount
  totalAmount: string | number; // Total invoice amount (subtotal + tax)
  amountDue: string | number; // Remaining amount due (can be less than total if partially paid)
  status: InvoiceStatus;
  notes?: string; // Optional internal notes
  terms?: string; // Optional payment terms
  associatedJournalEntryId?: string; // Optional link to the JE created when invoice is created
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string

  // Include customer data that's nested in the API response
  customer?: {
    name: string;
    email: string;
  };

  // For backward compatibility with existing code
  restaurant_id?: string;
  customer_id?: string;
  invoice_number?: string;
  issue_date?: string;
  due_date?: string;
  tax_amount?: number;
  total_amount?: number;
  amount_due?: number;
  associated_journal_entry_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface InvoiceItem {
  id: string; // UUID
  invoice_id: string; // Foreign key to Invoice
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate_id?: string; // Optional foreign key to TaxRate
  tax_rate?: number; // Optional tax rate percentage
  tax_amount: number; // Tax amount for this line item
  line_total: number; // Total for this line item (quantity * unit_price + tax_amount)
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface InvoicePayment {
  id: string; // UUID
  invoice_id: string; // Foreign key to Invoice
  payment_date: string; // ISO date string
  amount_paid: number;
  payment_method: string; // e.g., 'Check', 'Credit Card', 'ACH', 'Cash'
  reference_number?: string; // Optional check number, transaction ID, etc.
  associated_journal_entry_id?: string; // Optional link to the JE created for the payment
  created_at: string; // ISO date string
}

export interface RecurringInvoice {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant (assuming multi-tenant)
  customer_id: string; // Foreign key to Customer
  name: string; // Name for this recurring invoice template
  frequency: RecurringFrequency; // How often to generate invoices
  interval: number; // Used with frequency (e.g., every 2 months)
  start_date: string; // ISO date string - when to start generating invoices
  end_date?: string; // ISO date string - when to stop (optional)
  next_date: string; // ISO date string - when the next invoice will be generated
  days_due_after: number; // Number of days after generation that payment is due
  status: RecurringStatus;
  last_generated_date?: string; // ISO date string - when the last invoice was generated
  subtotal: number; // Sum of line items before tax
  tax_amount: number; // Total tax amount
  total_amount: number; // Total invoice amount (subtotal + tax)
  notes?: string; // Optional internal notes
  terms?: string; // Optional payment terms
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface RecurringInvoiceItem {
  id: string; // UUID
  recurring_invoice_id: string; // Foreign key to RecurringInvoice
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate_id?: string; // Optional foreign key to TaxRate
  tax_rate?: number; // Optional tax rate percentage
  tax_amount: number; // Tax amount for this line item
  line_total: number; // Total for this line item (quantity * unit_price + tax_amount)
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface InvoiceTemplate {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant (assuming multi-tenant)
  name: string; // Name for this template
  description?: string; // Optional description
  customer_id?: string; // Optional foreign key to Customer (if template is customer-specific)
  days_due_after: number; // Number of days after generation that payment is due
  subtotal: number; // Sum of line items before tax
  tax_amount: number; // Total tax amount
  total_amount: number; // Total invoice amount (subtotal + tax)
  notes?: string; // Optional internal notes
  terms?: string; // Optional payment terms
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface InvoiceTemplateItem {
  id: string; // UUID
  template_id: string; // Foreign key to InvoiceTemplate
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate_id?: string; // Optional foreign key to TaxRate
  tax_rate?: number; // Optional tax rate percentage
  tax_amount: number; // Tax amount for this line item
  line_total: number; // Total for this line item (quantity * unit_price + tax_amount)
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export enum EmailStatus {
  Sent = 'Sent',
  Failed = 'Failed',
  Scheduled = 'Scheduled',
}

export interface EmailTemplate {
  id: string; // UUID
  name: string;
  subject: string;
  body: string; // HTML content
  is_default: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface EmailLog {
  id: string; // UUID
  invoice_id: string; // Foreign key to Invoice
  customer_id: string; // Foreign key to Customer
  recipient_email: string;
  subject: string;
  body: string; // HTML content
  status: EmailStatus;
  error_message?: string; // Only populated if status is Failed
  sent_at: string; // ISO date string
  created_at: string; // ISO date string
}

export enum CreditNoteStatus {
  Draft = 'Draft',
  Open = 'Open',
  Closed = 'Closed',
  Void = 'Void'
}

export enum CreditNoteType {
  CustomerRefund = 'Customer Refund',
  SupplierRefund = 'Supplier Refund',
  WriteOff = 'Write-Off',
  ReturnCredit = 'Return Credit',
  Adjustment = 'Adjustment'
}

export interface CreditNote {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant (assuming multi-tenant)
  customer_id: string; // Foreign key to Customer
  credit_note_number: string; // e.g., CN-0001
  reference_invoice_id?: string; // Optional reference to the original invoice
  issue_date: string; // ISO date string
  status: CreditNoteStatus;
  type: CreditNoteType;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  remaining_credit: number; // Amount of credit still available to use
  notes?: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface CreditNoteItem {
  id: string; // UUID
  credit_note_id: string; // Foreign key to CreditNote
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate_id?: string; // Optional foreign key to TaxRate
  tax_rate?: number; // Optional tax rate percentage
  tax_amount: number;
  line_total: number;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface CreditApplication {
  id: string; // UUID
  credit_note_id: string; // Foreign key to CreditNote
  invoice_id: string; // Foreign key to Invoice
  amount_applied: number;
  application_date: string; // ISO date string
  created_at: string; // ISO date string
}

export interface CustomerCredit {
  id: string; // UUID
  customer_id: string; // Foreign key to Customer
  amount: number;
  description: string;
  issue_date: string; // ISO date string
  expiry_date?: string; // Optional ISO date string
  remaining_amount: number;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export enum PaymentReminderStatus {
  Scheduled = 'Scheduled',
  Sent = 'Sent',
  Cancelled = 'Cancelled'
}

export interface PaymentReminder {
  id: string; // UUID
  invoice_id: string; // Foreign key to Invoice
  customer_id: string; // Foreign key to Customer
  reminder_date: string; // ISO date string
  status: PaymentReminderStatus;
  email_template_id?: string; // Optional foreign key to EmailTemplate
  notes?: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface CustomerStatement {
  id: string; // UUID
  customer_id: string; // Foreign key to Customer
  start_date: string; // ISO date string
  end_date: string; // ISO date string
  opening_balance: number;
  closing_balance: number;
  total_invoiced: number;
  total_paid: number;
  total_credits: number;
  generated_at: string; // ISO date string
}

export interface StatementItem {
  id: string; // UUID
  statement_id: string; // Foreign key to CustomerStatement
  date: string; // ISO date string
  description: string;
  type: 'invoice' | 'payment' | 'credit' | 'adjustment';
  reference_id: string; // ID of the referenced document (invoice, payment, etc.)
  reference_number: string; // Number of the referenced document
  amount: number; // Positive for debits, negative for credits
  running_balance: number;
}


export interface TaxRate {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  name: string; // e.g., "Sales Tax", "VAT"
  rate: number; // Decimal representation, e.g., 0.08 for 8%
  description?: string; // Optional description
  is_active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}


export type JournalEntryLineType = 'Debit' | 'Credit';

export interface JournalEntry {
  id: string; // UUID
  merchantId: string; // Foreign key to Merchant (from Go backend)
  entryNumber: string; // Entry number (from Go backend)
  date: string; // ISO date string (from Go backend)
  description: string;
  reference?: string; // Optional reference (from Go backend)
  sourceType: string; // e.g., 'Manual', 'SalesOrder', 'Bill', 'BillPayment' (from Go backend)
  totalAmount: number; // Total amount (from Go backend)
  createdAt: string; // ISO date string (from Go backend)
  updatedAt: string; // ISO date string (from Go backend)
  lines?: JournalEntryLine[]; // Optional lines array (from Go backend)

  // Backward compatibility fields (snake_case)
  restaurant_id?: string; // Alias for merchantId
  entry_date?: string; // Alias for date
  source_type?: string; // Alias for sourceType
  source_id?: string; // Legacy field, not used in Go backend
  created_at?: string; // Alias for createdAt
  updated_at?: string; // Alias for updatedAt
}

export interface JournalEntryLine {
  id: string; // UUID
  journalEntryId: string; // Foreign key to JournalEntry (from Go backend)
  accountId: string; // Foreign key to Account (from Go backend)
  type: JournalEntryLineType; // 'Debit' or 'Credit' (from Go backend)
  amount: number; // Amount (from Go backend)
  description?: string; // Optional description (from Go backend)
  sortOrder: number; // Sort order (from Go backend)
  createdAt: string; // ISO date string (from Go backend)
  updatedAt: string; // ISO date string (from Go backend)

  // Computed fields for display
  debit: number; // Computed: amount if type === 'Debit', else 0
  credit: number; // Computed: amount if type === 'Credit', else 0

  // Account information (populated by backend joins)
  account_name?: string; // Account name for display
  account_code?: string; // Account code for display

  // Backward compatibility fields (snake_case)
  journal_entry_id?: string; // Alias for journalEntryId
  account_id?: string; // Alias for accountId
  created_at?: string; // Alias for createdAt
  updated_at?: string; // Alias for updatedAt
}


export interface ProfitLossReport {
  startDate?: string; // ISO date string or undefined if all time
  endDate?: string;   // ISO date string or undefined if all time
  totalRevenue: number;
  totalExpenses: number;
  netProfitLoss: number;
  generatedAt: string; // ISO date string
}

export interface BalanceSheetReport {
  asOfDate?: string; // ISO date string or undefined if current
  assets: { [accountName: string]: number }; // Key: Account Name, Value: Balance
  liabilities: { [accountName: string]: number };
  equity: { [accountName: string]: number };
  totalAssets: number;
  totalLiabilities: number;
  totalEquity: number;
  totalLiabilitiesAndEquity: number; // Should equal totalAssets
  generatedAt: string; // ISO date string
}


export enum AssetStatus {
  Active = 'Active',
  Disposed = 'Disposed',
  Sold = 'Sold',
}

export interface Asset {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  name: string;
  description?: string;
  purchase_date: string; // ISO date string
  purchase_cost: number;
  depreciation_method: string; // e.g., 'Straight-Line', 'Declining Balance'
  useful_life_months: number;
  salvage_value: number;
  asset_account_id: string; // Foreign key to CoA (e.g., Furniture & Fixtures)
  accumulated_depreciation_account_id: string; // Foreign key to CoA
  depreciation_expense_account_id: string; // Foreign key to CoA
  status: AssetStatus;
  disposal_date?: string; // ISO date string - Optional: Date asset was disposed
  disposal_proceeds?: number; // Optional: Amount received upon disposal
  last_depreciation_date?: string; // ISO date string - Optional: Date depreciation last ran until
  image_url?: string; // Optional: URL to an image representing the asset
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}


// --- Bank Reconciliation Types ---

export interface BankAccount {
  id: string; // UUID
  restaurant_id: string; // FK
  account_name: string;
  bank_name: string;
  account_number: string; // Used in the banking page
  account_number_masked?: string; // Optional for backward compatibility
  account_type?: string; // Used in the banking page
  currency?: string; // e.g., "USD", "EUR"
  linked_coa_account_id?: string; // FK to Account
  opening_balance: number; // Used in the banking page
  current_balance: number; // Used in the banking page
  notes?: string; // Used in the banking page
  is_active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export enum BankTransactionStatus {
  Unreconciled = 'Unreconciled',
  Reconciled = 'Reconciled',
  Adjusted = 'Adjusted',
}

export type BankTransactionType = 'Debit' | 'Credit';

export interface BankTransaction {
  id: string; // UUID
  bank_account_id: string; // FK to BankAccount
  transaction_date: string; // ISO date string
  description: string;
  foreign_currency?: string; // Currency of the transaction if different from base
  foreign_amount?: number; // Amount in the foreign currency
  exchange_rate?: number; // Rate used for conversion to base currency
  base_currency_amount: number; // Amount in the system's base currency
  type: BankTransactionType;
  status: BankTransactionStatus;
  adjustment_journal_entry_id?: string; // Optional FK to JournalEntry
  external_id?: string; // Optional: For OFX FITID, check numbers, etc.
  imported_at: string; // ISO date string
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export enum BankReconciliationStatus {
  InProgress = 'In Progress',
  Completed = 'Completed',
}

export interface BankReconciliation {
  id: string; // UUID
  bank_account_id: string; // FK to BankAccount
  statement_end_date: string; // ISO date string
  statement_opening_balance_base: number;
  statement_ending_balance_foreign?: number; // User input if foreign currency account
  statement_ending_balance_base: number; // User input or calculated
  calculated_book_balance_base: number;
  status: BankReconciliationStatus;
  reconciled_by_user_id?: string; // Optional FK to User
  reconciled_at?: string; // ISO date string (when completed)
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface BankTransactionMatch {
  id: string; // UUID
  bank_reconciliation_id: string; // FK to BankReconciliation
  bank_transaction_id: string; // FK to BankTransaction
  journal_entry_line_id: string; // FK to JournalEntryLine
  matched_at: string; // ISO date string
  matched_by_user_id?: string; // Optional FK to User
}



// --- Payroll Types ---

export enum EmploymentStatus {
  Active = 'Active',
  Inactive = 'Inactive',
}

export type PayFrequency = 'Weekly' | 'Bi-Weekly' | 'Monthly' | 'Semi-Monthly' | 'Annually';

export interface Employee {
  employee_id: string; // Using string UUID for consistency
  restaurant_id: string; // Foreign key to Restaurant
  employee_name: string;
  contact_info: string; // Simple string for now (e.g., email or phone)
  employment_status: EmploymentStatus;
  basic_pay_rate?: number; // Optional: Hourly rate or salary amount
  pay_frequency?: PayFrequency; // Optional: How often the employee is paid
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}



export enum PayrollRunStatus {
  Draft = 'Draft',
  Processed = 'Processed',
  Archived = 'Archived',
}

export interface PayrollRun {
  payroll_run_id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  pay_period_start_date: string; // ISO date string
  pay_period_end_date: string; // ISO date string
  payment_date: string; // ISO date string
  gross_wages: number; // Summary amount
  employee_taxes: number; // Summary amount (e.g., federal income tax, state income tax, FICA)
  employer_taxes: number; // Summary amount (e.g., FICA match, FUTA, SUTA)
  net_pay: number; // Calculated: gross_wages - employee_taxes
  status: PayrollRunStatus;
  journal_entry_id?: string; // Optional: Link to the JE created when processed
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}



// --- General Ledger Report Types ---

export interface GeneralLedgerReportLine {
  date: string; // ISO date string from JournalEntry
  description: string; // From JournalEntry
  sourceType: string; // From JournalEntry (e.g., 'Bill', 'SalesOrder')
  sourceId: string; // From JournalEntry
  debit: number | null; // Amount if debit, null otherwise
  credit: number | null; // Amount if credit, null otherwise
  balance: number; // Running balance for the account
}

export interface GeneralLedgerReport {
  accountName: string;
  accountCode: string;
  startDate?: string; // ISO date string or undefined
  endDate?: string; // ISO date string or undefined
  openingBalance: number; // For mock, this will be 0
  lines: GeneralLedgerReportLine[];
  closingBalance: number; // Final running balance
  generatedAt: string; // ISO date string
}



// --- Trial Balance Report Types ---

export interface TrialBalanceLine {
  accountCode: string;
  accountName: string;
  debit: number;
  credit: number;
}

export interface TrialBalanceReport {
  asOfDate?: string; // ISO date string or undefined if current
  lines: TrialBalanceLine[];
  totalDebit: number;
  totalCredit: number;
  generatedAt: string; // ISO date string
}



// --- A/P Aging Report Types ---

export interface APAgingBucket {
  label: 'Current' | '1-30 Days' | '31-60 Days' | '61-90 Days' | 'Over 90 Days';
  amount: number;
}

export interface APAgingReportLine {
  vendorName: string;
  billNumber: string;
  billDate: string; // ISO date string
  dueDate: string; // ISO date string
  totalAmount: number;
  amountDue: number; // The portion of totalAmount currently outstanding
  buckets: {
    current: number;
    days1_30: number;
    days31_60: number;
    days61_90: number;
    over90: number;
  };
}

export interface APAgingReport {
  asOfDate: string; // ISO date string
  lines: APAgingReportLine[];
  totals: {
    current: number;
    days1_30: number;
    days31_60: number;
    days61_90: number;
    over90: number;
    totalDue: number; // Sum of all buckets
  };
  generatedAt: string; // ISO date string
}

// --- A/R Aging Report Types ---

export interface ARAgingBucket {
  label: 'Current' | '1-30 Days' | '31-60 Days' | '61-90 Days' | 'Over 90 Days';
  amount: number;
}

export interface ARAgingReportLine {
  customerName: string;
  invoiceNumber: string;
  issueDate: string; // ISO date string
  dueDate: string; // ISO date string
  totalAmount: number;
  amountDue: number; // The portion of totalAmount currently outstanding
  buckets: {
    current: number;
    days1_30: number;
    days31_60: number;
    days61_90: number;
    over90: number;
  };
}

export interface ARAgingReport {
  asOfDate: string; // ISO date string
  lines: ARAgingReportLine[];
  totals: {
    current: number;
    days1_30: number;
    days31_60: number;
    days61_90: number;
    over90: number;
    totalDue: number; // Sum of all buckets
  };
  generatedAt: string; // ISO date string
}


// --- Inventory Types ---

export enum InventoryItemStatus {
  Active = 'Active',
  Inactive = 'Inactive',
}

export interface InventoryItem {
  id: string; // UUID
  merchantId: string; // Foreign key to Merchant
  name: string;
  description?: string | null;
  sku: string; // Stock Keeping Unit
  category?: string | null;
  unitOfMeasure: string; // e.g., 'each', 'lb', 'kg', 'case'
  unitCost: string | number;
  quantityOnHand: string | number;
  reorderPoint?: string | number | null;
  reorderQuantity?: string | number | null;
  vendorId?: string | null; // Optional link to preferred vendor
  assetAccountId: string; // Link to inventory asset account in CoA
  imageUrl?: string | null; // URL to the item's image
  status: InventoryItemStatus;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string

  // For backward compatibility with existing code
  restaurant_id?: string;
  unit_of_measure?: string;
  unit_cost?: number;
  quantity_on_hand?: number;
  reorder_point?: number;
  reorder_quantity?: number;
  vendor_id?: string;
  asset_account_id?: string;
  image_url?: string;
  created_at?: string;
  updated_at?: string;
}

export enum InventoryTransactionType {
  Purchase = 'Purchase',
  Sale = 'Sale',
  Adjustment = 'Adjustment',
  Transfer = 'Transfer',
  Waste = 'Waste',
}

export interface InventoryTransaction {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  inventory_item_id: string; // Foreign key to InventoryItem
  transaction_date: string; // ISO date string
  transaction_type: InventoryTransactionType;
  quantity: number; // Positive for additions, negative for reductions
  unit_cost?: number; // Optional: Cost per unit for this transaction
  total_cost: number; // Total cost impact of this transaction
  reference_type?: string; // e.g., 'Bill', 'SalesOrder', 'Manual'
  reference_id?: string; // ID of the reference document
  notes?: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

// --- Budget Types ---

export interface BudgetItem {
  id: string; // UUID
  branch_id: string; // Foreign key to Branch
  branchId?: string; // Alternative to branch_id
  account_id: string; // Foreign key to Account in CoA
  accountId?: string; // Alternative to account_id
  year: number;
  month: number; // 1-12
  amount: number;
  notes?: string;
  created_at: string; // ISO date string
  createdAt?: string; // Alternative to created_at
  updated_at: string; // ISO date string
  updatedAt?: string; // Alternative to updated_at
}

export interface BudgetReport {
  year: number;
  month?: number; // Optional: If specified, report is for a specific month
  accounts: {
    account_id: string;
    account_name: string;
    account_type: AccountType;
    budgeted: number;
    actual: number;
    variance: number; // Difference between budgeted and actual
    variance_percent: number; // Percentage difference
  }[];
  totals: {
    revenue: { budgeted: number; actual: number; variance: number; variance_percent: number };
    expenses: { budgeted: number; actual: number; variance: number; variance_percent: number };
    net: { budgeted: number; actual: number; variance: number; variance_percent: number };
  };
  generatedAt: string; // ISO date string
}

export interface BudgetTemplate {
  id: string; // UUID
  name: string;
  description?: string;
  isDefault: boolean;
  items: BudgetTemplateItem[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  itemCount?: number; // Optional: Used in list views
}

export interface BudgetTemplateItem {
  id: string; // UUID
  accountId: string; // Foreign key to Account in CoA
  amount: number;
  notes?: string;
}

// --- Cash Flow Forecast Types ---

export enum CashFlowItemType {
  Inflow = 'Inflow',
  Outflow = 'Outflow',
}

export enum CashFlowItemStatus {
  Projected = 'Projected',
  Confirmed = 'Confirmed',
  Completed = 'Completed',
}

export interface CashFlowItem {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  description: string;
  amount: number;
  type: CashFlowItemType;
  expected_date: string; // ISO date string
  status: CashFlowItemStatus;
  category?: string; // Optional categorization
  category_id?: string; // Reference to a CashFlowCategory
  reference_type?: string; // e.g., 'Bill', 'SalesOrder', 'Recurring'
  reference_id?: string; // ID of the reference document
  notes?: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  recurring_id?: string; // Optional reference to a recurring cash flow item
}

export interface RecurringCashFlowItem {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  description: string;
  amount: number;
  type: CashFlowItemType;
  frequency: RecurringFrequency;
  start_date: string; // ISO date string
  end_date?: string; // Optional ISO date string
  next_date: string; // ISO date string
  status: RecurringStatus;
  category?: string; // Optional categorization
  category_id?: string; // Reference to a CashFlowCategory
  notes?: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  is_active: boolean;
}

export interface CashFlowCategory {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  name: string;
  description?: string;
  type: CashFlowItemType | 'Both'; // Category can be for Inflow, Outflow, or Both
  parent_id?: string; // For hierarchical categories
  color?: string; // For visual representation
  is_active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface CashFlowForecastReport {
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  openingBalance: number;
  periods: {
    period: string; // e.g., "Week 1", "May 2025"
    startDate: string; // ISO date string
    endDate: string; // ISO date string
    inflows: number;
    outflows: number;
    netCashFlow: number;
    endingBalance: number;
  }[];
  totalInflows: number;
  totalOutflows: number;
  netCashFlow: number;
  closingBalance: number;
  generatedAt: string; // ISO date string
}

// --- Tax Reporting Types ---

export enum TaxReportType {
  Sales = 'Sales',
  Income = 'Income',
  Payroll = 'Payroll',
}

export interface TaxReport {
  id: string; // UUID
  restaurant_id: string; // Foreign key to Restaurant
  report_type: TaxReportType;
  start_date: string; // ISO date string
  end_date: string; // ISO date string
  total_taxable_amount: number;
  total_tax_amount: number;
  tax_rate_breakdown: {
    tax_rate_id: string;
    tax_rate_name: string;
    taxable_amount: number;
    tax_amount: number;
  }[];
  generated_at: string; // ISO date string
}

// --- Payroll Detail Types ---

export interface PayrollDetail {
  id: string; // UUID
  payroll_run_id: string; // Foreign key to PayrollRun
  employee_id: string; // Foreign key to Employee
  hours_worked?: number; // Optional: For hourly employees
  regular_pay: number;
  overtime_pay: number;
  bonus_pay: number;
  gross_pay: number; // Sum of regular, overtime, and bonus
  federal_tax: number;
  state_tax: number;
  fica_tax: number;
  other_deductions: number;
  net_pay: number; // Gross pay minus all deductions
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

// --- Collections Management Types ---

export enum CollectionCaseStatus {
  New = 'New',
  InProgress = 'In Progress',
  OnHold = 'On Hold',
  Resolved = 'Resolved',
  Closed = 'Closed'
}

export enum CollectionActivityType {
  Call = 'Call',
  Email = 'Email',
  Letter = 'Letter',
  Meeting = 'Meeting',
  Payment = 'Payment',
  Note = 'Note'
}

export interface CollectionCase {
  id: string;
  customer_id: string;
  assigned_to: string;
  status: CollectionCaseStatus;
  priority: 'Low' | 'Medium' | 'High';
  total_amount: number;
  amount_collected: number;
  amount_remaining: number;
  due_date: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface CollectionActivity {
  id: string;
  case_id: string;
  type: CollectionActivityType;
  date: string;
  notes: string;
  outcome: string;
  follow_up_date?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface CollectionTemplate {
  id: string;
  name: string;
  description: string;
  steps: CollectionTemplateStep[];
  created_at: string;
  updated_at: string;
}

export interface CollectionTemplateStep {
  id: string;
  template_id: string;
  step_number: number;
  days_after_due: number;
  activity_type: CollectionActivityType;
  description: string;
  email_template_id?: string;
  letter_template_id?: string;
}